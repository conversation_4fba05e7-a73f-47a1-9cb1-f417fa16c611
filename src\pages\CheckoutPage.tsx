import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm, SubmitHandler } from 'react-hook-form'
import { UserCircleIcon } from '@heroicons/react/24/outline'

// Payment method icons
import bkashIcon from '../assets/images/payment/Bkash.png'
import nagadIcon from '../assets/images/payment/Nagad.png'
import rocketIcon from '../assets/images/payment/Rocket.png'
import upayIcon from '../assets/images/payment/Upay.png'

type FormData = {
  name: string
  email: string
  phone: string
  address?: string
}

type PaymentMethod = 'bkash' | 'nagad' | 'rocket' | 'upay'

export default function CheckoutPage() {
  const location = useLocation()
  const navigate = useNavigate()
  const { plan, price } = location.state || { plan: 'Monthly', price: 199, currency: 'Taka' }

  // Get affiliate ID from URL query parameter if it exists
  const searchParams = new URLSearchParams(location.search)
  const urlAffiliateId = searchParams.get('ref')

  // Check for stored affiliate ID in localStorage
  const storedAffiliateId = localStorage.getItem('affiliateId')
  const affiliateExpiry = localStorage.getItem('affiliateExpiry')

  // Use URL affiliate ID first, then stored one if it's still valid
  let affiliateId = urlAffiliateId
  if (!affiliateId && storedAffiliateId && affiliateExpiry) {
    // Check if the stored affiliate ID hasn't expired
    if (parseInt(affiliateExpiry) > Date.now()) {
      affiliateId = storedAffiliateId
    } else {
      // Clear expired affiliate data
      localStorage.removeItem('affiliateId')
      localStorage.removeItem('affiliateExpiry')
    }
  }

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('bkash')
  const [transactionId, setTransactionId] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [copiedText, setCopiedText] = useState<string | null>(null)
  // We're using userInfo to determine authentication status, so we don't need a separate state
  // const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const { register, handleSubmit, formState: { errors }, setValue } = useForm<FormData>()

  // Function to copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopiedText(text)
        // Reset the copied state after 2 seconds
        setTimeout(() => setCopiedText(null), 2000)
      })
      .catch(err => {
        console.error('Failed to copy text: ', err)
      })
  }

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0)

    // Set page title
    document.title = 'Checkout - Meta Master'

    // Check if user is authenticated
    const checkAuth = async () => {
      setIsLoading(true)
      const token = localStorage.getItem('userToken')

      if (!token) {
        // Redirect to login page if not authenticated
        navigate('/user/login', {
          state: {
            returnTo: '/checkout',
            plan,
            price
          }
        })
        return
      }

      try {
        // Fetch user data
        const response = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('Authentication failed')
        }

        const data = await response.json()
        setUserInfo(data.user)
        // Authentication is determined by userInfo being set

        // Pre-fill form with user data
        setValue('name', data.user.firstName && data.user.lastName ? `${data.user.firstName} ${data.user.lastName}` : data.user.name || '')
        setValue('email', data.user.email)
        setValue('phone', data.user.phone || '')
        setValue('address', data.user.address || '')
      } catch (error) {
        console.error('Authentication error:', error)
        localStorage.removeItem('userToken')
        navigate('/user/login', {
          state: {
            returnTo: '/checkout',
            plan,
            price
          }
        })
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [navigate, plan, price, setValue])

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!transactionId) {
      alert('Please enter a transaction ID')
      return
    }

    setIsProcessing(true)

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login', {
          state: {
            returnTo: '/checkout',
            plan,
            price
          }
        })
        return
      }

      // Split name into firstName and lastName for backend compatibility
      let firstName = ''
      let lastName = ''
      if (data.name) {
        const nameParts = data.name.trim().split(' ')
        firstName = nameParts[0]
        lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : ''
      }

      // Send the transaction data to the backend API
      const response = await fetch('http://localhost:5001/api/payments/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          transactionId,
          paymentMethod,
          amount: price,
          plan,
          customerInfo: {
            name: data.name, // Include the full name
            firstName,
            lastName,
            email: data.email,
            phone: data.phone,
            address: data.address || ''
          },
          affiliateId: affiliateId || undefined
        })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Payment verification failed')
      }

      console.log('Payment processed successfully', result.data)

      // Redirect directly to user dashboard instead of thank you page
      // Pass state to indicate successful payment
      navigate('/user/dashboard', {
        state: {
          fromCheckout: true,
          orderId: result.data.orderId,
          plan: plan,
          licenseKey: result.data.licenseKey
        }
      })
    } catch (error) {
      console.error('Payment processing error:', error)
      alert('There was an error processing your payment. Please try again.')
      setIsProcessing(false)
    }
  }

  // Get merchant number based on payment method
  const getMerchantNumber = (method: PaymentMethod) => {
    switch (method) {
      case 'bkash':
        return '01637878155' // Replace with your actual bKash merchant number
      case 'nagad':
        return '01637878155' // Replace with your actual Nagad merchant number
      case 'rocket':
        return '01871822313' // Replace with your actual Rocket merchant number
      case 'upay':
        return '01637878155' // Replace with your actual Upay merchant number
      default:
        return '01637878155'
    }
  }

  // Get payment instructions based on payment method
  const getPaymentInstructions = (method: PaymentMethod) => {
    const number = getMerchantNumber(method)

    switch (method) {
      case 'bkash':
        return [
          `1. Open your bKash app and tap on "Send Money"`,
          `2. Enter the merchant number: ${number}`,
          `3. Enter the exact amount: ৳${price}`,
          `4. In the reference field, type: "Meta Master"`,
          `5. Double-check all information and confirm`,
          `6. Enter your bKash PIN to complete the payment`,
          `7. Save the Transaction ID from the confirmation SMS`,
          `8. Enter the Transaction ID in the field below`
        ]
      case 'nagad':
        return [
          `1. Open your Nagad app and tap on "Send Money"`,
          `2. Enter the merchant number: ${number}`,
          `3. Enter the exact amount: ৳${price}`,
          `4. In the reference field, type: "Meta Master"`,
          `5. Review the details and tap "Next"`,
          `6. Enter your Nagad PIN to authorize the payment`,
          `7. Save the Transaction ID from the confirmation screen`,
          `8. Enter the Transaction ID in the field below`
        ]
      case 'rocket':
        return [
          `1. Open your Rocket app and select "Fund Transfer"`,
          `2. Enter the merchant number: ${number}`,
          `3. Enter the exact amount: ৳${price}`,
          `4. Add "Meta Master" as the reference`,
          `5. Confirm the transaction details`,
          `6. Enter your Rocket PIN to complete the payment`,
          `7. Note down the Transaction ID from the confirmation`,
          `8. Enter the Transaction ID in the field below`
        ]
      case 'upay':
        return [
          `1. Open your Upay app and go to "Send Money"`,
          `2. Enter the merchant number: ${number}`,
          `3. Enter the exact amount: ৳${price}`,
          `4. Add "Meta Master" in the reference field`,
          `5. Review all details and confirm the transaction`,
          `6. Enter your Upay PIN to authorize the payment`,
          `7. Save the Transaction ID from the confirmation message`,
          `8. Enter the Transaction ID in the field below`
        ]
      default:
        return []
    }
  }

  // Function to render instruction with copy button for account number
  const renderInstructionWithCopy = (instruction: string) => {
    // Check if the instruction contains merchant number
    if (instruction.includes('Enter the merchant number:')) {
      const number = getMerchantNumber(paymentMethod)
      const parts = instruction.split(number)

      return (
        <span>
          {parts[0]}
          <span className="text-cyan-400">{number}</span>
          <button
            type="button"
            onClick={() => copyToClipboard(number)}
            className="inline-flex items-center justify-center p-1 ml-1 bg-cyan-500/10 hover:bg-cyan-500/20 rounded-md transition-colors"
            title="Copy to clipboard"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
            </svg>
          </button>
          {copiedText === number && (
            <span className="ml-1 text-xs text-green-400 animate-fade-in-out">
              Copied!
            </span>
          )}
          {parts[1]}
        </span>
      )
    }

    return instruction
  }

  // Show loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-[#051824] min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500 mb-4"></div>
          <p className="text-white text-lg">Loading...</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-[#051824] min-h-screen">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('/src/assets/images/grid-pattern.svg')] opacity-5"></div>

        {/* Radial gradient background */}
        <div className="absolute inset-0 bg-gradient-radial from-[#072a3a] to-[#051824]"></div>

        {/* Animated glow effects */}
        <motion.div
          className="absolute top-1/4 right-1/4 w-[600px] h-[600px] rounded-full bg-cyan-500/5 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />

        <motion.div
          className="absolute bottom-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-cyan-700/5 blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
      </div>

      <div className="container relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            Complete Your <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500">Purchase</span>
          </h1>
          <p className="text-xl text-gray-400">
            You're just a few steps away from unlocking Meta Master
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="bg-[#072a3a] backdrop-blur-sm border border-cyan-500/20 rounded-lg p-8 shadow-lg">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">Your Information</h2>
                {userInfo && (
                  <div className="flex items-center text-cyan-400">
                    <UserCircleIcon className="h-5 w-5 mr-1" />
                    <span>Logged in as {userInfo.name || (userInfo.firstName && userInfo.lastName ? `${userInfo.firstName} ${userInfo.lastName}` : userInfo.firstName || '')}</span>
                  </div>
                )}
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label htmlFor="name" className="block text-gray-300 mb-2">Name</label>
                    <input
                      type="text"
                      id="name"
                      className={`w-full bg-[#051824] border ${errors.name ? 'border-red-500' : 'border-cyan-500/30'} rounded-lg p-3 text-white focus:outline-none focus:border-cyan-500`}
                      placeholder="John Doe"
                      {...register('name', { required: 'Name is required' })}
                    />
                    {errors.name && <p className="mt-1 text-red-500 text-sm">{errors.name.message}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-gray-300 mb-2">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    className={`w-full bg-[#051824] border ${errors.email ? 'border-red-500' : 'border-cyan-500/30'} rounded-lg p-3 text-white focus:outline-none focus:border-cyan-500`}
                    placeholder="<EMAIL>"
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Invalid email address'
                      }
                    })}
                  />
                  {errors.email && <p className="mt-1 text-red-500 text-sm">{errors.email.message}</p>}
                </div>

                <div>
                  <label htmlFor="phone" className="block text-gray-300 mb-2">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    className={`w-full bg-[#051824] border ${errors.phone ? 'border-red-500' : 'border-cyan-500/30'} rounded-lg p-3 text-white focus:outline-none focus:border-cyan-500`}
                    placeholder="01XXXXXXXXX"
                    {...register('phone', {
                      required: 'Phone number is required',
                      pattern: {
                        value: /^01[3-9]\d{8}$/,
                        message: 'Please enter a valid Bangladesh phone number'
                      }
                    })}
                  />
                  {errors.phone && <p className="mt-1 text-red-500 text-sm">{errors.phone.message}</p>}
                </div>

                <div>
                  <label htmlFor="address" className="block text-gray-300 mb-2">Address (Optional)</label>
                  <textarea
                    id="address"
                    className="w-full bg-[#051824] border border-cyan-500/30 rounded-lg p-3 text-white focus:outline-none focus:border-cyan-500"
                    placeholder="Your address"
                    rows={3}
                    {...register('address')}
                  ></textarea>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-4 text-white">Payment Method</h3>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <motion.button
                      type="button"
                      onClick={() => setPaymentMethod('bkash')}
                      className={`flex flex-col items-center justify-center p-4 rounded-lg border transition-all ${
                        paymentMethod === 'bkash'
                          ? 'bg-[#e2136e]/10 border-[#e2136e] shadow-[0_0_15px_rgba(226,19,110,0.3)]'
                          : 'bg-[#051824] border-cyan-500/30 hover:border-cyan-500/50'
                      }`}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <img src={bkashIcon} alt="bKash" className="h-12 mb-2" />
                      <span className={`text-sm font-medium ${paymentMethod === 'bkash' ? 'text-[#e2136e]' : 'text-white'}`}>bKash</span>
                    </motion.button>

                    <motion.button
                      type="button"
                      onClick={() => setPaymentMethod('nagad')}
                      className={`flex flex-col items-center justify-center p-4 rounded-lg border transition-all ${
                        paymentMethod === 'nagad'
                          ? 'bg-[#f6921e]/10 border-[#f6921e] shadow-[0_0_15px_rgba(246,146,30,0.3)]'
                          : 'bg-[#051824] border-cyan-500/30 hover:border-cyan-500/50'
                      }`}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <img src={nagadIcon} alt="Nagad" className="h-12 mb-2" />
                      <span className={`text-sm font-medium ${paymentMethod === 'nagad' ? 'text-[#f6921e]' : 'text-white'}`}>Nagad</span>
                    </motion.button>

                    <motion.button
                      type="button"
                      onClick={() => setPaymentMethod('rocket')}
                      className={`flex flex-col items-center justify-center p-4 rounded-lg border transition-all ${
                        paymentMethod === 'rocket'
                          ? 'bg-[#8c3494]/10 border-[#8c3494] shadow-[0_0_15px_rgba(140,52,148,0.3)]'
                          : 'bg-[#051824] border-cyan-500/30 hover:border-cyan-500/50'
                      }`}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <img src={rocketIcon} alt="Rocket" className="h-12 mb-2" />
                      <span className={`text-sm font-medium ${paymentMethod === 'rocket' ? 'text-[#8c3494]' : 'text-white'}`}>Rocket</span>
                    </motion.button>

                    <motion.button
                      type="button"
                      onClick={() => setPaymentMethod('upay')}
                      className={`flex flex-col items-center justify-center p-4 rounded-lg border transition-all ${
                        paymentMethod === 'upay'
                          ? 'bg-[#df2029]/10 border-[#df2029] shadow-[0_0_15px_rgba(223,32,41,0.3)]'
                          : 'bg-[#051824] border-cyan-500/30 hover:border-cyan-500/50'
                      }`}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <img src={upayIcon} alt="Upay" className="h-12 mb-2" />
                      <span className={`text-sm font-medium ${paymentMethod === 'upay' ? 'text-[#df2029]' : 'text-white'}`}>Upay</span>
                    </motion.button>
                  </div>

                  <div className="bg-[#051824] border border-cyan-500/30 rounded-lg p-6 mb-6">
                    <div className="flex items-center mb-4">
                      <div className="bg-cyan-500/20 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h4 className="text-lg font-medium text-white">Payment Instructions</h4>
                    </div>

                    <div className="bg-[#072a3a]/50 rounded-lg p-4 mb-4">
                      <div className="flex items-center mb-2">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                          paymentMethod === 'bkash' ? 'bg-[#e2136e]/20 text-[#e2136e]' :
                          paymentMethod === 'nagad' ? 'bg-[#f6921e]/20 text-[#f6921e]' :
                          paymentMethod === 'rocket' ? 'bg-[#8c3494]/20 text-[#8c3494]' :
                          'bg-[#df2029]/20 text-[#df2029]'
                        }`}>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <span className="text-white font-medium">Send exactly: <span className="text-cyan-400">৳{price}</span></span>
                      </div>
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                          paymentMethod === 'bkash' ? 'bg-[#e2136e]/20 text-[#e2136e]' :
                          paymentMethod === 'nagad' ? 'bg-[#f6921e]/20 text-[#f6921e]' :
                          paymentMethod === 'rocket' ? 'bg-[#8c3494]/20 text-[#8c3494]' :
                          'bg-[#df2029]/20 text-[#df2029]'
                        }`}>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <span className="text-white font-medium">To number:
                          <span className="text-cyan-400 mx-1">{getMerchantNumber(paymentMethod)}</span>
                          <button
                            type="button"
                            onClick={() => copyToClipboard(getMerchantNumber(paymentMethod))}
                            className="inline-flex items-center justify-center p-1 ml-1 bg-cyan-500/10 hover:bg-cyan-500/20 rounded-md transition-colors"
                            title="Copy to clipboard"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                          </button>
                          {copiedText === getMerchantNumber(paymentMethod) && (
                            <span className="ml-2 text-xs text-green-400 animate-fade-in-out">
                              Copied!
                            </span>
                          )}
                        </span>
                      </div>
                    </div>

                    <ol className="space-y-2 text-gray-300">
                      {getPaymentInstructions(paymentMethod).map((instruction, index) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2">{renderInstructionWithCopy(instruction)}</span>
                        </li>
                      ))}
                    </ol>

                    <div className="mt-6">
                      <label htmlFor="transactionId" className="block text-gray-300 mb-2 font-medium">
                        Transaction ID <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          id="transactionId"
                          className={`w-full bg-[#072a3a] border ${!transactionId && isProcessing ? 'border-red-500' : 'border-cyan-500/30'} rounded-lg p-3 text-white focus:outline-none focus:border-cyan-500`}
                          placeholder="Enter your transaction ID from the payment confirmation"
                          value={transactionId}
                          onChange={(e) => setTransactionId(e.target.value)}
                          required
                        />
                        {!transactionId && isProcessing && (
                          <p className="mt-1 text-red-500 text-sm">Transaction ID is required to complete your purchase</p>
                        )}
                        <p className="mt-2 text-xs text-cyan-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          The Transaction ID is a unique code you received after completing the payment
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <motion.button
                  type="submit"
                  className="w-full py-4 px-6 bg-gradient-to-r from-cyan-600 to-blue-700 hover:from-cyan-700 hover:to-blue-800 text-white font-bold rounded-lg shadow-lg flex items-center justify-center"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Complete Purchase'
                  )}
                </motion.button>
              </form>
            </div>
          </motion.div>

          {/* Order Summary */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="bg-[#072a3a] backdrop-blur-sm border border-cyan-500/20 rounded-lg shadow-lg sticky top-24">
              <div className="p-6 border-b border-cyan-500/20">
                <h2 className="text-2xl font-bold mb-2 text-white flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Order Summary
                </h2>
                <p className="text-gray-400">Review your order details</p>
              </div>

              <div className="p-6 border-b border-cyan-500/20">
                <div className="flex justify-between mb-4">
                  <span className="text-gray-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                    </svg>
                    Plan
                  </span>
                  <span className="text-white font-medium">{plan}</span>
                </div>

                <div className="flex justify-between mb-4">
                  <span className="text-gray-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Duration
                  </span>
                  <span className="text-white font-medium">
                    {plan === 'Monthly' ? '1 Month' : plan === 'Yearly' ? '1 Year' : 'Lifetime'}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Licenses
                  </span>
                  <span className="text-white font-medium">
                    {plan === 'Lifetime' ? '2 Devices' : '1 Device'}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                    Subtotal
                  </span>
                  <span className="text-white">৳{price}</span>
                </div>

                <div className="flex justify-between mb-4">
                  <span className="text-gray-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" />
                    </svg>
                    Tax
                  </span>
                  <span className="text-white">Included</span>
                </div>

                <div className="flex justify-between items-center pt-4 border-t border-cyan-500/20">
                  <span className="text-lg text-white font-bold flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Total
                  </span>
                  <span className="text-2xl text-cyan-400 font-bold">৳{price}</span>
                </div>
              </div>

              <div className="p-6 bg-[#051824] rounded-b-lg">
                <div className="flex items-center text-gray-400 mb-3">
                  <div className="bg-cyan-500/10 p-1.5 rounded-full mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-cyan-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <span>Secure Payment</span>
                </div>
                <div className="flex items-center text-gray-400">
                  <div className="bg-cyan-500/10 p-1.5 rounded-full mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-cyan-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" />
                    </svg>
                  </div>
                  <span>30-Day Money-Back Guarantee</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

