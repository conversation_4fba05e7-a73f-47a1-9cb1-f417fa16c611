import { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import {
  HomeIcon,
  KeyIcon,
  CreditCardIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  AcademicCapIcon,
  UserGroupIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline'

export default function UserLayout({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('userToken')
    if (!token) {
      navigate('/user/login')
      return
    }

    // Verify token validity by making a request to the API
    const verifyToken = async () => {
      try {
        const response = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bear<PERSON> ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('Invalid token')
        }

        setIsAuthenticated(true)
      } catch (error) {
        console.error('Authentication error:', error)
        localStorage.removeItem('userToken')
        navigate('/user/login')
      } finally {
        setIsLoading(false)
      }
    }

    verifyToken()
  }, [navigate])

  const handleLogout = () => {
    localStorage.removeItem('userToken')
    navigate('/user/login')
  }

  const navigation = [
    { name: 'Dashboard', href: '/user/dashboard', icon: HomeIcon },
    { name: 'License Keys', href: '/user/licenses', icon: KeyIcon },
    { name: 'Payment History', href: '/user/payments', icon: CreditCardIcon },
    { name: 'Affiliate Program', href: '/user/affiliate', icon: UserGroupIcon },
    { name: 'Withdraw Earnings', href: '/user/withdrawal', icon: BanknotesIcon },
    { name: 'Tutorials', href: '/user/tutorials', icon: AcademicCapIcon },
    { name: 'Profile', href: '/user/profile', icon: UserCircleIcon },
    { name: 'Settings', href: '/user/settings', icon: Cog6ToothIcon }
  ]

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-dark">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect in useEffect
  }

  return (
    <div className="flex min-h-screen bg-dark">
      {/* Sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col">
        <div className="flex flex-col flex-grow bg-dark-light/30 backdrop-blur-sm border-r border-white/5 pt-5 pb-4 overflow-y-auto">
          <div className="flex items-center flex-shrink-0 px-4">
            <Link to="/" className="text-xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Meta Master
            </Link>
          </div>
          <div className="mt-8 flex-1 flex flex-col">
            <nav className="flex-1 px-2 space-y-1">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-4 py-3 text-sm font-medium rounded-lg ${
                      isActive
                        ? 'bg-primary/10 text-primary'
                        : 'text-text hover:bg-dark-light hover:text-light'
                    }`}
                  >
                    <item.icon
                      className={`mr-3 h-5 w-5 ${
                        isActive ? 'text-primary' : 'text-text group-hover:text-light'
                      }`}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                )
              })}
            </nav>
          </div>
          <div className="px-2 mt-6 mb-4">
            <button
              onClick={handleLogout}
              className="group flex items-center px-4 py-3 text-sm font-medium rounded-lg text-text hover:bg-dark-light hover:text-light w-full"
            >
              <ArrowRightOnRectangleIcon
                className="mr-3 h-5 w-5 text-text group-hover:text-light"
                aria-hidden="true"
              />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Mobile header */}
      <div className="md:hidden bg-dark-light/30 backdrop-blur-sm border-b border-white/5 p-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="text-xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            Meta Master
          </Link>
          <div className="flex space-x-4">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`p-2 rounded-lg ${
                    isActive
                      ? 'bg-primary/10 text-primary'
                      : 'text-text hover:bg-dark-light hover:text-light'
                  }`}
                >
                  <item.icon className="h-6 w-6" aria-hidden="true" />
                </Link>
              )
            })}
            <button
              onClick={handleLogout}
              className="p-2 rounded-lg text-text hover:bg-dark-light hover:text-light"
            >
              <ArrowRightOnRectangleIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1">
        <main className="flex-1 overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
