@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  /* Dark mode only */
  body {
    @apply bg-gradient-to-b from-dark-950 to-dark-900 text-white min-h-screen;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display text-white;
  }

  p {
    @apply text-dark-300;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-600 focus:ring-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-600 focus:ring-secondary;
  }

  .btn-accent {
    @apply bg-accent text-white hover:bg-accent-600 focus:ring-accent;
  }

  .btn-outline {
    @apply border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white focus:ring-primary;
  }

  .section {
    @apply py-16 md:py-24;
  }

  .section-header {
    @apply text-center mb-12 md:mb-16;
  }

  .section-header h2 {
    @apply text-3xl md:text-4xl font-bold mb-4;
  }

  .section-header p {
    @apply text-lg text-dark-400 max-w-3xl mx-auto;
  }

  /* Card styles */
  .card {
    @apply bg-dark-800/50 backdrop-blur-sm rounded-xl p-6 border border-dark-700 shadow-lg transition-all duration-300;
  }

  .card-hover {
    @apply hover:border-primary/50 hover:shadow-primary/20 hover:shadow-xl;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary via-secondary to-accent;
  }

  .glass-effect {
    @apply bg-white/5 backdrop-blur-lg border border-white/10 shadow-xl;
  }
}

/* Custom animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Glow effect */
.glow {
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
  }
}

/* Fade in-out animation for copy feedback */
.animate-fade-in-out {
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
