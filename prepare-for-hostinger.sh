#!/bin/bash

echo "==================================================="
echo "Meta Master Website - Prepare for Hostinger Upload"
echo "==================================================="
echo

echo "Building frontend for production..."
npm run build
echo

echo "Creating deployment directories..."
mkdir -p hostinger-upload/frontend
mkdir -p hostinger-upload/backend
echo

echo "Copying frontend files..."
cp -r dist/* hostinger-upload/frontend/
echo

echo "Copying backend files..."
cp -r server/* hostinger-upload/backend/
rm -f hostinger-upload/backend/package.json
mv hostinger-upload/backend/package.json.production hostinger-upload/backend/package.json
rm -f hostinger-upload/backend/.env
mv hostinger-upload/backend/.env.production hostinger-upload/backend/.env
echo

echo "Copying deployment instructions..."
cp DEPLOYMENT.md hostinger-upload/
echo

echo "Creating README file..."
cat > hostinger-upload/README.md << EOL
# Meta Master Website Files for Hostinger

This package contains all the files needed to deploy the Meta Master website to Hostinger.

- frontend/: Contains the React frontend files to be uploaded to the main domain
- backend/: Contains the Node.js backend files to be uploaded to the API subdomain
- DEPLOYMENT.md: Detailed deployment instructions
EOL
echo

echo "Creating ZIP archive..."
zip -r meta-master-hostinger.zip hostinger-upload
echo

echo "Done! Files are ready in the 'hostinger-upload' directory and 'meta-master-hostinger.zip' archive."
echo
