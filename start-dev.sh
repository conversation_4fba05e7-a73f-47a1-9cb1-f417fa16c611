#!/bin/bash

echo "Starting Meta Master development environment..."

echo "Starting backend server..."
cd server && npm run dev &
SERVER_PID=$!

echo "Starting frontend server..."
cd .. && npm run dev &
CLIENT_PID=$!

echo "Development servers started!"
echo "Backend: http://localhost:5000"
echo "Frontend: http://localhost:5173"

# Handle Ctrl+C to kill both processes
trap "kill $SERVER_PID $CLIENT_PID; exit" INT
wait
