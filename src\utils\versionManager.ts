/**
 * Version Management Utility
 * Manages the current software version displayed on the website
 */

interface VersionConfig {
  version: string;
  releaseDate: string;
  isLatest: boolean;
  showInHero: boolean;
  showInFloatingElement: boolean;
  showInFeatures: boolean;
}

const DEFAULT_VERSION_CONFIG: VersionConfig = {
  version: '5.3.0',
  releaseDate: '2024-12-20',
  isLatest: true,
  showInHero: true,
  showInFloatingElement: true,
  showInFeatures: false
};

const VERSION_STORAGE_KEY = 'metamaster_version_config';

/**
 * Get the current version configuration
 */
export function getVersionConfig(): VersionConfig {
  try {
    const stored = localStorage.getItem(VERSION_STORAGE_KEY);
    if (stored) {
      return { ...DEFAULT_VERSION_CONFIG, ...JSON.parse(stored) };
    }
  } catch (error) {
    console.error('Error loading version config:', error);
  }
  return DEFAULT_VERSION_CONFIG;
}

/**
 * Save version configuration
 */
export function saveVersionConfig(config: Partial<VersionConfig>): void {
  try {
    const currentConfig = getVersionConfig();
    const newConfig = { ...currentConfig, ...config };
    localStorage.setItem(VERSION_STORAGE_KEY, JSON.stringify(newConfig));
    
    // Trigger a custom event to notify components of version change
    window.dispatchEvent(new CustomEvent('versionConfigChanged', { 
      detail: newConfig 
    }));
  } catch (error) {
    console.error('Error saving version config:', error);
  }
}

/**
 * Get the current version string
 */
export function getCurrentVersion(): string {
  return getVersionConfig().version;
}

/**
 * Get formatted version for display
 */
export function getFormattedVersion(): string {
  const config = getVersionConfig();
  return `v${config.version}`;
}

/**
 * Check if version should be shown in a specific location
 */
export function shouldShowVersionIn(location: 'hero' | 'floating' | 'features'): boolean {
  const config = getVersionConfig();
  switch (location) {
    case 'hero':
      return config.showInHero;
    case 'floating':
      return config.showInFloatingElement;
    case 'features':
      return config.showInFeatures;
    default:
      return false;
  }
}

/**
 * Get version badge text
 */
export function getVersionBadgeText(): string {
  const config = getVersionConfig();
  if (config.isLatest) {
    return `Version ${config.version} Now Available`;
  }
  return `Version ${config.version}`;
}

/**
 * Update version number
 */
export function updateVersion(newVersion: string): void {
  saveVersionConfig({ 
    version: newVersion,
    releaseDate: new Date().toISOString().split('T')[0]
  });
}

/**
 * Toggle version display in specific locations
 */
export function toggleVersionDisplay(location: 'hero' | 'floating' | 'features', show: boolean): void {
  const updates: Partial<VersionConfig> = {};
  
  switch (location) {
    case 'hero':
      updates.showInHero = show;
      break;
    case 'floating':
      updates.showInFloatingElement = show;
      break;
    case 'features':
      updates.showInFeatures = show;
      break;
  }
  
  saveVersionConfig(updates);
}

/**
 * Reset to default version configuration
 */
export function resetVersionConfig(): void {
  localStorage.removeItem(VERSION_STORAGE_KEY);
  window.dispatchEvent(new CustomEvent('versionConfigChanged', { 
    detail: DEFAULT_VERSION_CONFIG 
  }));
}

/**
 * Get version history (mock data for now)
 */
export function getVersionHistory(): Array<{version: string, date: string, changes: string[]}> {
  return [
    {
      version: '5.3.0',
      date: '2024-12-20',
      changes: [
        'Added EPS version selection',
        'Enhanced vector file embedding',
        'Improved prompt generator mode',
        'Added multi-API key management'
      ]
    },
    {
      version: '5.2.0',
      date: '2024-11-15',
      changes: [
        'Google Gemini 2.5 integration',
        'Transparent PNG processing',
        'Batch processing improvements',
        'CSV export enhancements'
      ]
    },
    {
      version: '5.1.0',
      date: '2024-10-10',
      changes: [
        'AI metadata generation',
        'Multi-format support',
        'Custom keywords feature',
        'Performance optimizations'
      ]
    }
  ];
}
