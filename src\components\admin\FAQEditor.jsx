import React, { useState } from 'react';
import { 
  PencilIcon, 
  TrashIcon
} from '@heroicons/react/24/outline';

const FAQEditor = ({ faq, index, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedFAQ, setEditedFAQ] = useState({ ...faq });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEditedFAQ({
      ...editedFAQ,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSave = () => {
    onUpdate(index, editedFAQ);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedFAQ({ ...faq });
    setIsEditing(false);
  };

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            FAQ #{index + 1}
          </h3>
        </div>
        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <PencilIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => onDelete(index)}
                className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleSave}
                className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
            </>
          )}
        </div>
      </div>
      {isEditing ? (
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div className="sm:col-span-6">
              <label htmlFor={`question-${index}`} className="block text-sm font-medium text-gray-700">
                Question
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="question"
                  id={`question-${index}`}
                  value={editedFAQ.question}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="sm:col-span-6">
              <label htmlFor={`answer-${index}`} className="block text-sm font-medium text-gray-700">
                Answer
              </label>
              <div className="mt-1">
                <textarea
                  id={`answer-${index}`}
                  name="answer"
                  rows={4}
                  value={editedFAQ.answer}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label htmlFor={`order-${index}`} className="block text-sm font-medium text-gray-700">
                Display Order
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  name="order"
                  id={`order-${index}`}
                  min="1"
                  value={editedFAQ.order}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <div className="flex items-center h-full">
                <input
                  id={`isActive-${index}`}
                  name="isActive"
                  type="checkbox"
                  checked={editedFAQ.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor={`isActive-${index}`} className="ml-2 block text-sm text-gray-900">
                  Active (visible on website)
                </label>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Question</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{faq.question}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Answer</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 whitespace-pre-line">{faq.answer}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  faq.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {faq.isActive ? 'Active' : 'Inactive'}
                </span>
                <span className="ml-3 text-gray-500">Order: {faq.order}</span>
              </dd>
            </div>
          </dl>
        </div>
      )}
    </div>
  );
};

export default FAQEditor;
