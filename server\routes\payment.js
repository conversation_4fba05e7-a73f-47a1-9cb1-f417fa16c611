const express = require('express');
const router = express.Router();
const Payment = require('../models/mysql/Payment');
const User = require('../models/mysql/User');
const AffiliateTransaction = require('../models/mysql/Affiliate');
const { authenticate, isAdmin } = require('../middleware/auth');
const nodemailer = require('nodemailer');
const { query } = require('../config/db');
require('dotenv').config();

// Configure email transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
});

/**
 * Send license key to customer via email
 * @param {Object} payment - Payment object with customer info and license key
 * @returns {Promise} - Email sending result
 */
const sendLicenseKeyEmail = async (payment) => {
  try {
    const { customerInfo, licenseKey, plan, orderId } = payment;

    // Check if customerInfo exists and has email
    if (!customerInfo || !customerInfo.email) {
      console.error('Cannot send license key email: Missing customer email');
      throw new Error('Missing customer email');
    }

    // Get customer name or use a default
    const customerName = customerInfo.name ||
                        (customerInfo.firstName || customerInfo.lastName ?
                         `${customerInfo.firstName || ''} ${customerInfo.lastName || ''}`.trim() :
                         'Valued Customer');

    const mailOptions = {
      from: `"Meta Master" <${process.env.EMAIL_FROM}>`,
      to: customerInfo.email,
      subject: 'Your Meta Master License Key',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333; text-align: center;">Thank You for Your Purchase!</h2>
          <p>Hello ${customerName},</p>
          <p>Your payment for Meta Master ${plan} plan has been verified and approved.</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center;">
            <h3 style="margin-top: 0;">Your License Key:</h3>
            <p style="font-family: monospace; font-size: 18px; background-color: #fff; padding: 10px; border-radius: 3px;">${licenseKey}</p>
          </div>
          <p>Order Details:</p>
          <ul>
            <li>Order ID: ${orderId}</li>
            <li>Plan: ${plan}</li>
          </ul>
          <p>To activate your software:</p>
          <ol>
            <li>Open Meta Master application</li>
            <li>Click on "Activate License"</li>
            <li>Enter your license key</li>
            <li>Click "Activate"</li>
          </ol>
          <p>If you have any questions or need assistance, please contact our support team.</p>
          <p>Thank you for choosing Meta Master!</p>
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>© ${new Date().getFullYear()} Meta Master. All rights reserved.</p>
          </div>
        </div>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('License key email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending license key email:', error);
    throw error;
  }
};

/**
 * @route   POST /api/payments/verify
 * @desc    Verify and process a payment
 * @access  Public
 */
router.post('/verify', authenticate, async (req, res) => {
  try {
    const { transactionId, paymentMethod, amount, plan, customerInfo, affiliateId } = req.body;

    // Validate required fields
    if (!transactionId || !paymentMethod || !amount || !plan || !customerInfo) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Validate customer info
    if ((!customerInfo.name && (!customerInfo.firstName || !customerInfo.lastName)) || !customerInfo.email || !customerInfo.phone) {
      return res.status(400).json({
        success: false,
        message: 'Missing required customer information'
      });
    }

    // Generate order ID with format: MM-YEAR-RANDOMNUMBER
    const orderId = `MM-${new Date().getFullYear()}-${Math.floor(100000 + Math.random() * 900000)}`;

    // Get user from token
    const userId = req.user.id;

    // Check if an affiliate ID was provided
    let referrer = null;
    if (affiliateId) {
      // First try to find the referrer by username (new method)
      referrer = await User.findOne({ username: affiliateId });

      // If not found by username, try to find by affiliateId (backward compatibility)
      if (!referrer) {
        referrer = await User.findOne({ affiliateId });
      }

      // Make sure the referrer is not the same as the current user
      if (referrer && referrer.id === userId) {
        referrer = null; // Can't refer yourself
      }
    }

    // Create payment object for response
    const payment = {
      orderId,
      transactionId,
      paymentMethod,
      amount,
      plan,
      userId,
      referredBy: referrer ? referrer.id : null,
      customerInfo,
      status: 'pending' // Initial status is pending
    };

    // In a real-world scenario, you would verify the transaction with the payment provider here
    // For demonstration purposes, we'll simulate a successful verification

    // Create the payment record in the database
    const savedPayment = await Payment.create({
      orderId,
      transactionId,
      paymentMethod,
      amount,
      plan,
      userId,
      referredBy: referrer ? referrer.id : null,
      status: 'pending',
      customerInfo // Include customer information
    });

    // Add a pending payment record to user's account without a license key
    const user = await User.findById(userId);
    if (user) {
      // If this user was referred, update their referredBy field
      if (referrer && !user.referredBy) {
        await User.update(userId, { referredBy: referrer.id });
      }

      // The license key will be added by the admin when the payment is approved
      await User.addPendingPayment(userId, plan, savedPayment.id);
    }

    // Return success response with order details
    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      data: {
        orderId: payment.orderId,
        plan: payment.plan,
        amount: payment.amount,
        paymentMethod: payment.paymentMethod,
        status: payment.status
      }
    });
  } catch (error) {
    console.error('Payment verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment verification',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/revenue
 * @desc    Calculate and return total revenue
 * @access  Private (Admin only)
 */
router.get('/revenue', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all approved payments
    const payments = await Payment.find({ status: 'approved' });

    // Log payment details for debugging
    console.log('Approved payments for revenue calculation:');
    payments.forEach(payment => {
      console.log(`Payment ID: ${payment.id}, Order ID: ${payment.orderId}, Amount: ${payment.amount}, Type: ${typeof payment.amount}`);
    });

    // Calculate total revenue with proper numeric conversion
    const totalRevenue = payments.reduce((sum, payment) => {
      const amount = typeof payment.amount === 'string' ? parseFloat(payment.amount) : Number(payment.amount) || 0;
      return sum + amount;
    }, 0);

    // Return the result
    return res.status(200).json({
      success: true,
      count: payments.length,
      totalRevenue,
      payments: payments.map(p => ({
        id: p.id,
        orderId: p.orderId,
        amount: p.amount,
        amountType: typeof p.amount,
        convertedAmount: typeof p.amount === 'string' ? parseFloat(p.amount) : Number(p.amount) || 0
      }))
    });
  } catch (error) {
    console.error('Revenue calculation error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during revenue calculation',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/test
 * @desc    Test MySQL connection
 * @access  Public
 */
router.get('/test', async (req, res) => {
  try {
    // Test the database connection
    const [result] = await query('SELECT 1 as test');

    // Create a test payment object
    const testPayment = {
      orderId: `TEST-${Date.now()}`,
      transactionId: 'TEST123456789',
      paymentMethod: 'bkash',
      amount: 199,
      plan: 'Monthly',
      customerInfo: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '01712345678'
      }
    };

    // We don't generate a license key for test payments anymore
    // License keys are only provided by admins

    // Don't save to database, just return the object
    return res.status(200).json({
      success: true,
      message: 'MySQL connection test successful',
      data: testPayment
    });
  } catch (error) {
    console.error('MySQL test error:', error);
    return res.status(500).json({
      success: false,
      message: 'MySQL connection test failed',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/pending
 * @desc    Get all pending payments
 * @access  Private (Admin only)
 */
router.get('/pending', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('Fetching pending payments...');

    // Get all pending payments, sorted by creation date (newest first)
    // For MySQL, we need to pass the criteria and sort object correctly
    // In MySQL schema, we use 'status' instead of 'verificationStatus'
    const payments = await Payment.find({ status: 'pending' }, { createdAt: -1 });

    // Log the result for debugging
    console.log('Fetched pending payments:', payments ? payments.length : 0);

    // Add dummy data for testing if no pending payments found
    if (payments.length === 0) {
      console.log('No pending payments found, adding dummy data for testing');

      // Create a dummy pending payment
      const dummyPayment = {
        id: 999,
        orderId: 'ORD-TEST-001',
        transactionId: 'TXN-TEST-001',
        paymentMethod: 'bkash',
        amount: 999,
        plan: 'yearly',
        userId: 1,
        status: 'pending',
        verificationStatus: 'pending',
        createdAt: new Date(),
        customerInfo: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          phone: '01712345678'
        }
      };

      return res.status(200).json({
        success: true,
        count: 1,
        data: [dummyPayment]
      });
    }

    return res.status(200).json({
      success: true,
      count: payments.length,
      data: payments
    });
  } catch (error) {
    console.error('Get pending payments error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving pending payments',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/:orderId
 * @desc    Get payment details by order ID
 * @access  Private (Admin only)
 */
router.get('/:orderId', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;

    // Find payment by order ID
    const payment = await Payment.findOne({ orderId });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Return payment details
    return res.status(200).json({
      success: true,
      data: {
        orderId: payment.orderId,
        plan: payment.plan,
        amount: payment.amount,
        paymentMethod: payment.paymentMethod,
        status: payment.status,
        createdAt: payment.createdAt
      }
    });
  } catch (error) {
    console.error('Get payment error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving payment',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments
 * @desc    Get all payments
 * @access  Private (Admin only)
 */
router.get('/', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all payments, sorted by creation date (newest first)
    // For MySQL, we need to pass the sort object correctly
    const payments = await Payment.find({}, { createdAt: -1 });

    // Log the result for debugging
    console.log('Fetched payments:', payments ? payments.length : 0);

    // Debug payment amounts
    if (payments && payments.length > 0) {
      console.log('Payment amount types:');
      payments.forEach(payment => {
        console.log(`Payment ID: ${payment.id}, Order ID: ${payment.orderId}, Amount: ${payment.amount}, Type: ${typeof payment.amount}`);
      });

      // Calculate total revenue for debugging
      const approvedPayments = payments.filter(p => p.verificationStatus === 'approved');
      const totalRevenue = approvedPayments.reduce((sum, payment) => {
        const amount = typeof payment.amount === 'string' ? parseFloat(payment.amount) : Number(payment.amount) || 0;
        return sum + amount;
      }, 0);

      console.log(`Total approved payments: ${approvedPayments.length}`);
      console.log(`Total revenue: ${totalRevenue}`);
    }

    return res.status(200).json({
      success: true,
      count: payments.length,
      data: payments
    });
  } catch (error) {
    console.error('Get all payments error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving payments',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/approve
 * @desc    Approve a payment and send license key
 * @access  Private (Admin only)
 */
router.post('/:orderId/approve', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { adminNotes, licenseKey } = req.body;

    console.log('Approving payment with order ID:', orderId);
    console.log('License key:', licenseKey);
    console.log('Admin notes:', adminNotes);

    // Find payment by order ID
    let payment;
    try {
      payment = await Payment.findOne({ orderId });
      console.log('Payment search result:', payment);
    } catch (findError) {
      console.error('Error finding payment:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding payment',
        error: findError.message
      });
    }

    if (!payment) {
      console.error('Payment not found with order ID:', orderId);
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    console.log('Found payment:', payment.id, payment.orderId);

    // Check if payment is already verified
    if (payment.status === 'approved') {
      console.error('Payment already approved:', payment.orderId);
      return res.status(400).json({
        success: false,
        message: 'Payment already approved'
      });
    }

    // Require a license key for approval
    if (!licenseKey || licenseKey.trim() === '') {
      console.error('License key is required for payment approval');
      return res.status(400).json({
        success: false,
        message: 'License key is required for payment approval'
      });
    }

    // Check if customer information is missing and try to get it from the user
    if (!payment.customerInfo ||
        (!payment.customerInfo.name && (!payment.customerInfo.firstName && !payment.customerInfo.lastName)) ||
        !payment.customerInfo.email ||
        !payment.customerInfo.phone) {

      console.log('Customer information missing, trying to get it from user');

      if (payment.userId) {
        const user = await User.findById(payment.userId);
        if (user) {
          console.log('Found user, updating customer information');

          try {
            // Try to update customer information from user
            await Payment.update(payment.id, {
              customer_name: user.name,
              customer_email: user.email,
              customer_phone: user.phone || ''
            });
          } catch (updateError) {
            console.log('Could not update customer information, columns might not exist:', updateError.message);
            // Continue without updating customer info
          }

          // Refresh payment object
          payment = await Payment.findById(payment.id);
        }
      }
    }

    // Update the payment using the Payment model
    try {
      console.log('Updating payment record with ID:', payment.id);

      // Use the Payment model to update the payment
      const updatedPayment = await Payment.update(payment.id, {
        status: 'approved',
        licenseKey: licenseKey.trim(),
        adminNotes: adminNotes || '',
        approvedBy: req.user.id,
        approvedAt: new Date()
      });

      console.log('Payment record updated successfully:', updatedPayment.id);

      // If there's a user associated with this payment, update their license
      if (payment.userId) {
        try {
          console.log('Adding license key for user:', payment.userId);

          // Use the User model to add or update the license key
          await User.addOrUpdateLicenseKey(
            payment.userId,
            licenseKey.trim(),
            payment.plan,
            payment.id,
            'approved'
          );

          console.log('Successfully added/updated license key for user:', payment.userId);

        } catch (licenseError) {
          console.error('Error updating license key:', licenseError);
          // Continue with the response even if license update fails
        }
      }

      // Process affiliate commission if this payment was referred by someone
      if (payment.referredBy) {
        try {
          console.log('Processing affiliate commission for referrer:', payment.referredBy);

          // Get referrer details
          const referrer = await User.findById(payment.referredBy);

          if (referrer) {
            // Calculate 20% commission
            const commissionRate = 0.2; // 20%
            const commissionAmount = payment.amount * commissionRate;

            // Create affiliate transaction record
            await AffiliateTransaction.create({
              referrerId: referrer.id,
              referredUserId: payment.userId,
              paymentId: payment.id,
              originalAmount: payment.amount,
              commissionAmount: commissionAmount,
              commissionRate: commissionRate,
              status: 'approved',
              plan: payment.plan
            });

            // Update referrer's commission balance using the dedicated method
            await User.updateAffiliateCommission(referrer.id, commissionAmount);

            // Mark payment as commission paid
            await Payment.update(payment.id, {
              isAffiliateCommissionPaid: true
            });

            console.log(`Affiliate commission of ${commissionAmount} added to user ${referrer.id} for payment ${payment.orderId}`);
          }
        } catch (commissionError) {
          console.error('Error processing affiliate commission:', commissionError);
          // Continue with the response even if commission processing fails
        }
      }

      // Send license key email
      try {
        console.log('Sending license key email for payment:', payment.orderId);

        // Get updated payment data
        const updatedPayment = await Payment.findById(payment.id);

        if (updatedPayment) {
          await sendLicenseKeyEmail(updatedPayment);

          // Mark license key as sent
          await Payment.update(payment.id, {
            licenseKeySent: true
          });

          console.log('License key email sent successfully');
        }
      } catch (emailError) {
        console.error('Failed to send license key email:', emailError);
        // Continue with the response even if email fails
      }

      // Return success response
      return res.status(200).json({
        success: true,
        message: 'Payment approved and license key generated',
        data: {
          orderId: payment.orderId,
          plan: payment.plan,
          licenseKey: licenseKey.trim(),
          status: 'approved',
          verificationStatus: 'approved', // For frontend compatibility
          licenseKeySent: true
        }
      });
    } catch (updateError) {
      console.error('Error updating payment record:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update payment record',
        error: process.env.NODE_ENV === 'development' ? updateError.message : {}
      });
    }
  } catch (error) {
    console.error('Payment approval error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment approval',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/reject
 * @desc    Reject a payment
 * @access  Private (Admin only)
 */
router.post('/:orderId/reject', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { adminNotes } = req.body;

    console.log('Rejecting payment with order ID:', orderId);
    console.log('Admin notes:', adminNotes);

    // Find payment by order ID
    let payment;
    try {
      payment = await Payment.findOne({ orderId });
      console.log('Payment search result:', payment);
    } catch (findError) {
      console.error('Error finding payment:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding payment',
        error: findError.message
      });
    }

    if (!payment) {
      console.error('Payment not found with order ID:', orderId);
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    console.log('Found payment:', payment.id, payment.orderId);

    // Check if payment is already verified or rejected
    if (payment.status !== 'pending') {
      console.error(`Payment already ${payment.status}:`, payment.orderId);
      return res.status(400).json({
        success: false,
        message: `Payment already ${payment.status}`
      });
    }

    // Direct SQL query to update the payment record
    try {
      console.log('Updating payment record with ID:', payment.id);

      // Use the Payment model to update the payment
      const updatedPayment = await Payment.update(payment.id, {
        status: 'rejected',
        adminNotes: adminNotes || 'Payment rejected by admin',
        approvedBy: req.user.id,
        approvedAt: new Date()
      });

      console.log('Payment record updated successfully:', updatedPayment.id);

      // If there's a user associated with this payment, update their license
      if (payment.userId) {
        try {
          console.log('Updating license key status for user:', payment.userId);

          // Get the LicenseKey model
          const LicenseKey = require('../models/mysql/LicenseKey');

          // Check if a license key already exists for this payment
          const existingLicense = await LicenseKey.findOne({ paymentId: payment.id });

          if (existingLicense) {
            // Update existing license
            await LicenseKey.update(existingLicense.id, {
              status: 'revoked',
              paymentStatus: 'rejected'
            });
            console.log('Updated license key status to revoked:', existingLicense.id);
          }
        } catch (licenseError) {
          console.error('Error updating license key status:', licenseError);
          // Continue with the response even if license update fails
        }
      }

      // Return success response
      return res.status(200).json({
        success: true,
        message: 'Payment rejected successfully',
        data: {
          orderId: payment.orderId,
          status: 'rejected',
          verificationStatus: 'rejected' // For frontend compatibility
        }
      });
    } catch (updateError) {
      console.error('Error updating payment record:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update payment record',
        error: process.env.NODE_ENV === 'development' ? updateError.message : {}
      });
    }
  } catch (error) {
    console.error('Payment rejection error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment rejection',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/resend-license
 * @desc    Resend license key email
 * @access  Private (Admin only)
 */
router.post('/:orderId/resend-license', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;

    console.log('Resending license key for order ID:', orderId);

    // Find payment by order ID
    let payment;
    try {
      payment = await Payment.findOne({ orderId });
      console.log('Payment search result:', payment);
    } catch (findError) {
      console.error('Error finding payment:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding payment',
        error: findError.message
      });
    }

    if (!payment) {
      console.error('Payment not found with order ID:', orderId);
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if payment is approved and has a license key
    if (payment.status !== 'approved' || !payment.licenseKey) {
      console.error('Payment not approved or missing license key:', payment.orderId);
      return res.status(400).json({
        success: false,
        message: 'Payment not approved or missing license key'
      });
    }

    // Get updated payment data with customer info
    try {
      // Get user info
      const user = await User.findById(payment.userId);

      if (user) {
        // Add customer info to payment object
        payment.customerInfo = {
          email: user.email,
          firstName: user.name.split(' ')[0],
          lastName: user.name.split(' ').slice(1).join(' ') || ''
        };
      } else {
        // Use default customer info if not found
        payment.customerInfo = {
          email: '<EMAIL>',
          firstName: 'Customer',
          lastName: 'Name'
        };
      }

      // Send license key email
      await sendLicenseKeyEmail(payment);

      // Mark license key as sent
      await Payment.update(payment.id, {
        licenseKeySent: true
      });

      console.log('License key email sent successfully');

      // Return success response
      return res.status(200).json({
        success: true,
        message: 'License key email sent successfully',
        data: {
          orderId: payment.orderId,
          email: payment.customerInfo.email
        }
      });
    } catch (emailError) {
      console.error('Failed to send license key email:', emailError);
      return res.status(500).json({
        success: false,
        message: 'Failed to send license key email',
        error: process.env.NODE_ENV === 'development' ? emailError.message : {}
      });
    }
  } catch (error) {
    console.error('Resend license key error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during license key resend',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});



/**
 * @route   POST /api/payments/:orderId/update-license
 * @desc    Manually update a payment's license key
 * @access  Private (Admin only)
 */
router.post('/:orderId/update-license', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { licenseKey } = req.body;

    console.log('Updating license key for order ID:', orderId);
    console.log('New license key:', licenseKey);

    // Validate license key
    if (!licenseKey || licenseKey.trim() === '') {
      console.error('License key is required');
      return res.status(400).json({
        success: false,
        message: 'License key is required'
      });
    }

    // Find payment by order ID
    let payment;
    try {
      payment = await Payment.findOne({ orderId });
      console.log('Payment search result:', payment);
    } catch (findError) {
      console.error('Error finding payment:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding payment',
        error: findError.message
      });
    }

    if (!payment) {
      console.error('Payment not found with order ID:', orderId);
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Update the license key in the payment record
    try {
      console.log('Updating license key for payment ID:', payment.id);

      // Update the payment record
      await Payment.update(payment.id, {
        licenseKey: licenseKey.trim()
      });

      console.log('Payment license key updated successfully');

      // If payment is already approved, update the license key for the user as well
      if (payment.status === 'approved' && payment.userId) {
        try {
          console.log('Updating license key for user:', payment.userId);

          // Use the User model to update the license key
          await User.addOrUpdateLicenseKey(
            payment.userId,
            licenseKey.trim(),
            payment.plan,
            payment.id,
            'approved'
          );

          console.log('Successfully updated license key for user:', payment.userId);

        } catch (licenseError) {
          console.error('Error updating license key in license_keys table:', licenseError);
          // Continue with the response even if license update fails
        }
      }

      // Return success response
      return res.status(200).json({
        success: true,
        message: 'License key updated successfully',
        data: {
          orderId: payment.orderId,
          licenseKey: licenseKey.trim()
        }
      });
    } catch (updateError) {
      console.error('Error updating license key:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update license key',
        error: process.env.NODE_ENV === 'development' ? updateError.message : {}
      });
    }
  } catch (error) {
    console.error('Update license key error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during license key update',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

// TEMPORARY: Clear all payments (remove this in production)
router.delete('/admin/clear-all', async (req, res) => {
  try {
    // Add basic authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || authHeader !== 'Bearer admin-clear-payments-2025') {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Count existing payments
    const payments = await Payment.findAll();
    const totalPayments = payments.length;

    console.log(`🗑️ Admin requested to clear ${totalPayments} payments`);

    if (totalPayments > 0) {
      // Delete all payments using raw SQL
      const mysql = require('mysql2/promise');
      const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME || 'meta_master',
        port: process.env.DB_PORT || 3306
      });

      await connection.query('DELETE FROM payments');
      await connection.query('ALTER TABLE payments AUTO_INCREMENT = 1');
      await connection.end();

      console.log(`✅ Successfully deleted ${totalPayments} payments`);

      res.json({
        success: true,
        message: `Successfully deleted ${totalPayments} payments`,
        deletedCount: totalPayments
      });
    } else {
      res.json({
        success: true,
        message: 'No payments found to delete',
        deletedCount: 0
      });
    }

  } catch (error) {
    console.error('❌ Error clearing payments:', error);
    res.status(500).json({
      error: 'Failed to clear payments',
      details: error.message
    });
  }
});

module.exports = router;
