import { motion, AnimatePresence } from 'framer-motion'
import { Disclosure } from '@headlessui/react'
import { ChevronUpIcon } from '@heroicons/react/24/solid'
import SectionHeader from '../ui/SectionHeader'

const faqs = [
  {
    question: "What is Meta Master and why is it the best metadata generator?",
    answer: "Meta Master is the #1 AI-powered metadata generator specifically designed for microstock contributors. Using Google's latest Gemini 2.5 AI, it automatically generates SEO-optimized titles, keywords, and descriptions for images, vectors, and videos. With over 1200+ active users and 500K+ files processed, it's the most trusted tool for boosting microstock earnings."
  },
  {
    question: "How does Meta Master improve my microstock SEO and earnings?",
    answer: "Meta Master generates metadata specifically optimized for microstock search algorithms. Our AI understands what buyers search for and creates keywords that actually sell. Users report 40-60% increases in downloads and sales. The tool ensures your content ranks higher on Adobe Stock, Shutterstock, Getty Images, and other platforms."
  },
  {
    question: "What makes Meta Master different from other metadata generators?",
    answer: "Meta Master is the only tool built exclusively for microstock contributors. It features specialized handling for transparent PNGs, vector file embedding, EPS version selection, prompt generator mode, and automatic Shutterstock category selection. Our AI is trained specifically on microstock data, not generic image descriptions."
  },
  {
    question: "Can I export metadata as CSV for multiple stock platforms?",
    answer: "Yes! Meta Master includes professional CSV export functionality for all major platforms including Shutterstock, Adobe Stock, Getty Images, Freepik, Dreamstime, and Vecteezy. Each export is formatted to meet the specific requirements of each platform, saving you hours of manual formatting."
  },
  {
    question: "Which file formats does Meta Master support for metadata generation?",
    answer: "Meta Master supports all major formats: JPG, PNG (with transparent background detection), SVG, EPS, AI (Adobe Illustrator), MP4, MOV, AVI, and MKV. Special features include vector file metadata embedding, EPS version selection (Illustrator 10/2020), and video frame extraction for thumbnail analysis."
  },
  {
    question: "How fast is Meta Master compared to manual metadata creation?",
    answer: "Meta Master is 5x faster than manual tagging. Process hundreds of files in minutes instead of hours. Batch processing allows you to generate metadata for entire folders simultaneously, with intelligent pause/resume functionality. Save 10+ hours per week on metadata creation."
  },
  {
    question: "Is Meta Master suitable for beginners and professional contributors?",
    answer: "Absolutely! Meta Master is designed for all skill levels. Beginners benefit from the intuitive interface and automatic metadata generation, while professionals appreciate advanced features like custom keywords, negative word filtering, prompt generator mode, and multi-API key management."
  },
  {
    question: "How do I get started with Meta Master and Google Gemini AI?",
    answer: "Getting started is easy! Download Meta Master, obtain a free Google Gemini API key (Google provides generous free tier), and start generating metadata immediately. We include step-by-step setup guides, video tutorials, and email support to ensure you're successful from day one."
  },
  {
    question: "Can Meta Master help me rank higher on stock photography platforms?",
    answer: "Yes! Meta Master's AI generates SEO-optimized metadata specifically designed to improve your rankings on Shutterstock, Adobe Stock, Getty Images, and other platforms. The keywords and titles are based on actual search patterns and buyer behavior, not just image descriptions."
  }
]

export default function FAQ() {
  return (
    <section id="faq" className="section">
      <div className="container">
        <SectionHeader
          title="Meta Master FAQ - Metadata Generator Questions"
          subtitle="Everything you need to know about the #1 AI metadata generator for microstock success"
        />
        
        <div className="max-w-3xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Disclosure>
                  {({ open }) => (
                    <>
                      <Disclosure.Button className="flex w-full justify-between rounded-lg bg-dark-800 px-4 py-4 text-left text-lg font-medium text-white hover:bg-dark-700 focus:outline-none focus-visible:ring focus-visible:ring-primary">
                        <span>{faq.question}</span>
                        <ChevronUpIcon
                          className={`${
                            open ? 'rotate-180 transform' : ''
                          } h-5 w-5 text-primary`}
                        />
                      </Disclosure.Button>
                      <AnimatePresence>
                        {open && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Disclosure.Panel className="px-4 pt-4 pb-2 text-dark-300">
                              {faq.answer}
                            </Disclosure.Panel>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </>
                  )}
                </Disclosure>
              </motion.div>
            ))}
          </div>
          
          <motion.div 
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <p className="text-dark-400 mb-4">Still have questions?</p>
            <a href="/contact" className="btn btn-outline">
              Contact Us
            </a>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
