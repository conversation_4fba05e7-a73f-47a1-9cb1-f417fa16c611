import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { UserCircleIcon } from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function ProfilePage() {
  const { register, handleSubmit, formState: { errors }, reset } = useForm()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState('')
  const navigate = useNavigate()

  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }

        const response = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bear<PERSON> ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('Failed to fetch user profile')
        }

        const data = await response.json()

        // Set form default values
        reset({
          name: data.user.name,
          email: data.user.email,
          username: data.user.username
        })
      } catch (error) {
        console.error('Error fetching user profile:', error)
        setError('Failed to load user profile. Please try again.')

        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserProfile()
  }, [navigate, reset])

  const onSubmit = async (data) => {
    setIsSaving(true)
    setError(null)
    setSuccessMessage('')

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      const response = await fetch('http://localhost:5001/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: data.name
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      setSuccessMessage('Profile updated successfully')

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('')
      }, 3000)
    } catch (error) {
      console.error('Error updating profile:', error)
      setError('Failed to update profile. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-light mb-6">Your Profile</h1>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
          >
            {error && (
              <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4 mb-6">
                {error}
              </div>
            )}

            {successMessage && (
              <div className="bg-green-900/10 border border-green-900/20 text-green-500 rounded-lg p-4 mb-6">
                {successMessage}
              </div>
            )}

            <div className="flex items-center mb-8">
              <div className="bg-gradient-to-r from-primary to-secondary rounded-full p-4">
                <UserCircleIcon className="w-12 h-12 text-white" />
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-semibold text-light">Account Information</h2>
                <p className="text-text">Update your personal details</p>
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-light mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    className={`w-full bg-dark border ${errors.name ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                    {...register('name', { required: 'Name is required' })}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-danger">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-light mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                    {...register('email')}
                    disabled
                  />
                  <p className="mt-1 text-xs text-text">Email cannot be changed</p>
                </div>

                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-light mb-1">
                    Username
                  </label>
                  <input
                    type="text"
                    id="username"
                    className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                    {...register('username')}
                    disabled
                  />
                  <p className="mt-1 text-xs text-text">Username cannot be changed</p>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSaving}
                  className="bg-gradient-to-r from-primary to-secondary text-white font-medium py-3 px-6 rounded-lg hover:opacity-90 transition-opacity disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </motion.div>
        )}
      </div>
    </UserLayout>
  )
}
