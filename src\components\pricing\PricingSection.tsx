import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import PricingCard, { PricingPlan } from './PricingCard'

// Pricing plans for other regions can be added here if needed in the future

// Bangladesh pricing plans
const bangladeshPlans: PricingPlan[] = [
  {
    name: 'Monthly',
    planType: 'core',
    price: 199,
    originalPrice: 250,
    period: 'month',
    description: 'Unlock the full potential of Meta Master',
    features: [
      'AI-powered metadata generation',
      'Image & vector file support',
      'Basic video support',
      'Metadata embedding',
      'CSV export for Getty Images'
    ],
    featureDescriptions: [
      'Batch Processing',
      'Trial Period',
      'Gemini API Integration',
      'Results Generation',
      'Supported Formats'
    ],
    buttonText: 'GET STARTED',
    color: 'primary',
    currency: 'Taka',
    discount: '20% OFF'
  },
  {
    name: 'Yearly',
    planType: 'overdrive',
    price: 1399,
    originalPrice: 1700,
    period: 'year',
    description: 'Best value for regular contributors',
    features: [
      'Everything in Monthly plan',
      'Advanced video processing',
      'Unlimited batch processing',
      'Priority AI processing',
      'Advanced metadata editor'
    ],
    featureDescriptions: [
      'Unlimited',
      '1 Year License',
      'Gemini API Integration',
      'Priority Processing',
      'All Formats Supported'
    ],
    buttonText: 'GET STARTED',
    featured: false,
    color: 'accent',
    currency: 'Taka',
    discount: '18% OFF'
  },
  {
    name: 'Lifetime',
    planType: 'team',
    price: 2499,
    originalPrice: 3125,
    period: 'lifetime',
    description: 'One-time payment, lifetime access',
    features: [
      'Everything in Yearly plan',
      'Lifetime license',
      '1 device license',
      'All future updates included',
      'Custom CSV export formats'
    ],
    featureDescriptions: [
      'Unlimited',
      'Lifetime Access',
      'Gemini API Integration',
      'Premium Support',
      'All Formats + Custom'
    ],
    buttonText: 'GET STARTED',
    featured: true,
    badge: 'Most Popular',
    color: 'secondary',
    currency: 'Taka',
    discount: '20% OFF'
  }
]

const PricingSection: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annual'>('annual')

  // Use Bangladesh pricing plans
  const activePlans = bangladeshPlans

  // Filter plans based on billing period for Bangladesh pricing
  const getVisiblePlans = () => {
    if (billingPeriod === 'monthly') {
      return [activePlans[0]] // Only show Monthly plan
    } else {
      return [activePlans[1], activePlans[2]] // Show Yearly and Lifetime plans
    }
  }

  return (
    <section id="pricing" className="section relative overflow-hidden py-20 bg-[#051824]">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('/src/assets/images/grid-pattern.svg')] opacity-5"></div>

        {/* Radial gradient background */}
        <div className="absolute inset-0 bg-gradient-radial from-[#072a3a] to-[#051824]"></div>

        {/* Animated glow effects */}
        <motion.div
          className="absolute top-1/4 right-1/4 w-[600px] h-[600px] rounded-full bg-cyan-500/5 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />

        <motion.div
          className="absolute bottom-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-cyan-700/5 blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
      </div>

      <div className="container relative z-10">
        {/* Section header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            Meta Master <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500">Pricing</span>
          </h2>
          <h3 className="text-xl md:text-2xl font-medium text-gray-400">
            Choose the plan that's right for you !!
          </h3>
        </motion.div>

        {/* Billing toggle */}
        <motion.div
          className="flex justify-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-[#072a3a] rounded-lg inline-flex backdrop-blur-sm border border-cyan-500/20 shadow-lg overflow-hidden">
            <motion.button
              className={`px-8 py-3 text-sm font-medium transition-all relative overflow-hidden ${
                billingPeriod === 'monthly'
                  ? 'bg-cyan-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setBillingPeriod('monthly')}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10">Monthly Plan</span>
            </motion.button>
            <motion.button
              className={`px-8 py-3 text-sm font-medium transition-all relative overflow-hidden ${
                billingPeriod === 'annual'
                  ? 'bg-cyan-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setBillingPeriod('annual')}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10">Yearly & Lifetime</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Pricing cards */}
        <div className={`grid grid-cols-1 ${billingPeriod === 'monthly' ? 'md:grid-cols-1 max-w-md mx-auto' : 'md:grid-cols-2'} gap-8 lg:gap-10`}>
          <AnimatePresence mode="wait">
            {getVisiblePlans().map((plan, index) => (
              <PricingCard key={plan.name} plan={plan} index={index} />
            ))}
          </AnimatePresence>
        </div>

        {/* Note */}
        <motion.div
          className="text-center mt-16 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="bg-[#072a3a] backdrop-blur-sm border border-cyan-500/20 rounded-lg p-5 shadow-lg">
            <div className="flex items-center justify-center mb-3 text-cyan-500">
              <i className="fas fa-info-circle text-xl mr-2"></i>
              <h4 className="text-lg font-medium">Important Information</h4>
            </div>
            <div className="space-y-2 text-gray-400">
              <p>All plans require a Google Gemini API key (free tier available from Google)</p>
              <p>Prices are in Bangladeshi Taka (BDT) and include all applicable taxes</p>
              <p>Local payment methods available for customers in Bangladesh</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Animated scan line */}
      <motion.div
        className="absolute inset-0 overflow-hidden pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <motion.div
          className="w-full h-[1px] bg-gradient-to-r from-transparent via-cyan-500/50 to-transparent"
          animate={{ y: [0, 1000, 0] }}
          transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
        />
      </motion.div>
    </section>
  )
}

export default PricingSection
