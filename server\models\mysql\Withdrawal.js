const { query } = require('../../config/db');

class Withdrawal {
  // Find withdrawal by ID
  static async findById(id) {
    try {
      console.log('Finding withdrawal by ID:', id);

      // Check if the withdrawals table exists
      try {
        const [tables] = await query('SHOW TABLES LIKE "withdrawals"');
        if (tables.length === 0) {
          console.error('withdrawals table does not exist');
          return null;
        }
      } catch (tableError) {
        console.error('Error checking withdrawals table:', tableError);
        return null;
      }

      const [rows] = await query(
        'SELECT * FROM withdrawals WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        console.log('No withdrawal found with ID:', id);
        return null;
      }

      console.log('Found withdrawal:', rows[0]);

      // Convert snake_case to camelCase
      return this.formatWithdrawal(rows[0]);
    } catch (error) {
      console.error('Error finding withdrawal by ID:', error);
      throw error;
    }
  }

  // Find withdrawals by criteria
  static async find(criteria = {}, sort = {}) {
    try {
      console.log('Finding withdrawals with criteria:', JSON.stringify(criteria));

      // Check if the withdrawals table exists
      try {
        const [tables] = await query('SHOW TABLES LIKE "withdrawals"');
        if (tables.length === 0) {
          console.error('withdrawals table does not exist');
          return [];
        }
      } catch (tableError) {
        console.error('Error checking withdrawals table:', tableError);
        return [];
      }

      let sql = 'SELECT w.*, u.id as user_id, u.name, u.email, u.username FROM withdrawals w';
      const params = [];

      // Add JOIN for user data if populate is needed later
      sql += ' LEFT JOIN users u ON w.user_id = u.id';

      // Add WHERE clause if criteria provided
      if (Object.keys(criteria).length > 0) {
        const conditions = [];

        Object.entries(criteria).forEach(([key, value]) => {
          // Handle special case for userId which maps to user_id
          if (key === 'userId') {
            conditions.push(`w.user_id = ?`);
            params.push(value);
            return;
          }

          // Handle special case for user which should be user_id
          if (key === 'user') {
            conditions.push(`w.user_id = ?`);
            params.push(value);
            return;
          }

          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          conditions.push(`w.${snakeKey} = ?`);
          params.push(value);
        });

        sql += ` WHERE ${conditions.join(' AND ')}`;
      }

      // Add ORDER BY clause if sort provided
      if (Object.keys(sort).length > 0) {
        const sortFields = [];

        Object.entries(sort).forEach(([key, value]) => {
          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          sortFields.push(`w.${snakeKey} ${value === 1 ? 'ASC' : 'DESC'}`);
        });

        sql += ` ORDER BY ${sortFields.join(', ')}`;
      } else {
        // Default sort by created_at DESC
        sql += ' ORDER BY w.created_at DESC';
      }

      console.log('Executing query:', sql, 'with params:', params);
      const [rows] = await query(sql, params);
      console.log('Query returned rows:', rows.length);

      // Convert snake_case to camelCase for all results
      const withdrawals = rows.map(row => {
        const withdrawal = this.formatWithdrawal(row);

        // Add user data if available
        if (row.user_id) {
          withdrawal.user = {
            id: row.user_id,
            name: row.name || '',
            email: row.email || '',
            username: row.username || ''
          };
        }

        return withdrawal;
      });

      // Add a populate method that does nothing (for MongoDB compatibility)
      withdrawals.populate = function() { return this; };
      withdrawals.sort = function() { return this; };

      return withdrawals;
    } catch (error) {
      console.error('Error finding withdrawals:', error);
      throw error;
    }
  }

  // Create a new withdrawal
  static async create(withdrawalData) {
    try {
      const {
        userId,
        amount,
        paymentMethod = 'bkash',
        bkashNumber,
        accountType = 'personal',
        status = 'pending'
      } = withdrawalData;

      console.log('Creating withdrawal with data:', JSON.stringify({
        userId, amount, paymentMethod, bkashNumber, accountType, status
      }));

      // Check if the withdrawals table exists
      try {
        const [tables] = await query('SHOW TABLES LIKE "withdrawals"');
        if (tables.length === 0) {
          console.error('withdrawals table does not exist');
          // Create the table if it doesn't exist
          await query(`
            CREATE TABLE withdrawals (
              id INT AUTO_INCREMENT PRIMARY KEY,
              user_id INT NOT NULL,
              amount DECIMAL(10, 2) NOT NULL,
              payment_method VARCHAR(50) NOT NULL DEFAULT 'bkash',
              bkash_number VARCHAR(20) NOT NULL,
              account_type VARCHAR(20) NOT NULL DEFAULT 'personal',
              status VARCHAR(20) NOT NULL DEFAULT 'pending',
              transaction_id VARCHAR(100),
              admin_notes TEXT,
              processed_by INT,
              processed_at DATETIME,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
          `);
          console.log('Created withdrawals table');
        }

        // Check if the user_id column exists
        const [columns] = await query('SHOW COLUMNS FROM withdrawals LIKE "user_id"');
        if (columns.length === 0) {
          console.error('user_id column does not exist in withdrawals table');
          // Add the column if it doesn't exist
          await query('ALTER TABLE withdrawals ADD COLUMN user_id INT NOT NULL AFTER id');
          console.log('Added user_id column to withdrawals table');
        }
      } catch (tableError) {
        console.error('Error checking withdrawals table:', tableError);
        throw tableError;
      }

      const [result] = await query(
        'INSERT INTO withdrawals (user_id, amount, payment_method, bkash_number, account_type, status) ' +
        'VALUES (?, ?, ?, ?, ?, ?)',
        [userId, amount, paymentMethod, bkashNumber, accountType, status]
      );

      console.log('Withdrawal created with ID:', result.insertId);
      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating withdrawal:', error);
      throw error;
    }
  }

  // Update withdrawal
  static async update(id, updateData) {
    try {
      console.log('Updating withdrawal with ID:', id, 'Data:', JSON.stringify(updateData));

      // Check if the withdrawals table exists
      try {
        const [tables] = await query('SHOW TABLES LIKE "withdrawals"');
        if (tables.length === 0) {
          console.error('withdrawals table does not exist');
          return null;
        }
      } catch (tableError) {
        console.error('Error checking withdrawals table:', tableError);
        return null;
      }

      // First check if the withdrawal exists
      const existingWithdrawal = await this.findById(id);
      if (!existingWithdrawal) {
        console.error('Withdrawal not found with ID:', id);
        throw new Error(`Withdrawal not found with ID: ${id}`);
      }

      // Check if necessary columns exist
      try {
        const [processedByColumns] = await query('SHOW COLUMNS FROM withdrawals LIKE "processed_by"');
        if (processedByColumns.length === 0) {
          console.log('Adding processed_by column to withdrawals table');
          await query('ALTER TABLE withdrawals ADD COLUMN processed_by INT');
        }

        const [processedAtColumns] = await query('SHOW COLUMNS FROM withdrawals LIKE "processed_at"');
        if (processedAtColumns.length === 0) {
          console.log('Adding processed_at column to withdrawals table');
          await query('ALTER TABLE withdrawals ADD COLUMN processed_at DATETIME');
        }

        const [transactionIdColumns] = await query('SHOW COLUMNS FROM withdrawals LIKE "transaction_id"');
        if (transactionIdColumns.length === 0) {
          console.log('Adding transaction_id column to withdrawals table');
          await query('ALTER TABLE withdrawals ADD COLUMN transaction_id VARCHAR(100)');
        }

        const [adminNotesColumns] = await query('SHOW COLUMNS FROM withdrawals LIKE "admin_notes"');
        if (adminNotesColumns.length === 0) {
          console.log('Adding admin_notes column to withdrawals table');
          await query('ALTER TABLE withdrawals ADD COLUMN admin_notes TEXT');
        }
      } catch (columnError) {
        console.error('Error checking/adding columns:', columnError);
      }

      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Special handling for Date objects
        if (value instanceof Date) {
          value = value.toISOString().slice(0, 19).replace('T', ' ');
        }

        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      const updateQuery = `UPDATE withdrawals SET ${fields.join(', ')} WHERE id = ?`;
      console.log('Executing update query:', updateQuery, 'with values:', values);

      await query(updateQuery, values);
      console.log('Withdrawal updated successfully');

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating withdrawal:', error);
      throw error;
    }
  }

  // Helper method to format withdrawal data
  static formatWithdrawal(withdrawal) {
    return {
      id: withdrawal.id,
      userId: withdrawal.user_id,
      amount: withdrawal.amount,
      paymentMethod: withdrawal.payment_method,
      bkashNumber: withdrawal.bkash_number,
      accountType: withdrawal.account_type,
      // Add paymentDetails object for compatibility with frontend
      paymentDetails: {
        bkashNumber: withdrawal.bkash_number,
        accountType: withdrawal.account_type
      },
      status: withdrawal.status,
      adminNotes: withdrawal.admin_notes,
      transactionId: withdrawal.transaction_id,
      processedBy: withdrawal.processed_by,
      processedAt: withdrawal.processed_at,
      createdAt: withdrawal.created_at,
      updatedAt: withdrawal.updated_at
    };
  }
}

module.exports = Withdrawal;
