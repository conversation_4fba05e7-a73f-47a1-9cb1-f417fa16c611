import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import SectionHeader from '../components/ui/SectionHeader'
import Button from '../components/ui/Button'

const features = [
  {
    title: "AI-Powered Metadata Generation",
    description: "Watch as Meta Master analyzes your images and generates high-quality titles, keywords, and descriptions in seconds.",
    videoUrl: "#"
  },
  {
    title: "Batch Processing",
    description: "See how you can process hundreds of files at once with our efficient batch processing system.",
    videoUrl: "#"
  },
  {
    title: "Export Options",
    description: "Learn how to export your metadata to CSV files for different stock platforms or embed it directly into your files.",
    videoUrl: "#"
  },
  {
    title: "Special File Handling",
    description: "Discover how Meta Master handles transparent PNGs and black silhouettes with specialized processing.",
    videoUrl: "#"
  }
]

export default function DemoPage() {
  const [activeVideo, setActiveVideo] = useState(0)
  const [uploadedVideos, setUploadedVideos] = useState<any[]>([])
  const [mainDemoVideo, setMainDemoVideo] = useState<string | null>(null)

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0)

    // Set page title
    document.title = 'Demo - Meta Master'

    // Fetch videos from the server
    const fetchVideos = async () => {
      try {
        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
        const response = await fetch(`${apiUrl}/api/website-content/published`);

        if (!response.ok) {
          throw new Error('Failed to fetch website content');
        }

        const data = await response.json();

        if (data.data && data.data.videos) {
          // Filter for demo videos that are active
          const demoVideos = data.data.videos.filter(
            (video: any) => video.category === 'demo' && video.isActive
          );

          setUploadedVideos(demoVideos);

          // Set the first video as the main demo video if available
          if (demoVideos.length > 0) {
            setMainDemoVideo(demoVideos[0].videoUrl);
          }
        }
      } catch (error) {
        console.error('Error fetching videos:', error);
      }
    };

    fetchVideos();
  }, [])

  return (
    <div>
      <div className="pt-32 pb-16 md:pt-40 md:pb-20">
        <div className="container">
          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-6 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            See Meta Master <span className="gradient-text">in Action</span>
          </motion.h1>

          <motion.p
            className="text-xl text-dark-300 text-center max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Watch how easy it is to generate professional metadata for your content
          </motion.p>
        </div>
      </div>

      <section className="section">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <motion.div
                className="relative rounded-xl overflow-hidden shadow-2xl border border-dark-700 aspect-video"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {mainDemoVideo ? (
                  <video
                    src={mainDemoVideo}
                    controls
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <>
                    <img
                      src="/images/main-demo.png"
                      alt="Meta Master Demo"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.button
                        className="w-20 h-20 rounded-full bg-primary/90 flex items-center justify-center text-white"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      </motion.button>
                    </div>
                  </>
                )}
              </motion.div>

              <motion.div
                className="mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h2 className="text-2xl font-bold mb-4">Full Demo Walkthrough</h2>
                <p className="text-dark-400 mb-6">
                  This comprehensive demo shows you the complete workflow of Meta Master, from importing files to exporting metadata. You'll see how to use all the key features and get the most out of the software.
                </p>
                <div className="flex gap-4">
                  <Button to="/download" variant="primary" animate>
                    Try It Yourself
                  </Button>
                  <Button to="/pricing" variant="outline" animate>
                    View Pricing
                  </Button>
                </div>
              </motion.div>
            </div>

            <div>
              <motion.h3
                className="text-xl font-bold mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                Feature Highlights
              </motion.h3>

              <div className="space-y-4">
                {/* Display hardcoded features */}
                {features.map((feature, index) => (
                  <motion.div
                    key={`feature-${index}`}
                    className={`p-4 rounded-lg border ${
                      activeVideo === index
                        ? 'border-primary bg-dark-800/70'
                        : 'border-dark-700 bg-dark-800/30 hover:border-primary/50'
                    } cursor-pointer transition-colors`}
                    onClick={() => {
                      setActiveVideo(index);
                      setMainDemoVideo(null); // Reset main demo video when selecting a feature
                    }}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                  >
                    <h4 className="font-bold mb-1">{feature.title}</h4>
                    <p className="text-sm text-dark-400">{feature.description}</p>
                  </motion.div>
                ))}

                {/* Display uploaded videos */}
                {uploadedVideos.map((video, index) => (
                  <motion.div
                    key={`video-${video.id}`}
                    className={`p-4 rounded-lg border border-primary/30 bg-dark-800/40 hover:border-primary/70 cursor-pointer transition-colors`}
                    onClick={() => setMainDemoVideo(video.videoUrl)}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 + (features.length + index) * 0.1 }}
                  >
                    <h4 className="font-bold mb-1">{video.title || 'Untitled Video'}</h4>
                    <p className="text-sm text-dark-400">{video.description || 'No description available'}</p>
                    <div className="mt-2 flex items-center text-xs text-primary">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                      Watch Video
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="section bg-dark-900/50">
        <div className="container">
          <SectionHeader
            title="Screenshots Gallery"
            subtitle="Take a closer look at Meta Master's interface and features"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((num) => (
              <motion.div
                key={num}
                className="rounded-lg overflow-hidden border border-dark-700 shadow-lg hover:border-primary/50 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: num * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <img
                  src={`/images/screenshot-${num}.png`}
                  alt={`Meta Master Screenshot ${num}`}
                  className="w-full h-auto"
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <motion.h2
              className="text-3xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Ready to experience Meta Master yourself?
            </motion.h2>

            <motion.p
              className="text-lg text-dark-400 mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Download the free trial today and see how Meta Master can transform your workflow.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Button to="/download" variant="primary" size="lg" animate>
                Download Free Trial
              </Button>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
