/**
 * <PERSON><PERSON>t to create an admin user
 *
 * Usage:
 * node scripts/create-admin.js <username> <password> [role]
 *
 * Example:
 * node scripts/create-admin.js admin admin123 superadmin
 */

require('dotenv').config();
const bcrypt = require('bcryptjs');
const { query } = require('../config/db');

async function createAdmin() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);

    if (args.length < 2) {
      console.error('Usage: node scripts/create-admin.js <username> <password> [role]');
      process.exit(1);
    }

    const username = args[0];
    const password = args[1];
    const role = args[2] || 'admin';

    // Check if admin already exists
    const [existingAdmins] = await query('SELECT * FROM admins WHERE username = ?', [username]);

    if (existingAdmins.length > 0) {
      console.error(`Admin with username "${username}" already exists.`);
      process.exit(1);
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Insert admin into database
    const [result] = await query(
      'INSERT INTO admins (username, password, role) VALUES (?, ?, ?)',
      [username, hashedPassword, role]
    );

    console.log(`Admin user "${username}" created successfully with role "${role}".`);
    console.log(`Admin ID: ${result.insertId}`);

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin:', error);
    process.exit(1);
  }
}

// Connect to database and create admin
const { testConnection } = require('../config/db');

testConnection()
  .then(connected => {
    if (!connected) {
      console.error('Failed to connect to MySQL. Please check your configuration.');
      process.exit(1);
    }

    createAdmin();
  })
  .catch(err => {
    console.error('MySQL connection error:', err);
    process.exit(1);
  });
