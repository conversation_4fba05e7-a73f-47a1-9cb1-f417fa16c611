const express = require('express');
const router = express.Router();
const User = require('../models/mysql/User');
const Withdrawal = require('../models/mysql/Withdrawal');
const { authenticate, isAdmin } = require('../middleware/auth');
const { query } = require('../config/db');

/**
 * @route   POST /api/withdrawal/request
 * @desc    Create a new withdrawal request
 * @access  Private
 */
router.post('/request', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount, bkashNumber, accountType } = req.body;

    // Validate required fields
    if (!amount || !bkashNumber) {
      return res.status(400).json({
        success: false,
        message: 'Amount and bKash number are required'
      });
    }

    // Validate minimum withdrawal amount
    if (amount < 1000) {
      return res.status(400).json({
        success: false,
        message: 'Minimum withdrawal amount is 1000 Taka'
      });
    }

    // Get user with affiliate info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate available balance
    // Get pending withdrawal requests
    const pendingWithdrawals = await Withdrawal.find({
      user: userId,
      status: 'pending'
    });

    // Calculate total pending withdrawal amount
    const pendingWithdrawalAmount = pendingWithdrawals.reduce(
      (sum, withdrawal) => sum + withdrawal.amount,
      0
    );

    // Available balance = total earnings - withdrawn - pending withdrawals
    const availableBalance = user.affiliateCommission - user.withdrawnCommission - pendingWithdrawalAmount;

    // Check if user has enough available balance
    if (availableBalance < amount) {
      return res.status(400).json({
        success: false,
        message: `Insufficient balance for withdrawal. Available balance: ৳${availableBalance}`
      });
    }

    // Save bKash number to user profile if not already saved
    if (!user.paymentInfo || !user.paymentInfo.bkashNumber || user.paymentInfo.bkashNumber !== bkashNumber) {
      // Update user payment info
      await User.update(userId, {
        paymentInfoBkashNumber: bkashNumber,
        paymentInfoAccountType: accountType || 'personal'
      });
      console.log('Updated user payment info');
    }

    // Create new withdrawal request using the Withdrawal.create method
    console.log('Creating withdrawal request with user ID:', userId);
    const withdrawal = await Withdrawal.create({
      userId: userId,
      amount,
      paymentMethod: 'bkash',
      bkashNumber,
      accountType: accountType || 'personal',
      status: 'pending'
    });

    // Update the user's available balance immediately
    // This ensures the user can't request more withdrawals than their available balance
    try {
      console.log('Updating user available balance after withdrawal request');
      // We don't need to update the withdrawnCommission yet, as that happens on approval
      // But we do want to track the pending withdrawal in the user's record

      // Get the current pending withdrawals amount
      const pendingWithdrawals = await Withdrawal.find({
        userId: userId,
        status: 'pending'
      });

      // Calculate total pending withdrawal amount
      const pendingWithdrawalAmount = pendingWithdrawals.reduce(
        (sum, w) => sum + parseFloat(w.amount || 0),
        0
      );

      console.log('Total pending withdrawals:', pendingWithdrawalAmount);

      // Update the user's pendingWithdrawals field if it exists
      try {
        // Check if pendingWithdrawals column exists
        const [columns] = await query('SHOW COLUMNS FROM users LIKE "pending_withdrawals"');
        if (columns.length === 0) {
          console.log('Adding pending_withdrawals column to users table');
          await query('ALTER TABLE users ADD COLUMN pending_withdrawals DECIMAL(10, 2) DEFAULT 0');
        }

        // Update the user's pending withdrawals
        await User.update(userId, {
          pendingWithdrawals: pendingWithdrawalAmount
        });

        console.log('User pending withdrawals updated successfully');
      } catch (columnError) {
        console.error('Error checking/adding pending_withdrawals column:', columnError);
      }
    } catch (balanceError) {
      console.error('Error updating user balance after withdrawal request:', balanceError);
      // Continue with the process even if balance update fails
    }
    // This prevents negative balance issues

    // Return success response
    return res.status(201).json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        id: withdrawal._id,
        amount: withdrawal.amount,
        status: withdrawal.status,
        createdAt: withdrawal.createdAt
      }
    });
  } catch (error) {
    console.error('Withdrawal request error:', error);
    console.error('Error stack:', error.stack);
    return res.status(500).json({
      success: false,
      message: 'Server error during withdrawal request: ' + error.message,
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/history
 * @desc    Get withdrawal history for the current user
 * @access  Private
 */
router.get('/history', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get all withdrawal requests for this user, sorted by creation date (newest first)
    console.log('Finding withdrawals for user ID:', userId);

    // Use a try-catch block to handle potential errors
    try {
      const withdrawals = await Withdrawal.find({ userId: userId });
      console.log('Found withdrawals:', withdrawals.length);

      // Return withdrawal history
      return res.status(200).json({
        success: true,
        count: withdrawals.length,
        data: withdrawals
      });
    } catch (findError) {
      console.error('Error finding withdrawals:', findError);

      // Return empty array instead of error
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }
  } catch (error) {
    console.error('Get withdrawal history error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving withdrawal history',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   PUT /api/withdrawal/payment-info
 * @desc    Update user's payment information
 * @access  Private
 */
router.put('/payment-info', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { bkashNumber, accountType } = req.body;

    // Validate required fields
    if (!bkashNumber) {
      return res.status(400).json({
        success: false,
        message: 'bKash number is required'
      });
    }

    // Get user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update payment info
    await User.update(userId, {
      paymentInfoBkashNumber: bkashNumber,
      paymentInfoAccountType: accountType || 'personal'
    });

    // Get updated user
    const updatedUser = await User.findById(userId);

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Payment information updated successfully',
      data: {
        bkashNumber: updatedUser.paymentInfo.bkashNumber,
        accountType: updatedUser.paymentInfo.accountType
      }
    });
  } catch (error) {
    console.error('Update payment info error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while updating payment information',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/payment-info
 * @desc    Get user's payment information
 * @access  Private
 */
router.get('/payment-info', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with payment info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Return payment info
    return res.status(200).json({
      success: true,
      data: {
        bkashNumber: user.paymentInfo.bkashNumber || '',
        accountType: user.paymentInfo.accountType || 'personal'
      }
    });
  } catch (error) {
    console.error('Get payment info error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving payment information',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/admin/all
 * @desc    Get all withdrawal requests
 * @access  Private (Admin only)
 */
router.get('/admin/all', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all withdrawal requests, sorted by creation date (newest first)
    console.log('Finding all withdrawals');
    let withdrawals = [];
    try {
      withdrawals = await Withdrawal.find({});
      console.log('Found withdrawals:', withdrawals.length);
    } catch (findError) {
      console.error('Error finding withdrawals:', findError);
      // Return empty array instead of throwing error
      withdrawals = [];
    }

    // Return all withdrawals
    return res.status(200).json({
      success: true,
      count: withdrawals.length,
      data: withdrawals
    });
  } catch (error) {
    console.error('Get all withdrawals error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving withdrawals',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/admin/pending
 * @desc    Get all pending withdrawal requests
 * @access  Private (Admin only)
 */
router.get('/admin/pending', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all pending withdrawal requests, sorted by creation date (oldest first)
    console.log('Finding pending withdrawals');
    let withdrawals = [];
    try {
      withdrawals = await Withdrawal.find({ status: 'pending' });
      console.log('Found pending withdrawals:', withdrawals.length);
    } catch (findError) {
      console.error('Error finding pending withdrawals:', findError);
      // Return empty array instead of throwing error
      withdrawals = [];
    }

    // Return pending withdrawals
    return res.status(200).json({
      success: true,
      count: withdrawals.length,
      data: withdrawals
    });
  } catch (error) {
    console.error('Get pending withdrawals error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving pending withdrawals',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/withdrawal/:id/approve
 * @desc    Approve a withdrawal request
 * @access  Private (Admin only)
 */
router.post('/:id/approve', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { transactionId, adminNotes } = req.body;

    // Validate transaction ID
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
    }

    // Find withdrawal request
    let withdrawal;
    try {
      withdrawal = await Withdrawal.findById(id);
      if (!withdrawal) {
        return res.status(404).json({
          success: false,
          message: 'Withdrawal request not found'
        });
      }

      // Check if withdrawal is already processed
      if (withdrawal.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: `Withdrawal request already ${withdrawal.status}`
        });
      }
    } catch (findError) {
      console.error('Error finding withdrawal:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding withdrawal request: ' + findError.message
      });
    }

    // Update withdrawal status
    try {
      console.log('Updating withdrawal status to paid:', id);
      const updateResult = await Withdrawal.update(id, {
        status: 'paid',
        transactionId: transactionId,
        adminNotes: adminNotes || '',
        processedBy: req.user.id,
        processedAt: new Date()
      });

      if (!updateResult) {
        console.error('Failed to update withdrawal status');
        return res.status(500).json({
          success: false,
          message: 'Failed to update withdrawal status'
        });
      }

      console.log('Withdrawal status updated successfully');
    } catch (updateError) {
      console.error('Error updating withdrawal status:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Error updating withdrawal status: ' + updateError.message
      });
    }

    // Update user's withdrawn amount and pending withdrawals
    try {
      // Get the user ID from the withdrawal
      const userId = withdrawal.userId || withdrawal.user_id;
      console.log('Finding user with ID:', userId);

      if (!userId) {
        console.error('User ID is missing in the withdrawal record');
        // Continue with the process even if user ID is missing
      } else {
        const user = await User.findById(userId);

        if (user) {
          console.log('User found:', user.id, user.username);
          const currentWithdrawnAmount = user.withdrawnCommission || 0;
          const newWithdrawnAmount = currentWithdrawnAmount + withdrawal.amount;

          console.log('Updating user withdrawn amount:',
            'Current:', currentWithdrawnAmount,
            'Adding:', withdrawal.amount,
            'New:', newWithdrawnAmount);

          // Get all pending withdrawals for this user
          const pendingWithdrawals = await Withdrawal.find({
            userId: userId,
            status: 'pending'
          });

          // Calculate total pending withdrawal amount
          const pendingWithdrawalAmount = pendingWithdrawals.reduce(
            (sum, w) => sum + parseFloat(w.amount || 0),
            0
          );

          console.log('Total pending withdrawals after approval:', pendingWithdrawalAmount);

          // Update the user's withdrawn amount and pending withdrawals
          await User.update(userId, {
            withdrawnCommission: newWithdrawnAmount,
            pendingWithdrawals: pendingWithdrawalAmount
          });

          console.log('User withdrawn amount and pending withdrawals updated successfully');
        } else {
          console.error('User not found with ID:', userId);
        }
      }
    } catch (userError) {
      console.error('Error updating user withdrawn amount:', userError);
      // Continue with the process even if user update fails
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Withdrawal request approved and marked as paid',
      data: {
        id: withdrawal.id || id,
        amount: withdrawal.amount,
        status: 'paid',
        transactionId: transactionId
      }
    });
  } catch (error) {
    console.error('Approve withdrawal error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during withdrawal approval',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/withdrawal/:id/reject
 * @desc    Reject a withdrawal request
 * @access  Private (Admin only)
 */
router.post('/:id/reject', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { adminNotes } = req.body;

    // Find withdrawal request
    let withdrawal;
    try {
      withdrawal = await Withdrawal.findById(id);
      if (!withdrawal) {
        return res.status(404).json({
          success: false,
          message: 'Withdrawal request not found'
        });
      }

      // Check if withdrawal is already processed
      if (withdrawal.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: `Withdrawal request already ${withdrawal.status}`
        });
      }
    } catch (findError) {
      console.error('Error finding withdrawal:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding withdrawal request: ' + findError.message
      });
    }

    // Update withdrawal status
    try {
      console.log('Updating withdrawal status to rejected:', id);
      const updateResult = await Withdrawal.update(id, {
        status: 'rejected',
        adminNotes: adminNotes || 'Rejected by admin',
        processedBy: req.user.id,
        processedAt: new Date()
      });

      if (!updateResult) {
        console.error('Failed to update withdrawal status');
        return res.status(500).json({
          success: false,
          message: 'Failed to update withdrawal status'
        });
      }

      console.log('Withdrawal status updated successfully');
    } catch (updateError) {
      console.error('Error updating withdrawal status:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Error updating withdrawal status: ' + updateError.message
      });
    }

    // No need to return the amount to user's available balance
    // since we're not deducting it when the withdrawal is requested

    // Update user's pending withdrawals
    try {
      // Get the user ID from the withdrawal
      const userId = withdrawal.userId || withdrawal.user_id;
      console.log('Finding user with ID:', userId);

      if (!userId) {
        console.error('User ID is missing in the withdrawal record');
        // Continue with the process even if user ID is missing
      } else {
        const user = await User.findById(userId);

        if (user) {
          console.log('User found:', user.id, user.username);

          // Get all pending withdrawals for this user
          const pendingWithdrawals = await Withdrawal.find({
            userId: userId,
            status: 'pending'
          });

          // Calculate total pending withdrawal amount
          const pendingWithdrawalAmount = pendingWithdrawals.reduce(
            (sum, w) => sum + parseFloat(w.amount || 0),
            0
          );

          console.log('Total pending withdrawals after rejection:', pendingWithdrawalAmount);

          // Update the user's pending withdrawals
          await User.update(userId, {
            pendingWithdrawals: pendingWithdrawalAmount
          });

          console.log('User pending withdrawals updated successfully');
        } else {
          console.error('User not found with ID:', userId);
        }
      }
    } catch (userError) {
      console.error('Error updating user pending withdrawals:', userError);
      // Continue with the process even if user update fails
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Withdrawal request rejected',
      data: {
        id: withdrawal.id || id,
        amount: withdrawal.amount,
        status: 'rejected'
      }
    });
  } catch (error) {
    console.error('Reject withdrawal error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during withdrawal rejection',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
