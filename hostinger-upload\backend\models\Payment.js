const mongoose = require('mongoose');

const PaymentSchema = new mongoose.Schema({
  orderId: {
    type: String,
    required: true,
    unique: true
  },
  transactionId: {
    type: String,
    required: true
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: ['bkash', 'nagad', 'rocket', 'upay']
  },
  amount: {
    type: Number,
    required: true
  },
  plan: {
    type: String,
    required: true,
    enum: ['Monthly', 'Yearly', 'Lifetime']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  referredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  isAffiliateCommissionPaid: {
    type: Boolean,
    default: false
  },
  customerInfo: {
    firstName: {
      type: String,
      required: true
    },
    lastName: {
      type: String,
      required: true
    },
    email: {
      type: String,
      required: true
    },
    phone: {
      type: String,
      required: true
    },
    address: {
      type: String
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'verified', 'rejected', 'failed'],
    default: 'pending'
  },
  verificationStatus: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  adminNotes: {
    type: String
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  verifiedAt: {
    type: Date
  },
  licenseKey: {
    type: String
  },
  licenseKeySent: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
PaymentSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Generate a random license key - only used for testing purposes
// This is not used for actual license key generation in production
PaymentSchema.methods.generateLicenseKey = function() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let licenseKey = '';

  // Generate 5 groups of 5 characters separated by hyphens
  for (let i = 0; i < 5; i++) {
    for (let j = 0; j < 5; j++) {
      licenseKey += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    if (i < 4) licenseKey += '-';
  }

  this.licenseKey = licenseKey;
  return licenseKey;
};

module.exports = mongoose.model('Payment', PaymentSchema);
