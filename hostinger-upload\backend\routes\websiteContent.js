const express = require('express');
const router = express.Router();
const WebsiteContent = require('../models/WebsiteContent');
const { authenticate, isAdmin } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadDir = path.join(__dirname, '../../public/uploads');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'content-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit for videos
  fileFilter: function(req, file, cb) {
    const filetypes = /jpeg|jpg|png|gif|svg|mp4|webm|mov|avi|mkv/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only image and video files are allowed!'));
  }
});

/**
 * @route   GET /api/website-content/versions
 * @desc    Get all versions of website content
 * @access  Private (Admin only)
 */
router.get('/versions', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all versions of website content, sorted by version (newest first)
    const versions = await WebsiteContent.find()
      .sort({ version: -1 })
      .select('version isPublished publishedAt updatedAt updatedBy');

    return res.status(200).json({
      success: true,
      count: versions.length,
      data: versions
    });
  } catch (error) {
    console.error('Get website content versions error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving website content versions',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/website-content/restore/:id
 * @desc    Restore a specific version of website content
 * @access  Private (Admin only)
 */
router.post('/restore/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Find the version to restore
    const versionToRestore = await WebsiteContent.findById(id);

    if (!versionToRestore) {
      return res.status(404).json({
        success: false,
        message: 'Version not found'
      });
    }

    // Create a new version based on the restored version
    const newVersion = new WebsiteContent({
      version: versionToRestore.version + 0.1, // Increment version by 0.1 to indicate it's a restored version
      isPublished: false, // Restored versions are not published by default
      sections: versionToRestore.sections,
      features: versionToRestore.features,
      faqs: versionToRestore.faqs,
      testimonials: versionToRestore.testimonials,
      updatedBy: req.user.id
    });

    await newVersion.save();

    return res.status(200).json({
      success: true,
      message: 'Version restored successfully',
      data: newVersion
    });
  } catch (error) {
    console.error('Restore website content version error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while restoring website content version',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/website-content/current
 * @desc    Get the current website content
 * @access  Private (Admin only)
 */
router.get('/current', authenticate, isAdmin, async (req, res) => {
  try {
    // Get the latest version of website content
    const content = await WebsiteContent.findOne().sort({ version: -1 });

    if (!content) {
      // If no content exists, create a default template
      const defaultContent = new WebsiteContent({
        sections: [
          {
            sectionId: 'hero',
            sectionName: 'Hero Section',
            title: 'Supercharge Your Microstock Earnings with AI-Powered Metadata',
            subtitle: 'Generate high-quality titles, keywords, and descriptions for your images, vectors, and videos using advanced AI technology.',
            buttonText: 'Get Started',
            buttonUrl: '#pricing',
            order: 1,
            isActive: true
          },
          {
            sectionId: 'about',
            sectionName: 'About Section',
            title: 'About Meta Master',
            content: 'Meta Master is the ultimate tool for microstock contributors, helping you generate perfect metadata for your content.',
            order: 2,
            isActive: true
          }
        ],
        features: [
          {
            title: 'AI-Powered Metadata Generation',
            description: 'Leverages Google\'s Gemini AI to generate high-quality, relevant metadata that helps your content get discovered.',
            icon: 'fas fa-robot',
            order: 1,
            isActive: true
          },
          {
            title: 'Multi-Format Support',
            description: 'Process images (JPG, PNG), vector files (SVG, EPS, AI), and videos (MP4, MOV, AVI, MKV) with ease.',
            icon: 'fas fa-file-image',
            order: 2,
            isActive: true
          }
        ],
        faqs: [
          {
            question: 'What is Meta Master?',
            answer: 'Meta Master is an AI-powered metadata generator designed specifically for microstock contributors. It automatically generates titles, keywords, and descriptions for images, vectors, and videos.',
            order: 1,
            isActive: true
          },
          {
            question: 'How does Meta Master work?',
            answer: 'Meta Master uses advanced AI to analyze your content and generate relevant metadata. Simply upload your file, and the AI will create optimized titles, descriptions, and keywords.',
            order: 2,
            isActive: true
          }
        ],
        testimonials: [
          {
            name: 'John Doe',
            role: 'Professional Photographer',
            content: 'Meta Master has completely transformed my workflow. I\'ve seen a 40% increase in my acceptance rate since I started using it.',
            rating: 5,
            order: 1,
            isActive: true
          }
        ],
        updatedBy: req.user.id
      });

      await defaultContent.save();
      return res.status(200).json({
        success: true,
        data: defaultContent
      });
    }

    return res.status(200).json({
      success: true,
      data: content
    });
  } catch (error) {
    console.error('Get website content error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving website content',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/website-content/published
 * @desc    Get the published website content
 * @access  Public
 */
router.get('/published', async (req, res) => {
  try {
    // Get the latest published version of website content
    const content = await WebsiteContent.findOne({ isPublished: true }).sort({ publishedAt: -1 });

    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'No published content found'
      });
    }

    return res.status(200).json({
      success: true,
      data: content
    });
  } catch (error) {
    console.error('Get published website content error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving published website content',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   PUT /api/website-content/update
 * @desc    Update website content
 * @access  Private (Admin only)
 */
router.put('/update', authenticate, isAdmin, async (req, res) => {
  try {
    const { contentId, sections, features, faqs, testimonials } = req.body;

    // Find the content to update
    const content = await WebsiteContent.findById(contentId);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }

    // Update the content
    if (sections) content.sections = sections;
    if (features) content.features = features;
    if (faqs) content.faqs = faqs;
    if (testimonials) content.testimonials = testimonials;

    content.updatedBy = req.user.id;

    await content.save();

    return res.status(200).json({
      success: true,
      message: 'Website content updated successfully',
      data: content
    });
  } catch (error) {
    console.error('Update website content error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while updating website content',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/website-content/publish
 * @desc    Publish website content
 * @access  Private (Admin only)
 */
router.post('/publish', authenticate, isAdmin, async (req, res) => {
  try {
    const { contentId } = req.body;

    // Find the content to publish
    const content = await WebsiteContent.findById(contentId);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }

    // Create a new version for publishing
    const newVersion = new WebsiteContent({
      version: content.version + 1,
      isPublished: true,
      publishedAt: Date.now(),
      sections: content.sections,
      features: content.features,
      faqs: content.faqs,
      testimonials: content.testimonials,
      updatedBy: req.user.id
    });

    await newVersion.save();

    return res.status(200).json({
      success: true,
      message: 'Website content published successfully',
      data: newVersion
    });
  } catch (error) {
    console.error('Publish website content error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while publishing website content',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/website-content/upload-image
 * @desc    Upload an image for website content
 * @access  Private (Admin only)
 */
router.post('/upload-image', authenticate, isAdmin, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No image file provided'
      });
    }

    // Return the URL to the uploaded file
    const imageUrl = `/uploads/${req.file.filename}`;

    return res.status(200).json({
      success: true,
      message: 'Image uploaded successfully',
      data: { imageUrl }
    });
  } catch (error) {
    console.error('Upload image error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while uploading image',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/website-content/upload-video
 * @desc    Upload a video for website content
 * @access  Private (Admin only)
 */
router.post('/upload-video', authenticate, isAdmin, upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No video file provided'
      });
    }

    // Return the URL to the uploaded file
    const videoUrl = `/uploads/${req.file.filename}`;
    const fileSize = req.file.size;
    const fileType = req.file.mimetype;
    const fileName = req.file.originalname;

    return res.status(200).json({
      success: true,
      message: 'Video uploaded successfully',
      data: {
        videoUrl,
        fileSize,
        fileType,
        fileName
      }
    });
  } catch (error) {
    console.error('Upload video error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while uploading video',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
