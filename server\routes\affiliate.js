const express = require('express');
const router = express.Router();
const User = require('../models/mysql/User');
const AffiliateTransaction = require('../models/mysql/Affiliate');
const Withdrawal = require('../models/mysql/Withdrawal');
const { authenticate, isAdmin } = require('../middleware/auth');

/**
 * Helper function to calculate available balance
 * @param {string} userId - User ID
 * @returns {number} Available balance
 */
async function calculateAvailableBalance(userId) {
  try {
    console.log(`Calculating available balance for user ID: ${userId}`);

    // Get user
    let user;
    try {
      user = await User.findById(userId);
      if (!user) {
        console.log(`User not found with ID: ${userId}`);
        return 0;
      }
    } catch (userError) {
      console.error(`Error finding user with ID ${userId}:`, userError);
      return 0;
    }

    // Get total earnings and withdrawn amount
    const totalEarnings = user.affiliateCommission || 0;
    const withdrawnCommission = user.withdrawnCommission || 0;
    console.log(`User ${user.username} - Total earnings: ${totalEarnings}, Withdrawn: ${withdrawnCommission}`);

    // Get pending withdrawal requests
    let pendingWithdrawals = [];
    try {
      pendingWithdrawals = await Withdrawal.find({
        userId: userId,
        status: 'pending'
      });
      console.log(`Found ${pendingWithdrawals.length} pending withdrawals for user ${user.username}`);
    } catch (withdrawalError) {
      console.error(`Error finding pending withdrawals for user ${user.username}:`, withdrawalError);
      pendingWithdrawals = [];
    }

    // Calculate total pending withdrawal amount
    const pendingWithdrawalAmount = pendingWithdrawals.reduce(
      (sum, withdrawal) => sum + (typeof withdrawal.amount === 'number' ? withdrawal.amount : 0),
      0
    );
    console.log(`User ${user.username} - Pending withdrawal amount: ${pendingWithdrawalAmount}`);

    // Available balance = total earnings - withdrawn - pending withdrawals
    const availableBalance = totalEarnings - withdrawnCommission - pendingWithdrawalAmount;
    console.log(`User ${user.username} - Available balance: ${availableBalance}`);

    return availableBalance;
  } catch (error) {
    console.error('Error calculating available balance:', error);
    return 0;
  }
}

/**
 * @route   GET /api/affiliate/stats
 * @desc    Get affiliate stats for the current user
 * @access  Private
 */
router.get('/stats', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with affiliate info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all affiliate transactions for this user
    const transactions = await AffiliateTransaction.find({ referrerId: userId }, { createdAt: -1 });

    // Get affiliate stats from the AffiliateTransaction model
    const stats = await AffiliateTransaction.getAffiliateStats(userId);

    // Calculate stats
    const totalEarnings = user.affiliateCommission || 0;
    const withdrawnCommission = user.withdrawnCommission || 0;
    const availableBalance = await calculateAvailableBalance(userId);

    const totalReferrals = stats.totalReferrals;
    const pendingCommissions = stats.pendingCommission;

    // Return affiliate stats
    return res.status(200).json({
      success: true,
      data: {
        affiliateId: user.affiliateId,
        totalEarnings,
        withdrawnCommission,
        availableBalance,
        totalReferrals,
        pendingCommissions,
        transactions
      }
    });
  } catch (error) {
    console.error('Get affiliate stats error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving affiliate stats',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate/link
 * @desc    Get affiliate link for the current user
 * @access  Private
 */
router.get('/link', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with affiliate info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Make sure user has an affiliate ID
    if (!user.affiliateId) {
      // Generate a new affiliate ID if not present
      // Remove any special characters and spaces, and convert to lowercase
      const cleanUsername = user.username.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      await User.update(user.id, { affiliateId: cleanUsername });
      user.affiliateId = cleanUsername;
    }

    // Construct the affiliate link
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const affiliateLink = `${baseUrl}/?ref=${user.username}`;

    // Return affiliate link
    return res.status(200).json({
      success: true,
      data: {
        affiliateId: user.affiliateId,
        affiliateLink
      }
    });
  } catch (error) {
    console.error('Get affiliate link error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving affiliate link',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate/admin/leaderboard
 * @desc    Get all affiliates with their stats for admin
 * @access  Private (Admin only)
 */
router.get('/admin/leaderboard', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('Fetching affiliate leaderboard data...');

    // Get all users who have affiliate data (either have referred someone or have commission)
    // Using direct SQL query to get users with affiliate commission or withdrawn commission > 0
    const { query } = require('../../config/db');

    // Variable to store query results
    let rows = [];

    // First, check if the users table exists
    try {
      console.log('Checking if users table exists...');
      const [tables] = await query('SHOW TABLES LIKE "users"');
      if (tables.length === 0) {
        console.error('users table does not exist');
        return res.status(200).json({
          success: true,
          count: 0,
          data: []
        });
      }
    } catch (tableError) {
      console.error('Error checking if users table exists:', tableError);
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // Check if the affiliate_commission and withdrawn_commission columns exist
    try {
      console.log('Checking database schema...');
      const [columns] = await query('SHOW COLUMNS FROM users');
      const columnNames = columns.map(col => col.Field);
      console.log('Available columns in users table:', columnNames);

      // Build the query based on available columns
      let sqlQuery = 'SELECT id, username, name, email';

      // Check if affiliate_id column exists
      if (columnNames.includes('affiliate_id')) {
        sqlQuery += ', affiliate_id';
      } else {
        console.log('Warning: affiliate_id column not found in users table');
        sqlQuery += ', "" as affiliate_id';
      }

      if (columnNames.includes('affiliate_commission')) {
        sqlQuery += ', affiliate_commission';
      } else {
        console.log('Warning: affiliate_commission column not found in users table');
        sqlQuery += ', 0 as affiliate_commission';
      }

      if (columnNames.includes('withdrawn_commission')) {
        sqlQuery += ', withdrawn_commission';
      } else {
        console.log('Warning: withdrawn_commission column not found in users table');
        sqlQuery += ', 0 as withdrawn_commission';
      }

      sqlQuery += ', created_at FROM users';

      // Add WHERE clause if the columns exist
      if (columnNames.includes('affiliate_commission') || columnNames.includes('withdrawn_commission')) {
        let whereClause = [];
        if (columnNames.includes('affiliate_commission')) {
          whereClause.push('affiliate_commission > 0');
        }
        if (columnNames.includes('withdrawn_commission')) {
          whereClause.push('withdrawn_commission > 0');
        }
        if (whereClause.length > 0) {
          sqlQuery += ' WHERE ' + whereClause.join(' OR ');
        }
      }

      console.log('Executing SQL query:', sqlQuery);
      const [result] = await query(sqlQuery);
      rows = result;
      console.log('Query returned', rows.length, 'rows');
    } catch (error) {
      console.error('Error checking database schema:', error);
      // Fallback to a simpler query
      try {
        console.log('Falling back to simpler query...');
        const [result] = await query('SELECT id, username, name, email, "" as affiliate_id, 0 as affiliate_commission, 0 as withdrawn_commission, created_at FROM users LIMIT 50');
        rows = result;
      } catch (fallbackError) {
        console.error('Error with fallback query:', fallbackError);
        // Return empty array if all queries fail
        return res.status(200).json({
          success: true,
          count: 0,
          data: []
        });
      }
    }

    console.log(`Found ${rows.length} users with affiliate data`);

    // If no users found, return empty array
    if (rows.length === 0) {
      console.log('No users with affiliate data found, returning empty array');
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // Format users to match the expected format
    const users = rows.map(user => ({
      id: user.id,
      username: user.username || '',
      name: user.name || '',
      email: user.email || '',
      affiliateId: user.affiliate_id || '',
      affiliateCommission: parseFloat(user.affiliate_commission) || 0,
      withdrawnCommission: parseFloat(user.withdrawn_commission) || 0,
      createdAt: user.created_at
    }));

    // For each user, get their referral count
    const usersWithReferrals = await Promise.all(users.map(async (user) => {
      console.log(`Processing user ${user.username} (ID: ${user.id})`);

      // Get all transactions for this user
      let transactions = [];
      try {
        transactions = await AffiliateTransaction.find({ referrerId: user.id });
        console.log(`Found ${transactions.length} transactions for user ${user.username}`);
      } catch (error) {
        console.error(`Error fetching transactions for user ${user.username}:`, error);
        transactions = [];
      }

      // Count transactions by status
      const pendingCount = transactions.filter(t => t.status === 'pending').length;
      const approvedCount = transactions.filter(t => t.status === 'approved').length;
      const rejectedCount = transactions.filter(t => t.status === 'rejected').length;
      const paidCount = transactions.filter(t => t.status === 'paid').length;

      // Calculate available balance using helper function
      let availableBalance = 0;
      try {
        availableBalance = await calculateAvailableBalance(user.id);
      } catch (error) {
        console.error(`Error calculating available balance for user ${user.username}:`, error);
        availableBalance = 0;
      }

      return {
        _id: user.id,
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        affiliateId: user.affiliateId,
        totalReferrals: transactions.length,
        pendingReferrals: pendingCount,
        approvedReferrals: approvedCount,
        rejectedReferrals: rejectedCount,
        paidReferrals: paidCount,
        totalEarnings: user.affiliateCommission || 0,
        withdrawnAmount: user.withdrawnCommission || 0,
        availableBalance: availableBalance,
        joinedAt: user.createdAt
      };
    }));

    // Sort by total earnings (highest first)
    usersWithReferrals.sort((a, b) => {
      // Ensure we're comparing numbers
      const aEarnings = parseFloat(a.totalEarnings) || 0;
      const bEarnings = parseFloat(b.totalEarnings) || 0;
      return bEarnings - aEarnings;
    });

    console.log(`Returning ${usersWithReferrals.length} users with referral data`);

    // Add timestamp to help prevent caching issues
    return res.status(200).json({
      success: true,
      count: usersWithReferrals.length,
      data: usersWithReferrals,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Get affiliate leaderboard error:', error);
    // Return empty data instead of error to prevent breaking the frontend
    return res.status(200).json({
      success: true,
      count: 0,
      data: []
    });
  }
});

/**
 * @route   GET /api/affiliate/admin/transactions/:userId
 * @desc    Get all affiliate transactions for a specific user (admin view)
 * @access  Private (Admin only)
 */
router.get('/admin/transactions/:userId', authenticate, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    console.log('Fetching affiliate transactions for user ID:', userId);

    // Check if userId is valid
    if (!userId) {
      console.log('Invalid user ID provided');
      return res.status(200).json({
        success: true,
        count: 0,
        user: null,
        data: []
      });
    }

    // Get user info
    let user;
    try {
      user = await User.findById(userId);
      if (!user) {
        console.log('User not found with ID:', userId);
        return res.status(200).json({
          success: true,
          count: 0,
          user: null,
          data: []
        });
      }
      console.log('Found user:', user.username);
    } catch (userError) {
      console.error('Error finding user:', userError);
      return res.status(200).json({
        success: true,
        count: 0,
        user: null,
        data: []
      });
    }

    // Get all transactions for this user with detailed information
    let transactions = [];
    try {
      transactions = await AffiliateTransaction.find({ referrerId: userId }, { createdAt: -1 });
      console.log(`Found ${transactions.length} transactions for user ${user.username}`);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      console.log('Returning empty transactions array');
      transactions = [];
    }

    // Calculate available balance
    let availableBalance = 0;
    try {
      availableBalance = await calculateAvailableBalance(user.id);
      console.log(`User ${user.username} available balance: ${availableBalance}`);
    } catch (error) {
      console.error(`Error calculating available balance for user ${user.username}:`, error);
      console.log('Using default available balance of 0');
    }

    // Enhance transaction data with referred user and payment information
    const enhancedTransactions = await Promise.all(transactions.map(async (transaction) => {
      // Get referred user info if available
      let referredUser = null;
      if (transaction.referredUserId) {
        try {
          referredUser = await User.findById(transaction.referredUserId);
        } catch (err) {
          console.error('Error fetching referred user:', err);
        }
      }

      // Create a safe transaction object with default values for missing properties
      const safeTransaction = {
        ...transaction,
        id: transaction.id || 0,
        referrerId: transaction.referrerId || 0,
        referredUserId: transaction.referredUserId || 0,
        paymentId: transaction.paymentId || 0,
        originalAmount: transaction.originalAmount || 0,
        commissionAmount: transaction.commissionAmount || 0,
        commissionRate: transaction.commissionRate || 0.2,
        status: transaction.status || 'pending',
        plan: transaction.plan || 'Unknown',
        createdAt: transaction.createdAt || new Date(),
        updatedAt: transaction.updatedAt || new Date(),
        _id: transaction.id || 0
      };

      return {
        ...safeTransaction,
        referredUser: referredUser ? {
          id: referredUser.id,
          username: referredUser.username || '',
          name: referredUser.name || '',
          email: referredUser.email || '',
          firstName: referredUser.name ? referredUser.name.split(' ')[0] : '',
          lastName: referredUser.name ? referredUser.name.split(' ').slice(1).join(' ') || '' : ''
        } : null
      };
    }));

    console.log('Returning enhanced transaction data');
    return res.status(200).json({
      success: true,
      count: enhancedTransactions.length,
      user: {
        _id: user.id,
        id: user.id,
        username: user.username || '',
        name: user.name || '',
        email: user.email || '',
        affiliateId: user.affiliateId || '',
        totalEarnings: parseFloat(user.affiliateCommission) || 0,
        withdrawnAmount: parseFloat(user.withdrawnCommission) || 0,
        availableBalance: availableBalance
      },
      data: enhancedTransactions
    });
  } catch (error) {
    console.error('Get user affiliate transactions error:', error);
    // Return empty data instead of error to prevent breaking the frontend
    return res.status(200).json({
      success: true,
      count: 0,
      user: null,
      data: []
    });
  }
});

/**
 * @route   POST /api/affiliate/admin/transaction/:transactionId/approve
 * @desc    Approve an affiliate transaction
 * @access  Private (Admin only)
 */
router.post('/admin/transaction/:transactionId/approve', authenticate, isAdmin, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { adminNotes } = req.body;

    console.log(`Approving affiliate transaction with ID: ${transactionId}`);

    // Check if transactionId is valid
    if (!transactionId) {
      console.log('Invalid transaction ID provided');
      return res.status(400).json({
        success: false,
        message: 'Invalid transaction ID'
      });
    }

    // Find the transaction
    let transaction;
    try {
      transaction = await AffiliateTransaction.findById(transactionId);
      if (!transaction) {
        console.log(`Transaction not found with ID: ${transactionId}`);
        return res.status(404).json({
          success: false,
          message: 'Affiliate transaction not found'
        });
      }
    } catch (findError) {
      console.error('Error finding transaction:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding transaction'
      });
    }

    console.log(`Found transaction with ID ${transactionId}, status: ${transaction.status}`);

    // Check if transaction is already approved or rejected
    if (transaction.status !== 'pending') {
      console.log(`Transaction ${transactionId} is already ${transaction.status}`);
      return res.status(400).json({
        success: false,
        message: `Transaction is already ${transaction.status}`
      });
    }

    // Update transaction status
    try {
      await AffiliateTransaction.update(transaction.id, {
        status: 'approved',
        updatedAt: new Date()
      });
      console.log(`Updated transaction ${transactionId} status to approved`);
    } catch (updateError) {
      console.error('Error updating transaction status:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Error updating transaction status'
      });
    }

    // Update user's affiliate commission
    try {
      const user = await User.findById(transaction.referrerId);
      if (user) {
        // Make sure the commission amount is a valid number
        const commissionAmount = parseFloat(transaction.commissionAmount);
        if (!isNaN(commissionAmount) && commissionAmount > 0) {
          await User.updateAffiliateCommission(transaction.referrerId, commissionAmount);
          console.log(`Updated user ${user.username} affiliate commission by ${commissionAmount}`);
        } else {
          console.error(`Invalid commission amount: ${transaction.commissionAmount}`);
        }
      } else {
        console.log(`User not found with ID: ${transaction.referrerId}`);
      }
    } catch (userError) {
      console.error('Error updating user affiliate commission:', userError);
      // Continue even if user update fails
    }

    return res.status(200).json({
      success: true,
      message: 'Affiliate transaction approved successfully',
      data: transaction
    });
  } catch (error) {
    console.error('Approve affiliate transaction error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while approving affiliate transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate/admin/transaction/:transactionId/reject
 * @desc    Reject an affiliate transaction
 * @access  Private (Admin only)
 */
router.post('/admin/transaction/:transactionId/reject', authenticate, isAdmin, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { adminNotes } = req.body;

    console.log(`Rejecting affiliate transaction with ID: ${transactionId}`);

    // Check if transactionId is valid
    if (!transactionId) {
      console.log('Invalid transaction ID provided');
      return res.status(400).json({
        success: false,
        message: 'Invalid transaction ID'
      });
    }

    // Find the transaction
    let transaction;
    try {
      transaction = await AffiliateTransaction.findById(transactionId);
      if (!transaction) {
        console.log(`Transaction not found with ID: ${transactionId}`);
        return res.status(404).json({
          success: false,
          message: 'Affiliate transaction not found'
        });
      }
    } catch (findError) {
      console.error('Error finding transaction:', findError);
      return res.status(500).json({
        success: false,
        message: 'Error finding transaction'
      });
    }

    console.log(`Found transaction with ID ${transactionId}, status: ${transaction.status}`);

    // Check if transaction is already approved or rejected
    if (transaction.status !== 'pending') {
      console.log(`Transaction ${transactionId} is already ${transaction.status}`);
      return res.status(400).json({
        success: false,
        message: `Transaction is already ${transaction.status}`
      });
    }

    // Update transaction status
    try {
      await AffiliateTransaction.update(transaction.id, {
        status: 'rejected',
        updatedAt: new Date()
      });
      console.log(`Updated transaction ${transactionId} status to rejected`);
    } catch (updateError) {
      console.error('Error updating transaction status:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Error updating transaction status'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Affiliate transaction rejected successfully',
      data: transaction
    });
  } catch (error) {
    console.error('Reject affiliate transaction error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while rejecting affiliate transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate/fix-balance
 * @desc    Fix user's affiliate balance
 * @access  Private
 */
router.post('/fix-balance', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`Fixing affiliate balance for user ${userId}`);

    // Get user
    const user = await User.findById(userId);
    if (!user) {
      console.log(`User not found with ID: ${userId}`);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    console.log(`Found user: ${user.username}`);

    // Get all approved affiliate transactions
    const transactions = await AffiliateTransaction.find({
      referrerId: userId,
      status: 'approved'
    });

    console.log(`Found ${transactions.length} approved transactions`);

    // Calculate total earnings from transactions
    let totalEarnings = 0;
    for (const transaction of transactions) {
      const amount = parseFloat(transaction.commissionAmount) || 0;
      totalEarnings += amount;
      console.log(`Transaction ${transaction.id}: ${amount}`);
    }

    console.log(`Total earnings from transactions: ${totalEarnings}`);

    // Get all paid withdrawals
    const paidWithdrawals = await Withdrawal.find({
      userId: userId,
      status: 'paid'
    });

    console.log(`Found ${paidWithdrawals.length} paid withdrawals`);

    // Calculate total withdrawn amount
    let totalWithdrawn = 0;
    for (const withdrawal of paidWithdrawals) {
      const amount = parseFloat(withdrawal.amount) || 0;
      totalWithdrawn += amount;
    }

    console.log(`Total withdrawn: ${totalWithdrawn}`);

    // Update user's balance
    console.log(`Updating user balance to: earnings=${totalEarnings}, withdrawn=${totalWithdrawn}`);
    await User.update(userId, {
      affiliateCommission: totalEarnings,
      withdrawnCommission: totalWithdrawn
    });

    // Calculate available balance
    const availableBalance = await calculateAvailableBalance(userId);
    console.log(`Available balance: ${availableBalance}`);

    return res.status(200).json({
      success: true,
      message: 'Affiliate balance fixed successfully',
      data: {
        totalEarnings,
        totalWithdrawn,
        availableBalance
      }
    });
  } catch (error) {
    console.error('Fix affiliate balance error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while fixing affiliate balance',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
