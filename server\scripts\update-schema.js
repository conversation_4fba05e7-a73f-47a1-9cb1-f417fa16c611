require('dotenv').config();
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function updateSchema() {
  let connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'Jayed123#@!',
      database: process.env.DB_NAME || 'meta_master'
    });

    console.log('Connected to MySQL server');

    // Create license_keys table directly
    const createLicenseKeysTable = `
    CREATE TABLE IF NOT EXISTS license_keys (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      payment_id INT NOT NULL,
      license_key VARCHAR(255) DEFAULT '',
      plan ENUM('Monthly', 'Yearly', 'Lifetime') NOT NULL,
      purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      expiry_date DATETIME DEFAULT NULL,
      status ENUM('active', 'expired', 'revoked', 'pending') DEFAULT 'pending',
      payment_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
    )`;

    // Execute the create table statement
    await connection.query(createLicenseKeysTable);
    console.log('License keys table created successfully');

  } catch (error) {
    console.error('Error updating schema:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

updateSchema();
