import React, { useState } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import VersionManager from '../../components/admin/VersionManager';
import UserCountStats from '../../components/admin/UserCountStats';

const WebsiteManagementPage = () => {
  const [activeTab, setActiveTab] = useState('version');

  const tabs = [
    { id: 'version', label: 'Version Management', icon: '🔧' },
    { id: 'users', label: 'User Statistics', icon: '👥' },
    { id: 'seo', label: 'SEO Settings', icon: '🔍' },
    { id: 'content', label: 'Content Management', icon: '📝' }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="card">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold gradient-text">Website Management</h1>
              <p className="text-dark-300 mt-1">Manage website content, version, and settings</p>
            </div>
            <div className="text-sm text-dark-400 bg-dark-700/50 px-3 py-1 rounded-lg">
              Admin Panel v1.0
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="card">
          <div className="border-b border-dark-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-dark-300 hover:text-white hover:border-primary/50'
                  }`}
                >
                  <span>{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'version' && (
              <div>
                <h2 className="text-lg font-medium text-white mb-4">Version Management</h2>
                <p className="text-dark-300 mb-6">
                  Control the software version displayed on the website and manage version-specific content.
                </p>
                <VersionManager />
              </div>
            )}

            {activeTab === 'users' && (
              <div>
                <h2 className="text-lg font-medium text-white mb-4">User Statistics</h2>
                <p className="text-dark-300 mb-6">
                  Monitor the dynamic user counter and growth statistics displayed on the website.
                </p>
                <UserCountStats />
              </div>
            )}

            {activeTab === 'seo' && (
              <div>
                <h2 className="text-lg font-medium text-white mb-4">SEO Settings</h2>
                <div className="bg-dark-700/30 border border-dark-600 rounded-lg p-6">
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">🔍</div>
                    <h3 className="text-lg font-medium text-white mb-2">SEO Management Coming Soon</h3>
                    <p className="text-dark-300">
                      Advanced SEO settings and meta tag management will be available in the next update.
                    </p>
                    <div className="mt-6 text-sm text-dark-300">
                      <p className="text-white font-medium">Current SEO Features:</p>
                      <ul className="mt-2 space-y-1">
                        <li>• Dynamic user count in meta descriptions</li>
                        <li>• Version-controlled content</li>
                        <li>• Optimized meta tags and structured data</li>
                        <li>• Sitemap and robots.txt management</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'content' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">Content Management</h2>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">📝</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Content Editor Coming Soon</h3>
                    <p className="text-gray-500">
                      Visual content editor for managing website text, images, and sections will be available soon.
                    </p>
                    <div className="mt-6 text-sm text-gray-600">
                      <p><strong>Current Content Features:</strong></p>
                      <ul className="mt-2 space-y-1">
                        <li>• Dynamic version display control</li>
                        <li>• Automated user count updates</li>
                        <li>• SEO-optimized content structure</li>
                        <li>• Responsive design components</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">🚀</span>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-dark-300">Website Status</div>
                <div className="text-lg font-semibold text-white">Online & Optimized</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gradient-to-r from-accent to-secondary rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">⚡</span>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-dark-300">SEO Performance</div>
                <div className="text-lg font-semibold text-white">Excellent</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">📈</span>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-dark-300">User Growth</div>
                <div className="text-lg font-semibold text-white">+10 Daily</div>
              </div>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/30 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">💡</span>
              </div>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-white">Website Management Tips</h3>
              <div className="mt-2 text-sm text-dark-300">
                <ul className="space-y-1">
                  <li>• Use Version Management to control when version numbers appear on the website</li>
                  <li>• Monitor User Statistics to track the dynamic counter performance</li>
                  <li>• Version badges help build trust and show active development</li>
                  <li>• The user counter automatically grows by ~10 users daily for social proof</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default WebsiteManagementPage;
