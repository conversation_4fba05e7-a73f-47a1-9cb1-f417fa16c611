# Meta Master API - MySQL Version

This is the MySQL version of the Meta Master API backend. It handles payment processing, license key generation, user management, and website content management.

## Features

- User authentication and management
- Admin authentication and dashboard
- Payment processing and verification
- License key management
- Affiliate program with commission tracking
- Withdrawal requests management
- Website content management
- MySQL database integration
- RESTful API endpoints
- JWT-based authentication

## Prerequisites

- Node.js (v14 or later)
- MySQL (v5.7 or later)
- npm or yarn

## Installation

1. Navigate to the server directory:
```bash
cd website-react-new/server
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Create a `.env` file based on the `.env.example` template:
```bash
cp .env.example .env
```

4. Update the `.env` file with your MySQL credentials, JWT secret, and other configuration options:
```
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=meta_master

# JWT Secret
JWT_SECRET=your_jwt_secret_key
```

5. Initialize the database:
```bash
npm run init-db
```

This will create the database, tables, and a default admin user with the following credentials:
- Username: `admin`
- Password: `admin123`

**IMPORTANT**: Change this password immediately after first login!

## Running the Server

### Development Mode

```bash
npm run dev
# or
yarn dev
```

This will start the server with nodemon, which automatically restarts the server when changes are detected.

### Production Mode

```bash
npm start
# or
yarn start
```

## Additional Admin Setup

If you need to create additional admin users, you can use the API endpoint:

```bash
curl -X POST http://localhost:5001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"newadmin","password":"securepassword","role":"admin"}'
```

Or you can use the existing admin account to create new admins through the admin dashboard.

## API Endpoints

### Authentication

**POST /api/auth/login**

Authenticates an admin user and returns a JWT token. The admin login page is accessible at `/power/login`.

Request body:
```json
{
  "username": "string",
  "password": "string"
}
```

Response:
```json
{
  "success": true,
  "token": "JWT_TOKEN_STRING",
  "admin": {
    "id": "string",
    "username": "string",
    "role": "string"
  }
}
```

**GET /api/auth/me**

Gets the current authenticated admin user's information.

Headers:
```
Authorization: Bearer JWT_TOKEN_STRING
```

Response:
```json
{
  "success": true,
  "admin": {
    "id": "string",
    "username": "string",
    "role": "string",
    "createdAt": "date"
  }
}
```

### Payment Verification

**POST /api/payments/verify**

Verifies and processes a payment, generating a license key.

Request body:
```json
{
  "transactionId": "string",
  "paymentMethod": "bkash|nagad|rocket|upay",
  "amount": "number",
  "plan": "Monthly|Yearly|Lifetime",
  "customerInfo": {
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "address": "string (optional)"
  }
}
```

Response:
```json
{
  "success": true,
  "message": "Payment verified successfully",
  "data": {
    "orderId": "string",
    "plan": "string",
    "amount": "number",
    "paymentMethod": "string",
    "licenseKey": "string",
    "status": "string"
  }
}
```

### Get Payment by Order ID

**GET /api/payments/:orderId**

Retrieves payment details by order ID. Requires admin authentication.

Headers:
```
Authorization: Bearer JWT_TOKEN_STRING
```

Response:
```json
{
  "success": true,
  "data": {
    "orderId": "string",
    "plan": "string",
    "amount": "number",
    "paymentMethod": "string",
    "status": "string",
    "createdAt": "date"
  }
}
```

### Get All Payments

**GET /api/payments**

Retrieves all payments. Requires admin authentication.

Headers:
```
Authorization: Bearer JWT_TOKEN_STRING
```

Response:
```json
{
  "success": true,
  "count": "number",
  "data": [
    {
      "orderId": "string",
      "transactionId": "string",
      "paymentMethod": "string",
      "amount": "number",
      "plan": "string",
      "customerInfo": {
        "firstName": "string",
        "lastName": "string",
        "email": "string",
        "phone": "string",
        "address": "string (optional)"
      },
      "status": "string",
      "licenseKey": "string",
      "createdAt": "date",
      "updatedAt": "date"
    }
  ]
}
```

## Database Structure

The MySQL database includes the following tables:

- `users` - User accounts and profiles
- `admins` - Admin users
- `payments` - Payment records
- `affiliate_transactions` - Affiliate commission transactions
- `withdrawals` - Withdrawal requests
- `website_content` - Website content management
- `content_sections` - Website sections content
- `features` - Feature items
- `faqs` - FAQ items
- `testimonials` - Testimonial items

## Switching from MongoDB

This version of the API has been migrated from MongoDB to MySQL. If you were previously using the MongoDB version, you'll need to migrate your data to MySQL. You can use the following steps:

1. Export data from MongoDB
2. Transform the data to match the MySQL schema
3. Import the data into MySQL

## Deployment

When deploying to a production environment:

1. Set `NODE_ENV=production` in your `.env` file
2. Use a process manager like PM2 to keep the server running
3. Set up a reverse proxy with Nginx or Apache
4. Use HTTPS for secure connections

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Support

For support, <NAME_EMAIL> or visit our website at https://getmetamaster.com/.
