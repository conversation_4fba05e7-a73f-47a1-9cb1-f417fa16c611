/**
 * <PERSON><PERSON><PERSON> to add customer information columns to payments table
 * Run with: node scripts/add-customer-info-columns.js
 */

const { query } = require('../config/db');

async function addCustomerInfoColumns() {
  try {
    console.log('Checking if customer information columns exist in payments table...');

    // Check if customer_name column exists
    const [columns] = await query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'payments' 
      AND COLUMN_NAME = 'customer_name'
    `);

    if (columns.length === 0) {
      console.log('Customer information columns do not exist. Adding them...');

      // Add customer information columns
      await query(`
        ALTER TABLE payments 
        ADD COLUMN customer_name VARCHAR(200) DEFAULT NULL,
        ADD COLUMN customer_email VARCHAR(100) DEFAULT NULL,
        ADD COLUMN customer_phone VARCHAR(20) DEFAULT NULL
      `);

      console.log('Customer information columns added successfully!');
    } else {
      console.log('Customer information columns already exist.');
    }

    console.log('<PERSON><PERSON><PERSON> completed successfully.');
  } catch (error) {
    console.error('Error adding customer information columns:', error);
  }
}

addCustomerInfoColumns();
