# Meta Master API Server

This is the backend API server for the Meta Master website. It handles payment processing, license key generation, and data storage.

## Features

- Payment verification and processing
- License key generation
- MongoDB integration for data storage
- RESTful API endpoints
- JWT-based admin authentication

## Prerequisites

- Node.js (v14 or later)
- MongoDB (local installation or MongoDB Atlas account)
- npm or yarn

## Installation

1. Navigate to the server directory:
```bash
cd website-react-new/server
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Create a `.env` file based on the `.env.example` template:
```bash
cp .env.example .env
```

4. Update the `.env` file with your MongoDB connection string, JWT secret, and other configuration options.

## Running the Server

### Development Mode

```bash
npm run dev
# or
yarn dev
```

This will start the server with nodemon, which automatically restarts the server when changes are detected.

### Production Mode

```bash
npm start
# or
yarn start
```

## Initial Admin Setup

To create an initial admin user, run the following command:

```bash
node scripts/create-admin.js <username> <password>
```

For example:
```bash
node scripts/create-admin.js admin admin123
```

## API Endpoints

### Authentication

**POST /api/auth/login**

Authenticates an admin user and returns a JWT token. The admin login page is accessible at `/power/login`.

Request body:
```json
{
  "username": "string",
  "password": "string"
}
```

Response:
```json
{
  "success": true,
  "token": "JWT_TOKEN_STRING",
  "admin": {
    "id": "string",
    "username": "string",
    "role": "string"
  }
}
```

**GET /api/auth/me**

Gets the current authenticated admin user's information.

Headers:
```
Authorization: Bearer JWT_TOKEN_STRING
```

Response:
```json
{
  "success": true,
  "admin": {
    "id": "string",
    "username": "string",
    "role": "string",
    "createdAt": "date"
  }
}
```

### Payment Verification

**POST /api/payments/verify**

Verifies and processes a payment, generating a license key.

Request body:
```json
{
  "transactionId": "string",
  "paymentMethod": "bkash|nagad|rocket|upay",
  "amount": "number",
  "plan": "Monthly|Yearly|Lifetime",
  "customerInfo": {
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "address": "string (optional)"
  }
}
```

Response:
```json
{
  "success": true,
  "message": "Payment verified successfully",
  "data": {
    "orderId": "string",
    "plan": "string",
    "amount": "number",
    "paymentMethod": "string",
    "licenseKey": "string",
    "status": "string"
  }
}
```

### Get Payment by Order ID

**GET /api/payments/:orderId**

Retrieves payment details by order ID. Requires admin authentication.

Headers:
```
Authorization: Bearer JWT_TOKEN_STRING
```

Response:
```json
{
  "success": true,
  "data": {
    "orderId": "string",
    "plan": "string",
    "amount": "number",
    "paymentMethod": "string",
    "status": "string",
    "createdAt": "date"
  }
}
```

### Get All Payments

**GET /api/payments**

Retrieves all payments. Requires admin authentication.

Headers:
```
Authorization: Bearer JWT_TOKEN_STRING
```

Response:
```json
{
  "success": true,
  "count": "number",
  "data": [
    {
      "orderId": "string",
      "transactionId": "string",
      "paymentMethod": "string",
      "amount": "number",
      "plan": "string",
      "customerInfo": {
        "firstName": "string",
        "lastName": "string",
        "email": "string",
        "phone": "string",
        "address": "string (optional)"
      },
      "status": "string",
      "licenseKey": "string",
      "createdAt": "date",
      "updatedAt": "date"
    }
  ]
}
```

## Database Schema

### Admin Model

- `username`: Admin username (unique)
- `password`: Hashed admin password
- `role`: Admin role (admin or superadmin)
- `createdAt`: Timestamp when the admin was created

### Payment Model

- `orderId`: Unique order identifier (MM-YEAR-RANDOMNUMBER)
- `transactionId`: Payment transaction ID from the payment provider
- `paymentMethod`: Payment method used (bkash, nagad, rocket, upay)
- `amount`: Payment amount
- `plan`: Subscription plan (Monthly, Yearly, Lifetime)
- `customerInfo`: Customer information (name, email, phone, etc.)
- `status`: Payment status (pending, verified, failed)
- `licenseKey`: Generated license key for the software
- `createdAt`: Timestamp when the record was created
- `updatedAt`: Timestamp when the record was last updated

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
