import { useState } from 'react'
import VersionManager from './VersionManager'
import UserCountStats from './UserCountStats'

type TabType = 'version' | 'users' | 'analytics'

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<TabType>('version')

  const tabs = [
    { id: 'version' as TabType, label: 'Version Management', icon: '🔧' },
    { id: 'users' as TabType, label: 'User Statistics', icon: '👥' },
    { id: 'analytics' as TabType, label: 'Analytics', icon: '📊' }
  ]

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">Meta Master Admin Dashboard</h1>
            <div className="text-sm text-gray-500">
              Admin Panel v1.0
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tab Navigation */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'version' && (
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">Version Management</h2>
              <VersionManager />
            </div>
          )}

          {activeTab === 'users' && (
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">User Statistics</h2>
              <UserCountStats />
            </div>
          )}

          {activeTab === 'analytics' && (
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">Analytics Overview</h2>
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="text-center py-12">
                  <div className="text-4xl mb-4">📈</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Coming Soon</h3>
                  <p className="text-gray-500">
                    Advanced analytics and reporting features will be available in the next update.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🚀</span>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Website Status</div>
                <div className="text-lg font-semibold text-gray-900">Online</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                  <span className="text-green-600 text-sm">⚡</span>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Performance</div>
                <div className="text-lg font-semibold text-gray-900">Excellent</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                  <span className="text-purple-600 text-sm">🔒</span>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Security</div>
                <div className="text-lg font-semibold text-gray-900">Secure</div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center text-sm text-gray-500">
          <p>Meta Master Admin Dashboard - Manage your website content and settings</p>
        </div>
      </div>
    </div>
  )
}
