const jwt = require('jsonwebtoken');

// Get JWT secret from environment variables or use a default for development
const JWT_SECRET = process.env.JWT_SECRET || 'meta-master-jwt-secret-dev-only';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRE || '30d';

/**
 * Generate a JWT token for an admin user
 * @param {Object} admin - Admin user object
 * @returns {String} JWT token
 */
const generateToken = (admin) => {
  return jwt.sign(
    {
      id: admin.id,
      username: admin.username,
      role: admin.role
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
};

/**
 * Verify a JWT token
 * @param {String} token - JWT token to verify
 * @returns {Object} Decoded token payload or null if invalid
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

module.exports = {
  generateToken,
  verifyToken
};
