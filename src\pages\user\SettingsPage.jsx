import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import {
  Cog6ToothIcon,
  KeyIcon,
  BellIcon,
  ShieldCheckIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function SettingsPage() {
  const { register, handleSubmit, formState: { errors }, reset, getValues } = useForm()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState('')
  const [activeTab, setActiveTab] = useState('account')
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [notificationSettings, setNotificationSettings] = useState({
    emailUpdates: true,
    productNews: true,
    securityAlerts: true,
    promotionalOffers: false
  })
  const navigate = useNavigate()

  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }

        const response = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!response.ok) {
          throw new Error('Failed to fetch user profile')
        }

        const data = await response.json()

        // Set form default values
        reset({
          firstName: data.user.firstName,
          lastName: data.user.lastName,
          email: data.user.email,
          username: data.user.username,
          phone: data.user.phone || '',
          address: data.user.address || ''
        })
      } catch (error) {
        console.error('Error fetching user profile:', error)
        setError('Failed to load user profile. Please try again.')

        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserProfile()
  }, [navigate, reset])

  const onUpdateProfile = async (data) => {
    setIsSaving(true)
    setError(null)
    setSuccessMessage('')

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      const response = await fetch('http://localhost:5001/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          address: data.address
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      setSuccessMessage('Profile updated successfully')

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('')
      }, 3000)
    } catch (error) {
      console.error('Error updating profile:', error)
      setError('Failed to update profile. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const onChangePassword = async (data) => {
    setIsSaving(true)
    setError(null)
    setSuccessMessage('')

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // In a real app, you would call the API to change the password

      setSuccessMessage('Password changed successfully')

      // Clear form
      reset({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('')
      }, 3000)
    } catch (error) {
      console.error('Error changing password:', error)
      setError('Failed to change password. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const onUpdateNotifications = () => {
    setIsSaving(true)
    setError(null)
    setSuccessMessage('')

    try {
      // Simulate API call
      setTimeout(() => {
        setSuccessMessage('Notification preferences updated successfully')

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage('')
        }, 3000)

        setIsSaving(false)
      }, 1000)
    } catch (error) {
      console.error('Error updating notification preferences:', error)
      setError('Failed to update notification preferences. Please try again.')
      setIsSaving(false)
    }
  }

  const handleNotificationChange = (setting) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }))
  }

  return (
    <UserLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-light mb-6">Settings</h1>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="flex flex-col md:flex-row gap-6">
            {/* Settings Tabs */}
            <div className="md:w-64 flex-shrink-0">
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-4">
                <nav className="space-y-1">
                  <button
                    onClick={() => setActiveTab('account')}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg ${
                      activeTab === 'account'
                        ? 'bg-primary/10 text-primary'
                        : 'text-text hover:bg-dark-light hover:text-light'
                    }`}
                  >
                    <Cog6ToothIcon className="mr-3 h-5 w-5" />
                    Account Settings
                  </button>

                  <button
                    onClick={() => setActiveTab('security')}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg ${
                      activeTab === 'security'
                        ? 'bg-primary/10 text-primary'
                        : 'text-text hover:bg-dark-light hover:text-light'
                    }`}
                  >
                    <ShieldCheckIcon className="mr-3 h-5 w-5" />
                    Security
                  </button>

                  <button
                    onClick={() => setActiveTab('notifications')}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg ${
                      activeTab === 'notifications'
                        ? 'bg-primary/10 text-primary'
                        : 'text-text hover:bg-dark-light hover:text-light'
                    }`}
                  >
                    <BellIcon className="mr-3 h-5 w-5" />
                    Notifications
                  </button>

                  <button
                    onClick={() => setActiveTab('licenses')}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg ${
                      activeTab === 'licenses'
                        ? 'bg-primary/10 text-primary'
                        : 'text-text hover:bg-dark-light hover:text-light'
                    }`}
                  >
                    <KeyIcon className="mr-3 h-5 w-5" />
                    License Management
                  </button>
                </nav>
              </div>
            </div>

            {/* Settings Content */}
            <div className="flex-1">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                {error && (
                  <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4 mb-6">
                    {error}
                  </div>
                )}

                {successMessage && (
                  <div className="bg-green-900/10 border border-green-900/20 text-green-500 rounded-lg p-4 mb-6">
                    {successMessage}
                  </div>
                )}

                {/* Account Settings Tab */}
                {activeTab === 'account' && (
                  <>
                    <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                      <Cog6ToothIcon className="w-5 h-5 mr-2 text-primary" />
                      Account Settings
                    </h2>

                    <form onSubmit={handleSubmit(onUpdateProfile)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label htmlFor="firstName" className="block text-sm font-medium text-light mb-1">
                            First Name
                          </label>
                          <input
                            type="text"
                            id="firstName"
                            className={`w-full bg-dark border ${errors.firstName ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                            {...register('firstName', { required: 'First name is required' })}
                          />
                          {errors.firstName && (
                            <p className="mt-1 text-sm text-danger">{errors.firstName.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="lastName" className="block text-sm font-medium text-light mb-1">
                            Last Name
                          </label>
                          <input
                            type="text"
                            id="lastName"
                            className={`w-full bg-dark border ${errors.lastName ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                            {...register('lastName', { required: 'Last name is required' })}
                          />
                          {errors.lastName && (
                            <p className="mt-1 text-sm text-danger">{errors.lastName.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-light mb-1">
                            Email Address
                          </label>
                          <input
                            type="email"
                            id="email"
                            className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                            {...register('email')}
                            disabled
                          />
                          <p className="mt-1 text-xs text-text">Email cannot be changed</p>
                        </div>

                        <div>
                          <label htmlFor="username" className="block text-sm font-medium text-light mb-1">
                            Username
                          </label>
                          <input
                            type="text"
                            id="username"
                            className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                            {...register('username')}
                            disabled
                          />
                          <p className="mt-1 text-xs text-text">Username cannot be changed</p>
                        </div>

                        <div>
                          <label htmlFor="phone" className="block text-sm font-medium text-light mb-1">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            id="phone"
                            className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                            {...register('phone')}
                          />
                        </div>

                        <div className="md:col-span-2">
                          <label htmlFor="address" className="block text-sm font-medium text-light mb-1">
                            Address
                          </label>
                          <textarea
                            id="address"
                            rows="3"
                            className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                            {...register('address')}
                          ></textarea>
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isSaving}
                          className="bg-gradient-to-r from-primary to-secondary text-white font-medium py-3 px-6 rounded-lg hover:opacity-90 transition-opacity disabled:opacity-70 disabled:cursor-not-allowed"
                        >
                          {isSaving ? 'Saving...' : 'Save Changes'}
                        </button>
                      </div>
                    </form>
                  </>
                )}

                {/* Security Tab */}
                {activeTab === 'security' && (
                  <>
                    <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                      <ShieldCheckIcon className="w-5 h-5 mr-2 text-primary" />
                      Security Settings
                    </h2>

                    <form onSubmit={handleSubmit(onChangePassword)} className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="currentPassword" className="block text-sm font-medium text-light mb-1">
                            Current Password
                          </label>
                          <div className="relative">
                            <input
                              type={showCurrentPassword ? 'text' : 'password'}
                              id="currentPassword"
                              className={`w-full bg-dark border ${errors.currentPassword ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                              {...register('currentPassword', { required: 'Current password is required' })}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text hover:text-light"
                              onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            >
                              {showCurrentPassword ? (
                                <EyeSlashIcon className="h-5 w-5" />
                              ) : (
                                <EyeIcon className="h-5 w-5" />
                              )}
                            </button>
                          </div>
                          {errors.currentPassword && (
                            <p className="mt-1 text-sm text-danger">{errors.currentPassword.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="newPassword" className="block text-sm font-medium text-light mb-1">
                            New Password
                          </label>
                          <div className="relative">
                            <input
                              type={showNewPassword ? 'text' : 'password'}
                              id="newPassword"
                              className={`w-full bg-dark border ${errors.newPassword ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                              {...register('newPassword', {
                                required: 'New password is required',
                                minLength: {
                                  value: 6,
                                  message: 'Password must be at least 6 characters'
                                }
                              })}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text hover:text-light"
                              onClick={() => setShowNewPassword(!showNewPassword)}
                            >
                              {showNewPassword ? (
                                <EyeSlashIcon className="h-5 w-5" />
                              ) : (
                                <EyeIcon className="h-5 w-5" />
                              )}
                            </button>
                          </div>
                          {errors.newPassword && (
                            <p className="mt-1 text-sm text-danger">{errors.newPassword.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="confirmPassword" className="block text-sm font-medium text-light mb-1">
                            Confirm New Password
                          </label>
                          <div className="relative">
                            <input
                              type={showConfirmPassword ? 'text' : 'password'}
                              id="confirmPassword"
                              className={`w-full bg-dark border ${errors.confirmPassword ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                              {...register('confirmPassword', {
                                required: 'Please confirm your password',
                                validate: (value) => {
                                  const { newPassword } = getValues();
                                  return newPassword === value || 'Passwords do not match';
                                }
                              })}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text hover:text-light"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? (
                                <EyeSlashIcon className="h-5 w-5" />
                              ) : (
                                <EyeIcon className="h-5 w-5" />
                              )}
                            </button>
                          </div>
                          {errors.confirmPassword && (
                            <p className="mt-1 text-sm text-danger">{errors.confirmPassword.message}</p>
                          )}
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isSaving}
                          className="bg-gradient-to-r from-primary to-secondary text-white font-medium py-3 px-6 rounded-lg hover:opacity-90 transition-opacity disabled:opacity-70 disabled:cursor-not-allowed"
                        >
                          {isSaving ? 'Changing Password...' : 'Change Password'}
                        </button>
                      </div>
                    </form>
                  </>
                )}

                {/* Notifications Tab */}
                {activeTab === 'notifications' && (
                  <>
                    <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                      <BellIcon className="w-5 h-5 mr-2 text-primary" />
                      Notification Preferences
                    </h2>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-dark rounded-lg">
                        <div>
                          <h3 className="text-light font-medium">Email Updates</h3>
                          <p className="text-sm text-text">Receive emails about your account activity</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={notificationSettings.emailUpdates}
                            onChange={() => handleNotificationChange('emailUpdates')}
                          />
                          <div className="w-11 h-6 bg-dark-light peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-dark rounded-lg">
                        <div>
                          <h3 className="text-light font-medium">Product News</h3>
                          <p className="text-sm text-text">Receive updates about new features and improvements</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={notificationSettings.productNews}
                            onChange={() => handleNotificationChange('productNews')}
                          />
                          <div className="w-11 h-6 bg-dark-light peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-dark rounded-lg">
                        <div>
                          <h3 className="text-light font-medium">Security Alerts</h3>
                          <p className="text-sm text-text">Receive notifications about security-related events</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={notificationSettings.securityAlerts}
                            onChange={() => handleNotificationChange('securityAlerts')}
                          />
                          <div className="w-11 h-6 bg-dark-light peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-dark rounded-lg">
                        <div>
                          <h3 className="text-light font-medium">Promotional Offers</h3>
                          <p className="text-sm text-text">Receive special offers and promotions</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={notificationSettings.promotionalOffers}
                            onChange={() => handleNotificationChange('promotionalOffers')}
                          />
                          <div className="w-11 h-6 bg-dark-light peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                        </label>
                      </div>
                    </div>

                    <div className="flex justify-end mt-6">
                      <button
                        onClick={onUpdateNotifications}
                        disabled={isSaving}
                        className="bg-gradient-to-r from-primary to-secondary text-white font-medium py-3 px-6 rounded-lg hover:opacity-90 transition-opacity disabled:opacity-70 disabled:cursor-not-allowed"
                      >
                        {isSaving ? 'Saving...' : 'Save Preferences'}
                      </button>
                    </div>
                  </>
                )}

                {/* License Management Tab */}
                {activeTab === 'licenses' && (
                  <>
                    <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                      <KeyIcon className="w-5 h-5 mr-2 text-primary" />
                      License Management
                    </h2>

                    <div className="text-center py-8">
                      <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                        <KeyIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-light mb-2">Manage Your Licenses</h3>
                        <p className="text-text mb-6">View and manage your license keys in the dedicated licenses section</p>
                        <button
                          onClick={() => navigate('/user/licenses')}
                          className="px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
                        >
                          Go to Licenses
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </motion.div>
            </div>
          </div>
        )}
      </div>
    </UserLayout>
  )
}
