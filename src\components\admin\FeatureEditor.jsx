import React, { useState } from 'react';
import { 
  PencilIcon, 
  TrashIcon, 
  PhotoIcon
} from '@heroicons/react/24/outline';

const FeatureEditor = ({ feature, index, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedFeature, setEditedFeature] = useState({ ...feature });
  const [imageFile, setImageFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEditedFeature({
      ...editedFeature,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSave = () => {
    onUpdate(index, editedFeature);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedFeature({ ...feature });
    setIsEditing(false);
  };

  const handleImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setImageFile(e.target.files[0]);
    }
  };

  const handleImageUpload = async () => {
    if (!imageFile) return;

    setIsUploading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await fetch(`${apiUrl}/api/website-content/upload-image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      setEditedFeature({
        ...editedFeature,
        imageUrl: data.data.imageUrl
      });
      
      setImageFile(null);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert(`Failed to upload image: ${error.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Feature: {feature.title}
          </h3>
        </div>
        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <PencilIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => onDelete(index)}
                className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleSave}
                className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
            </>
          )}
        </div>
      </div>
      {isEditing ? (
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div className="sm:col-span-6">
              <label htmlFor={`title-${index}`} className="block text-sm font-medium text-gray-700">
                Title
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="title"
                  id={`title-${index}`}
                  value={editedFeature.title}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="sm:col-span-6">
              <label htmlFor={`description-${index}`} className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <div className="mt-1">
                <textarea
                  id={`description-${index}`}
                  name="description"
                  rows={3}
                  value={editedFeature.description}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label htmlFor={`icon-${index}`} className="block text-sm font-medium text-gray-700">
                Icon Class (FontAwesome)
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="icon"
                  id={`icon-${index}`}
                  value={editedFeature.icon || ''}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="fas fa-star"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Example: fas fa-robot, fas fa-file-image
              </p>
            </div>

            <div className="sm:col-span-3">
              <label htmlFor="order" className="block text-sm font-medium text-gray-700">
                Display Order
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  name="order"
                  id="order"
                  min="1"
                  value={editedFeature.order}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div className="sm:col-span-6">
              <label className="block text-sm font-medium text-gray-700">
                Current Image
              </label>
              <div className="mt-1">
                {editedFeature.imageUrl ? (
                  <div className="relative">
                    <img 
                      src={editedFeature.imageUrl} 
                      alt={editedFeature.title} 
                      className="h-32 w-auto object-contain border rounded-md"
                    />
                    <button
                      type="button"
                      onClick={() => setEditedFeature({...editedFeature, imageUrl: ''})}
                      className="absolute top-0 right-0 bg-red-600 text-white rounded-full p-1 shadow-sm"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-32 w-48 border-2 border-gray-300 border-dashed rounded-md">
                    <PhotoIcon className="h-12 w-12 text-gray-400" />
                  </div>
                )}
              </div>
            </div>

            <div className="sm:col-span-6">
              <label className="block text-sm font-medium text-gray-700">
                Upload New Image
              </label>
              <div className="mt-1 flex items-center">
                <input
                  type="file"
                  onChange={handleImageChange}
                  accept="image/*"
                  className="sr-only"
                  id={`image-upload-feature-${index}`}
                />
                <label
                  htmlFor={`image-upload-feature-${index}`}
                  className="relative cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                >
                  <span>Select image</span>
                </label>
                {imageFile && (
                  <div className="ml-3 flex items-center">
                    <span className="text-sm text-gray-500">{imageFile.name}</span>
                    <button
                      type="button"
                      onClick={handleImageUpload}
                      disabled={isUploading}
                      className="ml-3 inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                    >
                      {isUploading ? 'Uploading...' : 'Upload'}
                    </button>
                  </div>
                )}
              </div>
            </div>

            <div className="sm:col-span-3">
              <div className="flex items-center h-full">
                <input
                  id={`isActive-${index}`}
                  name="isActive"
                  type="checkbox"
                  checked={editedFeature.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor={`isActive-${index}`} className="ml-2 block text-sm text-gray-900">
                  Active (visible on website)
                </label>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Title</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{feature.title}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{feature.description}</dd>
            </div>
            {feature.icon && (
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Icon</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <i className={feature.icon} style={{ fontSize: '24px' }}></i>
                  <span className="ml-2">{feature.icon}</span>
                </dd>
              </div>
            )}
            {feature.imageUrl && (
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Image</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <img 
                    src={feature.imageUrl} 
                    alt={feature.title} 
                    className="h-32 w-auto object-contain border rounded-md"
                  />
                </dd>
              </div>
            )}
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  feature.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {feature.isActive ? 'Active' : 'Inactive'}
                </span>
                <span className="ml-3 text-gray-500">Order: {feature.order}</span>
              </dd>
            </div>
          </dl>
        </div>
      )}
    </div>
  );
};

export default FeatureEditor;
