const mysql = require('mysql2/promise');
require('dotenv').config();

const clearPayments = async () => {
  let connection;
  
  try {
    // Create connection to MySQL
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || 'meta_master',
      port: process.env.DB_PORT || 3306
    });
    
    console.log('✅ Connected to MySQL database');
    
    // Count existing payments
    const [countResult] = await connection.query('SELECT COUNT(*) as total FROM payments');
    const totalPayments = countResult[0].total;
    console.log(`📊 Found ${totalPayments} payments in database`);
    
    if (totalPayments > 0) {
      // Ask for confirmation
      console.log('⚠️  This will delete ALL payments from the database!');
      
      // Delete all payments
      const [deleteResult] = await connection.query('DELETE FROM payments');
      console.log(`🗑️  Successfully deleted ${deleteResult.affectedRows} payments`);
      
      // Reset auto-increment counter
      await connection.query('ALTER TABLE payments AUTO_INCREMENT = 1');
      console.log('🔄 Reset auto-increment counter');
      
    } else {
      console.log('ℹ️  No payments found to delete');
    }
    
    // Verify deletion
    const [verifyResult] = await connection.query('SELECT COUNT(*) as remaining FROM payments');
    const remainingPayments = verifyResult[0].remaining;
    console.log(`✅ Remaining payments: ${remainingPayments}`);
    
    console.log('🎉 Payment cleanup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error clearing payments:', error.message);
    
    // Provide specific error guidance
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Check your database credentials in .env file');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure MySQL server is running');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 Database does not exist. Check DB_NAME in .env file');
    }
    
  } finally {
    // Close connection
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
    process.exit(0);
  }
};

// Run the script
console.log('🚀 Starting payment cleanup...');
clearPayments();
