/**
 * Format a price with currency symbol
 * @param price - The price to format
 * @param currency - The currency symbol (default: $)
 * @returns Formatted price string
 */
export const formatPrice = (price: number, currency: string = '$'): string => {
  return `${currency}${price.toFixed(2)}`;
};

/**
 * Truncate a string to a specified length
 * @param str - The string to truncate
 * @param length - Maximum length (default: 100)
 * @returns Truncated string with ellipsis if needed
 */
export const truncateString = (str: string, length: number = 100): string => {
  if (str.length <= length) return str;
  return str.slice(0, length) + '...';
};

/**
 * Scroll to an element by ID with smooth scrolling
 * @param elementId - The ID of the element to scroll to
 */
export const scrollToElement = (elementId: string): void => {
  const element = document.getElementById(elementId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

/**
 * Get the current year
 * @returns Current year as a number
 */
export const getCurrentYear = (): number => {
  return new Date().getFullYear();
};

/**
 * Check if the device is mobile
 * @returns Boolean indicating if the device is mobile
 */
export const isMobile = (): boolean => {
  return window.innerWidth <= 768;
};

/**
 * Generate a random ID
 * @param length - Length of the ID (default: 8)
 * @returns Random ID string
 */
export const generateId = (length: number = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};
