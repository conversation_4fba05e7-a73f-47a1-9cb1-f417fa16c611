import { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  UserGroupIcon,
  CurrencyBangladeshiIcon,
  ClipboardDocumentIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  BanknotesIcon,
  ClockIcon,
  XCircleIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function AffiliatePage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [affiliateData, setAffiliateData] = useState(null)
  const [userInfo, setUserInfo] = useState(null)
  const [copiedText, setCopiedText] = useState(null)
  const [applicationStatus, setApplicationStatus] = useState(null)
  const [applicationReason, setApplicationReason] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }

        // Fetch user profile first
        const userResponse = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!userResponse.ok) {
          throw new Error('Failed to fetch user data')
        }

        const userData = await userResponse.json()
        setUserInfo(userData.user)

        // Fetch affiliate application status
        const statusResponse = await fetch('http://localhost:5001/api/affiliate-application/status', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          setApplicationStatus(statusData.data)
        }

        // Only fetch affiliate stats if user is approved
        if (userData.user.affiliateStatus === 'approved') {
          const response = await fetch('http://localhost:5001/api/affiliate/stats', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })

          if (response.ok) {
            const data = await response.json()
            setAffiliateData(data.data)
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        setError('Failed to load data. Please try again.')

        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [navigate])

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    setCopiedText(text)
    setTimeout(() => setCopiedText(null), 2000)
  }

  const handleApplicationSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      const response = await fetch('http://localhost:5001/api/affiliate-application/apply', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          applicationReason
        })
      })

      const data = await response.json()

      if (response.ok) {
        // Refresh application status
        const statusResponse = await fetch('http://localhost:5001/api/affiliate-application/status', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          setApplicationStatus(statusData.data)
        }

        setApplicationReason('')
        setError(null)
      } else {
        setError(data.message || 'Failed to submit application')
      }
    } catch (error) {
      console.error('Error submitting application:', error)
      setError('Failed to submit application. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleFixBalance = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      const response = await fetch('http://localhost:5001/api/affiliate/fix-balance', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()

      if (response.ok) {
        // Refresh data
        const affiliateResponse = await fetch('http://localhost:5001/api/affiliate/stats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (affiliateResponse.ok) {
          const affiliateData = await affiliateResponse.json()
          setAffiliateData(affiliateData.data)
        }
      } else {
        setError(data.message || 'Failed to fix balance')
      }
    } catch (error) {
      console.error('Error fixing balance:', error)
      setError('An error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-light mb-6">Affiliate Program</h1>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4">
            {error}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Application Status Section */}
            {applicationStatus && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-light mb-4 flex items-center">
                  <UserGroupIcon className="w-5 h-5 mr-2 text-primary" />
                  Affiliate Program Status
                </h2>

                <div className="flex items-center space-x-4">
                  {applicationStatus.affiliateStatus === 'not_applied' && (
                    <div className="flex items-center">
                      <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500 mr-2" />
                      <span className="text-yellow-500">Not Applied</span>
                    </div>
                  )}

                  {applicationStatus.affiliateStatus === 'pending' && (
                    <div className="flex items-center">
                      <ClockIcon className="w-5 h-5 text-yellow-500 mr-2" />
                      <span className="text-yellow-500">Application Pending Review</span>
                    </div>
                  )}

                  {applicationStatus.affiliateStatus === 'approved' && (
                    <div className="flex items-center">
                      <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
                      <span className="text-green-500">Approved - Commission Rate: {(applicationStatus.affiliateCommissionRate * 100).toFixed(0)}%</span>
                    </div>
                  )}

                  {applicationStatus.affiliateStatus === 'rejected' && (
                    <div className="flex items-center">
                      <XCircleIcon className="w-5 h-5 text-red-500 mr-2" />
                      <span className="text-red-500">Application Rejected</span>
                    </div>
                  )}
                </div>

                {applicationStatus.application && (
                  <div className="mt-4 p-4 bg-dark/50 rounded-lg">
                    <p className="text-text text-sm">
                      <strong>Applied:</strong> {new Date(applicationStatus.application.appliedAt).toLocaleDateString()}
                    </p>
                    {applicationStatus.application.reviewedAt && (
                      <p className="text-text text-sm mt-1">
                        <strong>Reviewed:</strong> {new Date(applicationStatus.application.reviewedAt).toLocaleDateString()}
                      </p>
                    )}
                    {applicationStatus.application.adminNotes && (
                      <p className="text-text text-sm mt-2">
                        <strong>Admin Notes:</strong> {applicationStatus.application.adminNotes}
                      </p>
                    )}
                  </div>
                )}
              </motion.div>
            )}

            {/* Application Form - Show only if not applied or rejected */}
            {applicationStatus && (applicationStatus.affiliateStatus === 'not_applied' || applicationStatus.affiliateStatus === 'rejected') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-light mb-4 flex items-center">
                  <PaperAirplaneIcon className="w-5 h-5 mr-2 text-primary" />
                  Apply for Affiliate Program
                </h2>

                <form onSubmit={handleApplicationSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="applicationReason" className="block text-sm font-medium text-light mb-2">
                      Why do you want to join our affiliate program? (Optional)
                    </label>
                    <textarea
                      id="applicationReason"
                      value={applicationReason}
                      onChange={(e) => setApplicationReason(e.target.value)}
                      rows={4}
                      className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors resize-none"
                      placeholder="Tell us about your marketing experience, audience, or why you'd be a great affiliate..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-primary hover:bg-primary/90 disabled:bg-primary/50 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    {isSubmitting ? (
                      <>
                        <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" />
                        Submitting Application...
                      </>
                    ) : (
                      <>
                        <PaperAirplaneIcon className="w-5 h-5 mr-2" />
                        Submit Application
                      </>
                    )}
                  </button>
                </form>
              </motion.div>
            )}

            {/* Affiliate Stats - Only show if approved */}
            {applicationStatus && applicationStatus.affiliateStatus === 'approved' && affiliateData && (
              <>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 md:grid-cols-3 gap-6"
                >
              {/* Available Balance */}
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-lg bg-primary/10">
                    <CurrencyBangladeshiIcon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="ml-4">
                    <p className="text-text text-sm">Available Balance</p>
                    <h3 className="text-2xl font-semibold text-light">
                      ৳{affiliateData?.availableBalance !== undefined ?
                        affiliateData.availableBalance :
                        ((affiliateData?.affiliateCommission || 0) - (affiliateData?.withdrawnCommission || 0) - (affiliateData?.pendingWithdrawals || 0))}
                    </h3>
                    {affiliateData?.availableBalance < 0 && (
                      <button
                        onClick={handleFixBalance}
                        className="mt-2 text-xs text-primary hover:text-primary/80 underline"
                      >
                        Fix Balance
                      </button>
                    )}
                  </div>
                </div>
                <div className="mt-4 text-center">
                  <Link
                    to="/user/withdrawal"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <BanknotesIcon className="-ml-1 mr-2 h-5 w-5" />
                    Withdraw Earnings
                  </Link>
                </div>
              </div>

              {/* Total Referrals */}
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-lg bg-secondary/10">
                    <UserGroupIcon className="h-6 w-6 text-secondary" />
                  </div>
                  <div className="ml-4">
                    <p className="text-text text-sm">Total Referrals</p>
                    <h3 className="text-2xl font-semibold text-light">{affiliateData?.totalReferrals || 0}</h3>
                  </div>
                </div>
              </div>

              {/* Total Earnings */}
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-lg bg-green-500/10">
                    <CurrencyBangladeshiIcon className="h-6 w-6 text-green-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-text text-sm">Total Earnings</p>
                    <h3 className="text-2xl font-semibold text-light">৳{affiliateData?.totalEarnings || 0}</h3>
                  </div>
                </div>
              </div>
                </motion.div>

                {/* Affiliate Link */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
                >
              <h2 className="text-xl font-semibold text-light mb-4">Your Affiliate Link</h2>
              <p className="text-text mb-4">
                Share this link with your friends, followers, or customers. You'll earn 20% commission on every purchase made using your link.
              </p>

              <div className="relative">
                <input
                  type="text"
                  readOnly
                  value={`http://localhost:5173/?ref=${userInfo?.username || ''}`}
                  className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors pr-12"
                />
                <button
                  onClick={() => copyToClipboard(`http://localhost:5173/?ref=${userInfo?.username || ''}`)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-text hover:text-light transition-colors"
                >
                  {copiedText === `http://localhost:5173/?ref=${userInfo?.username || ''}` ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  ) : (
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  )}
                </button>
              </div>

              {copiedText === `http://localhost:5173/?ref=${userInfo?.username || ''}` && (
                <p className="mt-2 text-green-500 text-sm">Copied to clipboard!</p>
              )}
            </motion.div>

            {/* Referral Transactions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-4">Referral Transactions</h2>

              {affiliateData?.transactions && affiliateData.transactions.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-border">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Referred User
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Commission
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {affiliateData.transactions.map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-dark-light/30">
                          <td className="px-4 py-3 text-sm text-text">
                            {new Date(transaction.createdAt).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            {transaction.referredUser ?
                              `${transaction.referredUser.name || 'Unknown'}` :
                              'Unknown User'}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            {transaction.plan}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            ৳{transaction.originalAmount}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            ৳{transaction.commissionAmount}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              transaction.status === 'approved' ? 'bg-green-900/20 text-green-400' :
                              transaction.status === 'pending' ? 'bg-yellow-900/20 text-yellow-400' :
                              'bg-red-900/20 text-red-400'
                            }`}>
                              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                    <UserGroupIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-light mb-2">No Referrals Yet</h3>
                    <p className="text-text mb-6">Share your affiliate link to start earning commissions</p>
                  </div>
                </div>
                )}
                </motion.div>
              </>
            )}
          </div>
        )}
      </div>
    </UserLayout>
  )
}
