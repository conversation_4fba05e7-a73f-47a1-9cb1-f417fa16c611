# Server configuration
PORT=5001
NODE_ENV=production

# MongoDB connection string
# You'll need to replace this with your actual MongoDB connection string
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/meta-master?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=meta-master-jwt-secret-change-in-production
JWT_EXPIRES_IN=24h

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>
