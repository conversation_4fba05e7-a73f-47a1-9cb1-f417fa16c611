import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import Button from '../components/ui/Button'

export default function NotFoundPage() {
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0)
    
    // Set page title
    document.title = 'Page Not Found - Meta Master'
  }, [])

  return (
    <div className="pt-32 pb-20 md:pt-40 md:pb-32">
      <div className="container">
        <div className="max-w-3xl mx-auto text-center">
          <motion.div 
            className="mb-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <span className="text-9xl font-bold gradient-text">404</span>
          </motion.div>
          
          <motion.h1 
            className="text-4xl md:text-5xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Page Not Found
          </motion.h1>
          
          <motion.p 
            className="text-xl text-dark-300 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            The page you are looking for doesn't exist or has been moved.
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Button to="/" variant="primary" size="lg" animate>
              Return to Home
            </Button>
          </motion.div>
          
          <motion.div 
            className="mt-12 p-6 rounded-lg bg-dark-800/50 border border-dark-700"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h2 className="text-xl font-bold mb-4">Looking for something?</h2>
            <p className="text-dark-400 mb-4">
              Here are some helpful links:
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link to="/" className="text-primary hover:underline">Home</Link>
              <Link to="/features" className="text-primary hover:underline">Features</Link>
              <Link to="/pricing" className="text-primary hover:underline">Pricing</Link>
              <Link to="/download" className="text-primary hover:underline">Download</Link>
              <Link to="/contact" className="text-primary hover:underline">Contact</Link>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
