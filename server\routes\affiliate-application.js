const express = require('express');
const router = express.Router();
const AffiliateApplication = require('../models/mysql/AffiliateApplication');
const User = require('../models/mysql/User');
const { authenticate, isAdmin } = require('../middleware/auth');

/**
 * @route   POST /api/affiliate-application/apply
 * @desc    Apply for affiliate program
 * @access  Private (User)
 */
router.post('/apply', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { applicationReason } = req.body;

    // Check if user already has an application
    const existingApplication = await AffiliateApplication.findByUserId(userId);
    if (existingApplication) {
      return res.status(400).json({
        success: false,
        message: 'You have already applied for the affiliate program'
      });
    }

    // Check if user is already an approved affiliate
    const user = await User.findById(userId);
    if (user && user.affiliateStatus === 'approved') {
      return res.status(400).json({
        success: false,
        message: 'You are already an approved affiliate'
      });
    }

    // Create new application
    const application = await AffiliateApplication.create({
      userId,
      applicationReason: applicationReason || ''
    });

    // Update user status to pending
    await User.update(userId, { affiliateStatus: 'pending' });

    return res.status(201).json({
      success: true,
      message: 'Affiliate application submitted successfully',
      data: application
    });

  } catch (error) {
    console.error('Error applying for affiliate program:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while processing application',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate-application/status
 * @desc    Get user's affiliate application status
 * @access  Private (User)
 */
router.get('/status', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user's affiliate status
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get application details if exists
    const application = await AffiliateApplication.findByUserId(userId);

    return res.status(200).json({
      success: true,
      data: {
        affiliateStatus: user.affiliateStatus,
        affiliateCommissionRate: user.affiliateCommissionRate,
        affiliateApprovedAt: user.affiliateApprovedAt,
        application: application
      }
    });

  } catch (error) {
    console.error('Error getting affiliate application status:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while fetching status',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate-application/admin/applications
 * @desc    Get all affiliate applications for admin
 * @access  Private (Admin)
 */
router.get('/admin/applications', authenticate, isAdmin, async (req, res) => {
  try {
    const { status, limit } = req.query;
    
    const options = {};
    if (status) options.status = status;
    if (limit) options.limit = parseInt(limit);

    const applications = await AffiliateApplication.findAll(options);
    const stats = await AffiliateApplication.getStats();

    return res.status(200).json({
      success: true,
      data: {
        applications,
        stats
      }
    });

  } catch (error) {
    console.error('Error fetching affiliate applications:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while fetching applications',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate-application/admin/approve/:id
 * @desc    Approve affiliate application
 * @access  Private (Admin)
 */
router.post('/admin/approve/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const applicationId = req.params.id;
    const { commissionRate, adminNotes } = req.body;
    const adminId = req.user.id;

    // Validate commission rate
    const rate = parseFloat(commissionRate);
    if (isNaN(rate) || rate < 0 || rate > 1) {
      return res.status(400).json({
        success: false,
        message: 'Commission rate must be between 0 and 1'
      });
    }

    // Update application status
    const updatedApplication = await AffiliateApplication.updateStatus(applicationId, {
      status: 'approved',
      commissionRate: rate,
      adminNotes: adminNotes || '',
      reviewedBy: adminId
    });

    if (!updatedApplication) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Affiliate application approved successfully',
      data: updatedApplication
    });

  } catch (error) {
    console.error('Error approving affiliate application:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while approving application',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate-application/admin/reject/:id
 * @desc    Reject affiliate application
 * @access  Private (Admin)
 */
router.post('/admin/reject/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const applicationId = req.params.id;
    const { adminNotes } = req.body;
    const adminId = req.user.id;

    // Update application status
    const updatedApplication = await AffiliateApplication.updateStatus(applicationId, {
      status: 'rejected',
      commissionRate: 0,
      adminNotes: adminNotes || '',
      reviewedBy: adminId
    });

    if (!updatedApplication) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Update user status to rejected
    await User.update(updatedApplication.userId, { affiliateStatus: 'rejected' });

    return res.status(200).json({
      success: true,
      message: 'Affiliate application rejected',
      data: updatedApplication
    });

  } catch (error) {
    console.error('Error rejecting affiliate application:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while rejecting application',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate-application/admin/stats
 * @desc    Get affiliate application statistics
 * @access  Private (Admin)
 */
router.get('/admin/stats', authenticate, isAdmin, async (req, res) => {
  try {
    const stats = await AffiliateApplication.getStats();

    return res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching affiliate application stats:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while fetching stats',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
