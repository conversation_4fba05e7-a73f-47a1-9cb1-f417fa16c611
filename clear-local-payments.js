#!/usr/bin/env node

import mysql from 'mysql2/promise';

const clearLocalPayments = async () => {
  let connection;
  
  try {
    console.log('🚀 Starting local payment cleanup...');
    console.log('📍 Target: Local development database');
    
    // Create connection to local MySQL (using default local settings)
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'Jayed123#@!', // Update this if your local MySQL password is different
      database: 'meta_master',
      port: 3306
    });
    
    console.log('✅ Connected to local MySQL database');
    
    // Count existing payments
    const [countResult] = await connection.query('SELECT COUNT(*) as total FROM payments');
    const totalPayments = countResult[0].total;
    console.log(`📊 Found ${totalPayments} payments in local database`);
    
    if (totalPayments > 0) {
      console.log('🗑️  Clearing all payments from local database...');
      
      // Delete all payments
      const [deleteResult] = await connection.query('DELETE FROM payments');
      console.log(`✅ Successfully deleted ${deleteResult.affectedRows} payments`);
      
      // Reset auto-increment counter
      await connection.query('ALTER TABLE payments AUTO_INCREMENT = 1');
      console.log('🔄 Reset auto-increment counter');
      
      // Also clear related data (optional)
      try {
        // Clear affiliate transactions related to payments
        const [affiliateResult] = await connection.query('DELETE FROM affiliate_transactions WHERE payment_id IS NOT NULL');
        if (affiliateResult.affectedRows > 0) {
          console.log(`🔄 Cleared ${affiliateResult.affectedRows} affiliate transactions`);
        }
        
        // Clear license keys related to payments
        const [licenseResult] = await connection.query('DELETE FROM license_keys WHERE payment_id IS NOT NULL');
        if (licenseResult.affectedRows > 0) {
          console.log(`🔄 Cleared ${licenseResult.affectedRows} license keys`);
        }
      } catch (relatedError) {
        console.log('ℹ️  Some related tables may not exist yet:', relatedError.message);
      }
      
    } else {
      console.log('ℹ️  No payments found to delete');
    }
    
    // Verify deletion
    const [verifyResult] = await connection.query('SELECT COUNT(*) as remaining FROM payments');
    const remainingPayments = verifyResult[0].remaining;
    console.log(`✅ Remaining payments: ${remainingPayments}`);
    
    console.log('🎉 Local payment cleanup completed successfully!');
    console.log('💡 You can now refresh your admin panel at http://localhost:5173/admin/payments');
    
  } catch (error) {
    console.error('❌ Error clearing local payments:', error.message);
    
    // Provide specific error guidance
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Check your database credentials in server/.env file');
      console.error('💡 Make sure DB_PASSWORD is correct');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure MySQL server is running locally');
      console.error('💡 Try starting XAMPP/WAMP or your local MySQL service');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 Database "meta_master" does not exist');
      console.error('💡 Run: npm run init-db to create the database');
    }
    
    process.exit(1);
    
  } finally {
    // Close connection
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
};

// Auto-run the script
console.log('🧹 Meta Master - Local Payment Cleanup Tool');
console.log('==========================================');
clearLocalPayments();
