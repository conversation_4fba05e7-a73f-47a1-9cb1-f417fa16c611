const express = require('express');
const router = express.Router();
const Admin = require('../models/mysql/Admin');
const { generateToken } = require('../utils/jwt');
const { authenticate } = require('../middleware/auth');
const bcrypt = require('bcryptjs');

/**
 * @route   POST /api/auth/login
 * @desc    Authenticate admin & get token
 * @access  Public
 */
router.post('/login', async (req, res) => {
  try {
    console.log('Login attempt with body:', req.body);
    const { username, password } = req.body;

    // Validate input
    if (!username || !password) {
      console.log('Missing username or password');
      return res.status(400).json({
        success: false,
        message: 'Please provide username and password'
      });
    }

    console.log('Attempting to find admin with username:', username);
    // Check if admin exists
    const admin = await Admin.findOne({ username });
    console.log('Admin found:', admin ? 'Yes' : 'No');

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    console.log('Checking password for admin:', admin.username);
    // Check password
    const isMatch = await bcrypt.compare(password, admin.password);
    console.log('Password match:', isMatch ? 'Yes' : 'No');

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    console.log('Generating token for admin:', admin.username);
    // Generate JWT token
    const token = generateToken(admin);
    console.log('Token generated successfully');

    // Return token and admin info
    return res.status(200).json({
      success: true,
      token,
      admin: {
        id: admin.id,
        username: admin.username,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during login',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/auth/me
 * @desc    Get current admin user
 * @access  Private
 */
router.get('/me', authenticate, async (req, res) => {
  try {
    const admin = await Admin.findById(req.user.id);

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    return res.status(200).json({
      success: true,
      admin
    });
  } catch (error) {
    console.error('Get admin error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving admin',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/auth/register
 * @desc    Register a new admin (for initial setup only)
 * @access  Public (should be restricted in production)
 */
router.post('/register', async (req, res) => {
  try {
    const { username, password, role } = req.body;

    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide username and password'
      });
    }

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ username });
    if (existingAdmin) {
      return res.status(400).json({
        success: false,
        message: 'Admin already exists'
      });
    }

    // Create new admin
    const admin = await Admin.create({
      username,
      password,
      role: role || 'admin'
    });

    // Generate JWT token
    const token = generateToken(admin);

    // Return token and admin info
    return res.status(201).json({
      success: true,
      token,
      admin: {
        id: admin.id,
        username: admin.username,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('Register error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during registration',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
