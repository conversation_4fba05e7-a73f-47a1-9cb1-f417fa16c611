const express = require('express');
const router = express.Router();
const { TutorialCategory } = require('../models/mysql');
const { authenticate, isAdmin } = require('../middleware/auth');

/**
 * @route   GET /api/tutorial-categories
 * @desc    Get all tutorial categories
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    const { includeInactive } = req.query;
    
    // Get categories
    const categories = await TutorialCategory.findAll(includeInactive === 'true');
    
    return res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get tutorial categories error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving tutorial categories',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/tutorial-categories/:id
 * @desc    Get tutorial category by ID
 * @access  Public
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get category
    const category = await TutorialCategory.findById(id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Tutorial category not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Get tutorial category error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving tutorial category',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/tutorial-categories
 * @desc    Create a new tutorial category
 * @access  Private (Admin only)
 */
router.post('/', authenticate, isAdmin, async (req, res) => {
  try {
    const {
      name,
      slug,
      description,
      orderNum,
      isActive
    } = req.body;
    
    // Validate required fields
    if (!name || !slug) {
      return res.status(400).json({
        success: false,
        message: 'Name and slug are required'
      });
    }
    
    // Check if slug already exists
    const existingCategory = await TutorialCategory.findBySlug(slug);
    
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'A category with this slug already exists'
      });
    }
    
    // Create category
    const category = await TutorialCategory.create({
      name,
      slug,
      description,
      orderNum,
      isActive
    });
    
    return res.status(201).json({
      success: true,
      message: 'Tutorial category created successfully',
      data: category
    });
  } catch (error) {
    console.error('Create tutorial category error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while creating tutorial category',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   PUT /api/tutorial-categories/:id
 * @desc    Update a tutorial category
 * @access  Private (Admin only)
 */
router.put('/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      slug,
      description,
      orderNum,
      isActive
    } = req.body;
    
    // Check if category exists
    const category = await TutorialCategory.findById(id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Tutorial category not found'
      });
    }
    
    // If slug is being changed, check if it already exists
    if (slug && slug !== category.slug) {
      const existingCategory = await TutorialCategory.findBySlug(slug);
      
      if (existingCategory && existingCategory.id !== parseInt(id)) {
        return res.status(400).json({
          success: false,
          message: 'A category with this slug already exists'
        });
      }
    }
    
    // Update category
    const updatedCategory = await TutorialCategory.update(id, {
      name,
      slug,
      description,
      orderNum,
      isActive
    });
    
    return res.status(200).json({
      success: true,
      message: 'Tutorial category updated successfully',
      data: updatedCategory
    });
  } catch (error) {
    console.error('Update tutorial category error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while updating tutorial category',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   DELETE /api/tutorial-categories/:id
 * @desc    Delete a tutorial category
 * @access  Private (Admin only)
 */
router.delete('/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if category exists
    const category = await TutorialCategory.findById(id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Tutorial category not found'
      });
    }
    
    // Delete category
    await TutorialCategory.delete(id);
    
    return res.status(200).json({
      success: true,
      message: 'Tutorial category deleted successfully'
    });
  } catch (error) {
    console.error('Delete tutorial category error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while deleting tutorial category',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
