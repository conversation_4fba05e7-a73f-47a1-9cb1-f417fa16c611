import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { CheckIcon, PhotoIcon, BoltIcon, ClockIcon, StarIcon } from '@heroicons/react/24/solid'

export interface PricingPlan {
  name: string
  price: number
  originalPrice?: number
  period: string
  description: string
  features: string[]
  buttonText: string
  featured?: boolean
  badge?: string
  icon?: string
  color?: 'primary' | 'secondary' | 'accent'
  currency?: string
  highlightFeature?: string
  planType?: 'core' | 'overdrive' | 'team'
  discount?: string
  featureDescriptions?: string[]
}

interface PricingCardProps {
  plan: PricingPlan
  index: number
}

const PricingCard: React.FC<PricingCardProps> = ({ plan, index }) => {
  // Custom Infinity Icon
  const InfinityIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-cyan-500" viewBox="0 0 24 24" fill="currentColor">
      <path d="M18.6,6.62C21.58,6.62 24,9.04 24,12C24,14.96 21.58,17.38 18.6,17.38C17.15,17.38 15.8,16.78 14.83,15.81L12,13L9.17,15.81C8.2,16.78 6.85,17.38 5.4,17.38C2.42,17.38 0,14.96 0,12C0,9.04 2.42,6.62 5.4,6.62C6.85,6.62 8.2,7.22 9.17,8.19L12,11L14.83,8.19C15.8,7.22 17.15,6.62 18.6,6.62M7.8,14.39L10.5,11.7L7.8,9C7.16,8.37 6.31,8 5.4,8C3.14,8 1.4,9.74 1.4,12C1.4,14.26 3.14,16 5.4,16C6.31,16 7.16,15.63 7.8,14.39M16.2,9L13.5,11.7L16.2,14.39C16.84,15.63 17.69,16 18.6,16C20.86,16 22.6,14.26 22.6,12C22.6,9.74 20.86,8 18.6,8C17.69,8 16.84,8.37 16.2,9Z" />
    </svg>
  )

  // Get feature icon based on text content
  const getFeatureIcon = (feature: string) => {
    if (feature.toLowerCase().includes('unlimited')) {
      return <InfinityIcon />
    }
    if (feature.toLowerCase().includes('license') || feature.toLowerCase().includes('lifetime')) {
      return <ClockIcon className="h-5 w-5 text-cyan-500" />
    }
    if (feature.toLowerCase().includes('api') || feature.toLowerCase().includes('gemini')) {
      return <BoltIcon className="h-5 w-5 text-cyan-500" />
    }
    if (feature.toLowerCase().includes('image') || feature.toLowerCase().includes('png') || feature.toLowerCase().includes('jpg') || feature.toLowerCase().includes('csv')) {
      return <PhotoIcon className="h-5 w-5 text-cyan-500" />
    }
    return <CheckIcon className="h-5 w-5 text-cyan-500" />
  }

  // Feature descriptions for the right side
  const featureDescriptions = plan.featureDescriptions || [
    'Batch Processing',
    'Trial Period',
    'Gemini API Integration',
    'Results Generation',
    'Supported Formats'
  ]

  return (
    <motion.div
      className="relative overflow-hidden rounded-lg border border-cyan-500/30 bg-[#051824]"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{
        boxShadow: '0 0 20px rgba(0, 167, 217, 0.3)',
        borderColor: 'rgba(0, 167, 217, 0.5)'
      }}
    >
      {/* Discount tag */}
      {plan.discount && (
        <div className="absolute top-4 left-4 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
          {plan.discount}
        </div>
      )}

      {/* Recommended ribbon */}
      {plan.featured && (
        <div className="absolute top-0 right-0">
          <div className="bg-cyan-600 text-white py-1 px-6 text-xs font-bold transform rotate-45 translate-x-[30%] translate-y-[40%] shadow-lg">
            Recommended
          </div>
        </div>
      )}

      {/* Header */}
      <div className="p-6">
        <div className="flex items-center mb-2">
          <StarIcon className="h-5 w-5 text-cyan-500 mr-2" />
          <h3 className="text-xl font-bold text-white">{plan.name}</h3>
        </div>
        <p className="text-gray-400 text-sm mb-4">{plan.description}</p>

        {/* Price */}
        <div className="mb-6">
          <div className="flex items-baseline">
            <span className="text-white text-3xl font-bold">৳{plan.price}</span>
            {plan.originalPrice && (
              <span className="text-gray-500 line-through ml-2">৳{plan.originalPrice}</span>
            )}
            <span className="text-gray-400 ml-2">/{plan.period === 'month' ? 'mo' : plan.period}</span>
          </div>
          {plan.period === 'year' && (
            <p className="text-cyan-500 text-sm mt-1">Save ৳{Math.round(plan.price * 0.2)} per year</p>
          )}
        </div>
      </div>

      {/* Divider */}
      <div className="h-px bg-cyan-500/20 w-full"></div>

      {/* Features */}
      <div className="p-6">
        <ul className="space-y-4">
          {plan.features.slice(0, 5).map((feature, i) => (
            <motion.li
              key={i}
              className="flex justify-between"
              initial={{ opacity: 0, x: -10 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.2 + i * 0.05 }}
            >
              <div className="flex items-center">
                {getFeatureIcon(feature)}
                <span className="text-cyan-400 ml-3 text-sm">{feature}</span>
              </div>
              <span className="text-gray-400 text-sm">{featureDescriptions[i] || ''}</span>
            </motion.li>
          ))}
        </ul>

        {plan.features.length > 5 && (
          <button className="text-cyan-500 text-sm mt-4 hover:text-cyan-400 transition-colors">
            View all features
          </button>
        )}
      </div>

      {/* CTA Button */}
      <div className="p-6 pt-2">
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          transition={{ duration: 0.2 }}
        >
          <Link
            to="/checkout"
            state={{ plan: plan.name, price: plan.price, currency: plan.currency }}
            className="w-full py-3 px-6 rounded-md font-semibold text-center block text-white bg-gradient-to-r from-cyan-600 to-blue-700 hover:from-cyan-700 hover:to-blue-800 transition-all duration-300 shadow-lg shadow-cyan-900/30"
          >
            <span className="flex items-center justify-center">
              <StarIcon className="h-5 w-5 mr-2" />
              <span>Get {plan.name}</span>
              {plan.period === 'year' && <span className="ml-1">- ৳{plan.price}/year</span>}
              {plan.period === 'lifetime' && <span className="ml-1">- ৳{plan.price}</span>}
            </span>
          </Link>
        </motion.div>
      </div>
    </motion.div>
  )
}

export default PricingCard
