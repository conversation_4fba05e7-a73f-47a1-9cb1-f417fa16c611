import { motion } from 'framer-motion'
import <PERSON><PERSON>eader from '../ui/SectionHeader'

const seoSections = [
  {
    title: 'Best Metadata Generator for Microstock',
    content: 'Meta Master is the industry-leading metadata generator specifically designed for microstock contributors. Our AI-powered tool generates SEO-optimized titles, keywords, and descriptions that help your content rank higher on stock photography platforms.',
    keywords: ['metadata generator', 'microstock metadata', 'stock photo keywords']
  },
  {
    title: 'Professional CSV Generator for Stock Platforms',
    content: 'Export your metadata in perfect CSV format for Shutterstock, Adobe Stock, Getty Images, Freepik, and all major microstock platforms. Our CSV generator ensures your metadata meets each platform\'s specific requirements.',
    keywords: ['csv generator', 'shutterstock csv', 'adobe stock csv']
  },
  {
    title: 'AI-Powered Title Generator for Stock Photos',
    content: 'Generate compelling, search-optimized titles for your stock photos, vectors, and videos. Our microstock title generator uses advanced AI to create titles that buyers are actually searching for.',
    keywords: ['title generator', 'stock photo titles', 'microstock titles']
  },
  {
    title: 'Advanced Keyword Research Tool',
    content: 'Discover high-performing keywords for your microstock content. Meta Master analyzes market trends and search patterns to suggest keywords that will maximize your content\'s visibility and sales potential.',
    keywords: ['keyword research', 'stock photo keywords', 'microstock SEO']
  }
]

const platforms = [
  { name: 'Shutterstock', description: 'Perfect metadata formatting for Shutterstock submissions' },
  { name: 'Adobe Stock', description: 'Optimized keywords and titles for Adobe Stock' },
  { name: 'Getty Images', description: 'Professional metadata for Getty Images contributors' },
  { name: 'Freepik', description: 'SEO-optimized content for Freepik platform' },
  { name: 'Dreamstime', description: 'Enhanced discoverability on Dreamstime' },
  { name: 'Vecteezy', description: 'Vector-specific metadata for Vecteezy' }
]

export default function SEOContent() {
  return (
    <section className="section bg-dark-900/30">
      <div className="container">
        <SectionHeader
          title="The Complete Metadata Solution for Microstock Success"
          subtitle="Everything you need to optimize your content for maximum visibility and earnings"
          gradient
        />

        {/* SEO Content Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {seoSections.map((section, index) => (
            <motion.div
              key={section.title}
              className="card p-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <h3 className="text-xl font-bold mb-3 text-primary">{section.title}</h3>
              <p className="text-dark-300 mb-4">{section.content}</p>
              <div className="flex flex-wrap gap-2">
                {section.keywords.map((keyword) => (
                  <span
                    key={keyword}
                    className="px-3 py-1 text-xs rounded-full bg-primary/10 text-primary border border-primary/20"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Platform Support Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h3 className="text-2xl font-bold text-center mb-8">
            Optimized for All Major <span className="gradient-text">Microstock Platforms</span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {platforms.map((platform, index) => (
              <motion.div
                key={platform.name}
                className="card p-4 text-center"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <h4 className="font-bold text-lg mb-2">{platform.name}</h4>
                <p className="text-sm text-dark-400">{platform.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* FAQ-style SEO Content */}
        <motion.div
          className="card p-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h3 className="text-2xl font-bold mb-6 text-center">
            Why Meta Master is the #1 Choice for <span className="gradient-text">Metadata Generation</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-bold text-lg mb-3 text-primary">What makes Meta Master different?</h4>
              <p className="text-dark-300 mb-4">
                Meta Master 5.3.0 uses Google's latest Gemini 2.5 AI technology specifically trained for microstock metadata generation. Unlike generic tools, our AI understands the nuances of stock photography markets and generates metadata that actually sells.
              </p>
              
              <h4 className="font-bold text-lg mb-3 text-primary">How does it improve my earnings?</h4>
              <p className="text-dark-300">
                Better metadata means better discoverability. Our users report 40-60% increases in downloads and sales after switching to Meta Master. The AI generates keywords that buyers are actually searching for, not just descriptive terms.
              </p>
            </div>
            
            <div>
              <h4 className="font-bold text-lg mb-3 text-primary">Which file formats are supported?</h4>
              <p className="text-dark-300 mb-4">
                Meta Master supports all major formats: JPG, PNG, SVG, EPS, AI, MP4, MOV, AVI, and MKV. Special handling for transparent PNGs, vector files, and video thumbnails ensures optimal metadata for each format type.
              </p>
              
              <h4 className="font-bold text-lg mb-3 text-primary">Is it suitable for beginners?</h4>
              <p className="text-dark-300">
                Absolutely! Meta Master is designed for contributors of all levels. The intuitive interface makes it easy for beginners, while advanced features satisfy professional contributors managing thousands of files.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
