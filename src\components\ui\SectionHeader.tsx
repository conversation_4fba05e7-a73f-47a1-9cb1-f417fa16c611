import { motion } from 'framer-motion'

interface SectionHeaderProps {
  title: string
  subtitle: string
  centered?: boolean
  light?: boolean
  gradient?: boolean
}

export default function SectionHeader({ 
  title, 
  subtitle, 
  centered = true, 
  light = false,
  gradient = false
}: SectionHeaderProps) {
  return (
    <div className={`mb-12 md:mb-16 ${centered ? 'text-center' : 'text-left'}`}>
      <motion.h2 
        className={`text-3xl md:text-4xl font-bold mb-4 ${gradient ? 'gradient-text' : light ? 'text-white' : 'text-white'}`}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
      >
        {title}
      </motion.h2>
      <motion.p 
        className={`text-lg ${light ? 'text-dark-200' : 'text-dark-400'} ${centered ? 'max-w-3xl mx-auto' : ''}`}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        {subtitle}
      </motion.p>
    </div>
  )
}
