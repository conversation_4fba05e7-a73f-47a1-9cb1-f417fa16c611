import { Routes, Route, useLocation } from 'react-router-dom'
import { useEffect } from 'react'
import HomePage from './pages/HomePage'
import PricingPage from './pages/PricingPage'
import DemoPage from './pages/DemoPage'
import DownloadPage from './pages/DownloadPage'
import ContactPage from './pages/ContactPage'
import CheckoutPage from './pages/CheckoutPage'
// import ThankYouPage from './pages/ThankYouPage' // Not used anymore - redirecting to dashboard after checkout
import NotFoundPage from './pages/NotFoundPage'
import Header from './components/layout/Header'
import Footer from './components/layout/Footer'
import ScrollToTop from './components/ui/ScrollToTop'

// Admin pages
import AdminLoginPage from './pages/admin/LoginPage'
import AdminDashboardPage from './pages/admin/DashboardPage'
import AllPaymentsPage from './pages/admin/AllPaymentsPage'
import PendingPaymentsPage from './pages/admin/PendingPaymentsPage'
import ApprovedPaymentsPage from './pages/admin/ApprovedPaymentsPage'
import RejectedPaymentsPage from './pages/admin/RejectedPaymentsPage'
import WithdrawalsPage from './pages/admin/WithdrawalsPage'
import AllWithdrawalsPage from './pages/admin/AllWithdrawalsPage'
import AffiliateApplicationsPage from './pages/admin/AffiliateApplicationsPage'
import AffiliateLeaderboardPage from './pages/admin/AffiliateLeaderboardPage'
import WebsiteContentPage from './pages/admin/WebsiteContentPage'
import WebsiteManagementPage from './pages/admin/WebsiteManagementPage'

// User pages
import UserLoginPage from './pages/user/LoginPage'
import UserRegisterPage from './pages/user/RegisterPage'
import UserDashboardPage from './pages/user/DashboardPage'
import UserProfilePage from './pages/user/ProfilePage'
import UserLicensesPage from './pages/user/LicensesPage'
import UserPaymentsPage from './pages/user/PaymentsPage'
import UserSettingsPage from './pages/user/SettingsPage'
import UserTutorialPage from './pages/user/TutorialPage'
import AffiliatePage from './pages/user/AffiliatePage'
import WithdrawalPage from './pages/user/WithdrawalPage'

function App() {
  const location = useLocation()
  const isAdminRoute = location.pathname.startsWith('/admin')
  const isUserRoute = location.pathname.startsWith('/user')

  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [location.pathname])

  return (
    <div className="flex flex-col min-h-screen">
      {!isAdminRoute && !isUserRoute && <Header />}
      <main className={`flex-grow ${isAdminRoute ? 'bg-gray-100' : ''}`}>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/pricing" element={<PricingPage />} />
          <Route path="/demo" element={<DemoPage />} />
          <Route path="/download" element={<DownloadPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/checkout" element={<CheckoutPage />} />
          {/* Thank you page removed - redirecting to dashboard after checkout */}
          {/* <Route path="/thank-you" element={<ThankYouPage />} /> */}

          {/* Admin routes */}
          <Route path="/power/login" element={<AdminLoginPage />} />
          <Route path="/admin" element={<AdminDashboardPage />} />
          <Route path="/admin/payments" element={<AllPaymentsPage />} />
          <Route path="/admin/payments/pending" element={<PendingPaymentsPage />} />
          <Route path="/admin/payments/approved" element={<ApprovedPaymentsPage />} />
          <Route path="/admin/payments/rejected" element={<RejectedPaymentsPage />} />
          <Route path="/admin/withdrawals" element={<WithdrawalsPage />} />
          <Route path="/admin/all-withdrawals" element={<AllWithdrawalsPage />} />
          <Route path="/admin/affiliate-applications" element={<AffiliateApplicationsPage />} />
          <Route path="/admin/affiliates" element={<AffiliateLeaderboardPage />} />
          <Route path="/admin/website-content" element={<WebsiteContentPage />} />
          <Route path="/admin/website-management" element={<WebsiteManagementPage />} />

          {/* User routes */}
          <Route path="/user/login" element={<UserLoginPage />} />
          <Route path="/user/register" element={<UserRegisterPage />} />
          <Route path="/user/dashboard" element={<UserDashboardPage />} />
          <Route path="/user/profile" element={<UserProfilePage />} />
          <Route path="/user/licenses" element={<UserLicensesPage />} />
          <Route path="/user/payments" element={<UserPaymentsPage />} />
          <Route path="/user/settings" element={<UserSettingsPage />} />
          <Route path="/user/tutorials" element={<UserTutorialPage />} />
          <Route path="/user/affiliate" element={<AffiliatePage />} />
          <Route path="/user/withdrawal" element={<WithdrawalPage />} />

          {/* 404 route */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </main>
      {!isAdminRoute && !isUserRoute && <Footer />}
      <ScrollToTop />
    </div>
  )
}

export default App
