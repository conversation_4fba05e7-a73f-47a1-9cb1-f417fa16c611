# Meta Master Marketing Website

This is a modern, React-based marketing website for Meta Master, an AI-powered metadata generator for microstock contributors.

## Technologies Used

- **React.js** - Frontend library for building user interfaces
- **TypeScript** - Typed JavaScript for better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - For navigation and routing
- **Framer Motion** - For animations and transitions
- **Headless UI** - Unstyled, accessible UI components
- **React Hook Form** - For form validation and handling
- **Heroicons** - Beautiful hand-crafted SVG icons

## Features

- Modern, responsive design with dark mode only
- Animated UI elements using Framer Motion
- Comprehensive product information and feature showcase
- Pricing plans with toggle between monthly and yearly billing
- Interactive demo section with screenshots and video
- Contact form with validation
- FAQ section with expandable answers
- Thank you page after purchase completion

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/meta-master-website.git
cd meta-master-website
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Start the development server
```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Building for Production

To build the website for production, run:

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory.

## Project Structure

- `src/` - Source code
  - `assets/` - Images, icons, and other static assets
  - `components/` - Reusable UI components
    - `home/` - Components specific to the home page
    - `layout/` - Layout components (Header, Footer)
    - `pricing/` - Pricing-related components
    - `ui/` - Generic UI components
  - `pages/` - Page components
  - `App.tsx` - Main application component
  - `main.tsx` - Application entry point
  - `index.css` - Global styles

## Customization

### Colors

The color scheme can be customized in the `tailwind.config.js` file. The current theme uses:

- Primary: Indigo (#6366f1)
- Secondary: Violet (#8b5cf6)
- Accent: Pink (#ec4899)
- Dark: Slate (#0f172a)

### Images

Replace the placeholder images in the `src/assets/images/` directory with actual screenshots and images of the Meta Master software.

### Content

Update the content in the component files to reflect the latest features and pricing of Meta Master.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Tailwind CSS](https://tailwindcss.com/)
- [React](https://reactjs.org/)
- [Framer Motion](https://www.framer.com/motion/)
- [Headless UI](https://headlessui.dev/)
- [Heroicons](https://heroicons.com/)
