import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import SectionHeader from '../ui/SectionHeader'

const screenshots = [
  {
    image: '/images/screenshot-1.png',
    caption: 'Intuitive Quantum Interface',
    description: 'Redesigned UI with neural-responsive elements that adapt to your workflow'
  },
  {
    image: '/images/screenshot-2.png',
    caption: 'AI-powered Metadata Generation',
    description: 'Advanced neural processing creates optimized metadata in seconds'
  },
  {
    image: '/images/screenshot-3.png',
    caption: 'Holographic Batch Processing',
    description: 'Process thousands of files simultaneously with visual progress tracking'
  }
]

export default function Demo() {
  const [activeScreenshot, setActiveScreenshot] = useState(0)

  const nextScreenshot = () => {
    setActiveScreenshot((prev) => (prev + 1) % screenshots.length)
  }

  const prevScreenshot = () => {
    setActiveScreenshot((prev) => (prev - 1 + screenshots.length) % screenshots.length)
  }

  return (
    <section id="demo" className="section relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-gradient-radial from-secondary/10 to-transparent opacity-50 blur-3xl"></div>
      </div>

      <div className="container relative z-10">
        <SectionHeader
          title="See Meta Master in Action"
          subtitle="Experience the power and simplicity of our AI-driven metadata generation"
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Video/Demo section */}
          <motion.div
            className="relative rounded-xl overflow-hidden shadow-2xl border border-dark-700"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <img
              src="/images/demo-video.png"
              alt="Meta Master Demo"
              className="w-full h-auto"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.button
                className="w-20 h-20 rounded-full bg-primary/90 flex items-center justify-center text-white"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              </motion.button>
            </div>
          </motion.div>

          {/* Screenshots carousel */}
          <div>
            <motion.h3
              className="text-2xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Explore the Interface
            </motion.h3>

            {/* Screenshots with 3D effect */}
            <div className="relative h-[400px] perspective-1000">
              <AnimatePresence mode="wait">
                {screenshots.map((screenshot, index) => (
                  activeScreenshot === index && (
                    <motion.div
                      key={index}
                      className="absolute inset-0 flex flex-col md:flex-row items-center gap-8"
                      initial={{ opacity: 0, rotateY: -10, scale: 0.9 }}
                      animate={{ opacity: 1, rotateY: 0, scale: 1 }}
                      exit={{ opacity: 0, rotateY: 10, scale: 0.9 }}
                      transition={{ duration: 0.5 }}
                    >
                      {/* Screenshot image */}
                      <div className="w-full md:w-2/3 relative group">
                        <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-xl blur-sm"></div>
                        <div className="relative rounded-lg overflow-hidden border border-dark-700 shadow-lg">
                          <img
                            src={screenshot.image}
                            alt={screenshot.caption}
                            className="w-full h-auto"
                          />
                        </div>
                      </div>

                      {/* Screenshot info */}
                      <div className="w-full md:w-1/3">
                        <h4 className="text-xl font-bold mb-2 gradient-text">{screenshot.caption}</h4>
                        <p className="text-dark-400 mb-6">{screenshot.description}</p>
                      </div>
                    </motion.div>
                  )
                ))}
              </AnimatePresence>

              {/* Navigation buttons */}
              <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-4">
                <motion.button
                  className="p-2 rounded-full bg-dark-800 text-dark-400 hover:text-white hover:bg-primary/20 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={prevScreenshot}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </motion.button>

                {screenshots.map((_, index) => (
                  <motion.button
                    key={index}
                    className={`w-3 h-3 rounded-full ${
                      activeScreenshot === index ? 'bg-primary' : 'bg-dark-700'
                    }`}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.8 }}
                    onClick={() => setActiveScreenshot(index)}
                  />
                ))}

                <motion.button
                  className="p-2 rounded-full bg-dark-800 text-dark-400 hover:text-white hover:bg-primary/20 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={nextScreenshot}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
