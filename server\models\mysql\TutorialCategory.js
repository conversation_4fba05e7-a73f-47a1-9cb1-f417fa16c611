const { query } = require('../../config/db');

class TutorialCategory {
  // Find category by ID
  static async findById(id) {
    try {
      const [rows] = await query(
        'SELECT * FROM tutorial_categories WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return this.formatCategory(rows[0]);
    } catch (error) {
      console.error('Error finding tutorial category by ID:', error);
      throw error;
    }
  }

  // Find category by slug
  static async findBySlug(slug) {
    try {
      const [rows] = await query(
        'SELECT * FROM tutorial_categories WHERE slug = ?',
        [slug]
      );

      if (rows.length === 0) {
        return null;
      }

      return this.formatCategory(rows[0]);
    } catch (error) {
      console.error('Error finding tutorial category by slug:', error);
      throw error;
    }
  }

  // Find all categories
  static async findAll(includeInactive = false) {
    try {
      let sql = 'SELECT * FROM tutorial_categories';
      
      if (!includeInactive) {
        sql += ' WHERE is_active = 1';
      }
      
      sql += ' ORDER BY order_num ASC, name ASC';
      
      const [rows] = await query(sql);

      return rows.map(row => this.formatCategory(row));
    } catch (error) {
      console.error('Error finding all tutorial categories:', error);
      throw error;
    }
  }

  // Create a new category
  static async create(categoryData) {
    try {
      const {
        name,
        slug,
        description,
        orderNum = 0,
        isActive = true
      } = categoryData;

      const [result] = await query(
        'INSERT INTO tutorial_categories (name, slug, description, order_num, is_active) VALUES (?, ?, ?, ?, ?)',
        [
          name,
          slug,
          description,
          orderNum,
          isActive ? 1 : 0
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating tutorial category:', error);
      throw error;
    }
  }

  // Update category
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        
        // Handle boolean values
        if (key === 'isActive') {
          fields.push(`${snakeKey} = ?`);
          values.push(value ? 1 : 0);
          return;
        }
        
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      // If there are no fields to update, return the existing category
      if (fields.length === 0) {
        return await this.findById(id);
      }

      await query(
        `UPDATE tutorial_categories SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating tutorial category:', error);
      throw error;
    }
  }

  // Delete category
  static async delete(id) {
    try {
      // First, update any tutorials that use this category to have null category_id
      await query('UPDATE tutorials SET category_id = NULL WHERE category_id = ?', [id]);
      
      // Then delete the category
      await query('DELETE FROM tutorial_categories WHERE id = ?', [id]);
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting tutorial category:', error);
      throw error;
    }
  }

  // Helper method to format category data
  static formatCategory(category) {
    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      orderNum: category.order_num,
      isActive: category.is_active === 1,
      createdAt: category.created_at,
      updatedAt: category.updated_at
    };
  }
}

module.exports = TutorialCategory;
