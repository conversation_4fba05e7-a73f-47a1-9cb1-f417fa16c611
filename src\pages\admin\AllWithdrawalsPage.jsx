import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CurrencyBangladeshiIcon,
  PhoneIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import AdminLayout from '../../components/admin/AdminLayout'

export default function AllWithdrawalsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [withdrawals, setWithdrawals] = useState([])
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [successMessage, setSuccessMessage] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    fetchWithdrawals()
  }, [])

  const fetchWithdrawals = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('adminToken')
      if (!token) {
        navigate('/power/login')
        return
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/withdrawal/admin/all`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch withdrawal history')
      }

      const data = await response.json()
      setWithdrawals(data.data || [])
    } catch (error) {
      console.error('Error fetching withdrawals:', error)
      setError('Failed to load withdrawal history. Please try again.')

      // If token is invalid, redirect to login
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        localStorage.removeItem('adminToken')
        navigate('/power/login')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium bg-accent/20 text-accent border border-accent/30">
            <ClockIcon className="w-3 h-3 mr-1" />
            Pending
          </span>
        )
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Paid
          </span>
        )
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30">
            <XCircleIcon className="w-3 h-3 mr-1" />
            Rejected
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium bg-dark-700 text-dark-300 border border-dark-600">
            {status}
          </span>
        )
    }
  }

  const filteredWithdrawals = selectedStatus === 'all'
    ? withdrawals
    : withdrawals.filter(withdrawal => withdrawal.status === selectedStatus)

  return (
    <AdminLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold gradient-text">Withdrawal History</h1>
          <div className="flex space-x-2">
            <div className="relative">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base bg-dark-800 border-dark-600 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-lg"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
            <button
              onClick={fetchWithdrawals}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-primary hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200"
            >
              <ClockIcon className="h-5 w-5 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {successMessage && (
          <div className="mb-4 bg-green-50 border-l-4 border-green-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700">{successMessage}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setSuccessMessage(null)}
                    className="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    <XCircleIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setError(null)}
                    className="inline-flex bg-red-500/20 rounded-lg p-1.5 text-red-400 hover:bg-red-500/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                  >
                    <XCircleIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : filteredWithdrawals.length === 0 ? (
          <div className="card p-6 text-center">
            <CurrencyBangladeshiIcon className="mx-auto h-12 w-12 text-primary" />
            <h3 className="mt-2 text-sm font-medium text-white">No withdrawals found</h3>
            <p className="mt-1 text-sm text-dark-300">There are no withdrawal records matching your filter.</p>
          </div>
        ) : (
          <div className="card">
            <ul className="divide-y divide-dark-700">
              {filteredWithdrawals.map((withdrawal) => (
                <li key={withdrawal.id || withdrawal._id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <CurrencyBangladeshiIcon className="h-10 w-10 text-white bg-gradient-to-r from-primary to-secondary rounded-full p-2" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-primary">
                            ৳{withdrawal.amount}
                          </div>
                          <div className="text-sm text-dark-300">
                            {withdrawal.user ? (withdrawal.user.name || withdrawal.user.username || 'User #' + withdrawal.userId) : 'User #' + withdrawal.userId}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {getStatusBadge(withdrawal.status)}
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <div className="flex items-center text-sm text-gray-500">
                          <PhoneIcon className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                          <p>
                            bKash: {withdrawal.paymentDetails?.bkashNumber || withdrawal.bkashNumber} ({withdrawal.paymentDetails?.accountType || withdrawal.accountType || 'personal'})
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <ClockIcon className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                        <p>
                          Requested on {new Date(withdrawal.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    {withdrawal.status !== 'pending' && (
                      <div className="mt-2 text-sm text-gray-500">
                        <div className="flex items-center">
                          <CalendarIcon className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                          <p>
                            {withdrawal.status === 'paid' ? 'Paid' : 'Rejected'} on {withdrawal.processedAt ? new Date(withdrawal.processedAt).toLocaleDateString() : 'Unknown date'}
                            {withdrawal.transactionId && ` • Transaction ID: ${withdrawal.transactionId}`}
                          </p>
                        </div>
                        {withdrawal.adminNotes && (
                          <div className="mt-1 ml-6 text-sm text-gray-500">
                            <p>Notes: {withdrawal.adminNotes}</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
