import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import { CheckIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline';

const PendingPaymentsPage = () => {
  const [payments, setPayments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [licenseKey, setLicenseKey] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [actionType, setActionType] = useState(null); // 'approve' or 'reject'

  useEffect(() => {
    fetchPendingPayments();
  }, []);

  const fetchPendingPayments = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');

      // Log the API URL to debug
      console.log('API URL in PendingPaymentsPage:', import.meta.env.VITE_API_URL);

      // Use the environment variable or fallback to port 5001
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      console.log('Using API URL:', apiUrl);

      // Check if token exists
      if (!token) {
        console.error('No admin token found in localStorage');
        setError('Authentication error. Please log in again.');
        setIsLoading(false);
        return;
      }

      const response = await fetch(`${apiUrl}/api/payments/pending`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server response:', errorText);
        throw new Error(`Failed to fetch pending payments: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Payments data received:', data);
      setPayments(data.data || []);
    } catch (err) {
      console.error('Error fetching pending payments:', err);
      setError(`Failed to load pending payments. ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (payment) => {
    setSelectedPayment(payment);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
    setAdminNotes('');
    setLicenseKey('');
    setActionType(null);
  };

  const handleAction = (type) => {
    setActionType(type);

    // Clear any previous license key and focus on the input field when approving
    if (type === 'approve') {
      setLicenseKey(''); // Start with an empty license key field

      // Set a small timeout to ensure the modal is open before focusing
      setTimeout(() => {
        const licenseKeyInput = document.getElementById('licenseKey');
        if (licenseKeyInput) {
          licenseKeyInput.focus();
        }
      }, 100);
    }
  };

  const confirmAction = async () => {
    if (!selectedPayment || !actionType) return;

    // Validate that a license key is provided when approving
    if (actionType === 'approve' && (!licenseKey || licenseKey.trim() === '')) {
      alert('Please enter or generate a license key before approving the payment. This key will be shown in the user\'s dashboard.');

      // Focus on the license key input field
      document.getElementById('licenseKey').focus();
      return;
    }

    setActionLoading(true);

    try {
      const token = localStorage.getItem('adminToken');

      // Check if token exists
      if (!token) {
        console.error('No admin token found in localStorage');
        alert('Authentication error. Please log in again.');
        setActionLoading(false);
        return;
      }

      const endpoint = actionType === 'approve' ? 'approve' : 'reject';

      // Use the environment variable or fallback to port 5001
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      console.log('Using API URL for action:', apiUrl);

      const response = await fetch(`${apiUrl}/api/payments/${selectedPayment.orderId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          adminNotes,
          ...(actionType === 'approve' && licenseKey ? { licenseKey } : {})
        })
      });

      console.log('Action response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server response:', errorText);
        throw new Error(`Failed to ${actionType} payment: ${response.status} ${response.statusText}`);
      }

      // Refresh the payments list
      await fetchPendingPayments();

      // Close the modal
      closeModal();

      // Show success message with license key information if approved
      if (actionType === 'approve') {
        alert(`Payment approved successfully!\n\nThe license key you entered (${licenseKey}) has been assigned to the user and is now visible in their dashboard.`);
      } else {
        alert('Payment rejected successfully');
      }
    } catch (err) {
      console.error(`Error ${actionType}ing payment:`, err);
      alert(`Failed to ${actionType} payment: ${err.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <AdminLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold gradient-text">Pending Payments</h1>
        <p className="mt-1 text-sm text-dark-300">
          Review and verify pending payment transactions
        </p>

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="mt-8 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
            </div>
          </div>
        ) : payments.length === 0 ? (
          <div className="mt-8 card p-6 text-center">
            <p className="text-dark-300">No pending payments found</p>
          </div>
        ) : (
          <div className="mt-8 card">
            <ul className="divide-y divide-dark-700">
              {payments.map((payment) => (
                <li key={payment.orderId}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary truncate">
                          {payment.orderId}
                        </p>
                        <p className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-lg bg-accent/20 text-accent border border-accent/30">
                          Pending
                        </p>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <button
                          onClick={() => handleViewDetails(payment)}
                          className="mr-2 px-3 py-1 border border-dark-600 rounded-lg text-sm font-medium text-dark-300 bg-dark-700 hover:bg-dark-600 hover:text-white transition-all duration-200"
                        >
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          Details
                        </button>
                        <button
                          onClick={() => {
                            setSelectedPayment(payment);
                            handleAction('approve');
                            setIsModalOpen(true);
                          }}
                          className="mr-2 px-3 py-1 border border-transparent rounded-lg text-sm font-medium text-white bg-green-500 hover:bg-green-600 transition-all duration-200"
                        >
                          <CheckIcon className="h-4 w-4 inline mr-1" />
                          Approve
                        </button>
                        <button
                          onClick={() => {
                            setSelectedPayment(payment);
                            handleAction('reject');
                            setIsModalOpen(true);
                          }}
                          className="px-3 py-1 border border-transparent rounded-lg text-sm font-medium text-white bg-red-500 hover:bg-red-600 transition-all duration-200"
                        >
                          <XMarkIcon className="h-4 w-4 inline mr-1" />
                          Reject
                        </button>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {payment.customerInfo.firstName} {payment.customerInfo.lastName}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          {payment.paymentMethod.toUpperCase()} - ৳{payment.amount}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          {payment.plan}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Submitted on {formatDate(payment.createdAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Modal for payment details and actions */}
      {isModalOpen && selectedPayment && (
        <div className="fixed z-50 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-dark-900/80 backdrop-blur-sm"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom card rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-primary to-secondary flex items-center justify-center mr-3">
                        {actionType === 'approve' ? (
                          <CheckIcon className="h-5 w-5 text-white" />
                        ) : actionType === 'reject' ? (
                          <XMarkIcon className="h-5 w-5 text-white" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-white" />
                        )}
                      </div>
                      <h3 className="text-lg leading-6 font-medium gradient-text">
                        {actionType === 'approve' ? 'Approve Payment' :
                         actionType === 'reject' ? 'Reject Payment' :
                         'Payment Details'}
                      </h3>
                    </div>

                    {/* License Key Input - Always visible when approving */}
                    {actionType === 'approve' && (
                      <div className="mt-4 bg-gradient-to-r from-green-500/10 to-primary/10 border border-green-500/30 rounded-lg p-4">
                        <h4 className="text-md font-semibold text-white mb-2 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                          Enter License Key
                        </h4>
                        <p className="text-sm text-green-400 mb-3">
                          <strong>Important:</strong> Please enter a license key manually. This key will be shown in the user's dashboard after approval.
                        </p>
                        <div className="flex items-center">
                          <input
                            type="text"
                            id="licenseKey"
                            name="licenseKey"
                            className="block w-full border border-green-500/30 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 sm:text-sm font-mono bg-dark-800 text-white placeholder-dark-400"
                            placeholder="Enter license key for this user (required)"
                            value={licenseKey}
                            onChange={(e) => setLicenseKey(e.target.value)}
                            required
                          />
                        </div>
                        <p className="mt-2 text-xs text-green-400">
                          Suggested format: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
                        </p>
                      </div>
                    )}

                    <div className="mt-4 border-t border-dark-700 pt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-dark-300">Order ID</p>
                          <p className="mt-1 text-sm text-white">{selectedPayment.orderId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-dark-300">Transaction ID</p>
                          <p className="mt-1 text-sm text-white">{selectedPayment.transactionId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-dark-300">Payment Method</p>
                          <p className="mt-1 text-sm text-white">{selectedPayment.paymentMethod.toUpperCase()}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-dark-300">Amount</p>
                          <p className="mt-1 text-sm text-primary font-semibold">৳{selectedPayment.amount}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-dark-300">Plan</p>
                          <p className="mt-1 text-sm text-white">{selectedPayment.plan}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-dark-300">Date</p>
                          <p className="mt-1 text-sm text-white">{formatDate(selectedPayment.createdAt)}</p>
                        </div>
                      </div>

                      <div className="mt-4">
                        <p className="text-sm font-medium text-dark-300">Customer Information</p>
                        <div className="mt-1 grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-dark-400">Name</p>
                            <p className="text-sm text-white">{selectedPayment.customerInfo.firstName} {selectedPayment.customerInfo.lastName}</p>
                          </div>
                          <div>
                            <p className="text-sm text-dark-400">Email</p>
                            <p className="text-sm text-white">{selectedPayment.customerInfo.email}</p>
                          </div>
                          <div>
                            <p className="text-sm text-dark-400">Phone</p>
                            <p className="text-sm text-white">{selectedPayment.customerInfo.phone}</p>
                          </div>
                          {selectedPayment.customerInfo.address && (
                            <div>
                              <p className="text-sm text-dark-400">Address</p>
                              <p className="text-sm text-white">{selectedPayment.customerInfo.address}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {(actionType === 'approve' || actionType === 'reject') && (
                        <div className="mt-4">
                          <label htmlFor="adminNotes" className="block text-sm font-medium text-white">
                            {actionType === 'approve' ? 'Admin Notes (Optional)' : 'Rejection Reason'}
                          </label>
                          <textarea
                            id="adminNotes"
                            name="adminNotes"
                            rows="3"
                            className="mt-1 block w-full border border-dark-600 rounded-lg shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-dark-800 text-white placeholder-dark-400"
                            placeholder={actionType === 'approve' ? 'Optional notes about this approval' : 'Reason for rejection'}
                            value={adminNotes}
                            onChange={(e) => setAdminNotes(e.target.value)}
                          ></textarea>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-dark-700/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-dark-700">
                {actionType ? (
                  <button
                    type="button"
                    onClick={confirmAction}
                    disabled={actionLoading}
                    className={`w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white sm:ml-3 sm:w-auto sm:text-sm transition-all duration-200 ${
                      actionType === 'approve' ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      actionType === 'approve' ? 'focus:ring-green-500' : 'focus:ring-red-500'
                    } disabled:opacity-50`}
                  >
                    {actionLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      actionType === 'approve' ? 'Approve Payment' : 'Reject Payment'
                    )}
                  </button>
                ) : null}
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-lg border border-dark-600 shadow-sm px-4 py-2 bg-dark-700 text-base font-medium text-dark-300 hover:bg-dark-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-200"
                  onClick={closeModal}
                >
                  {actionType ? 'Cancel' : 'Close'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default PendingPaymentsPage;
