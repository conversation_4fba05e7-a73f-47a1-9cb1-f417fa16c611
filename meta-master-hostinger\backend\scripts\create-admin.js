/**
 * <PERSON><PERSON>t to create an initial admin user
 * 
 * Usage:
 * node scripts/create-admin.js <username> <password>
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Admin = require('../models/Admin');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/meta-master', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected successfully'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function createAdmin() {
  try {
    // Get username and password from command line arguments
    const username = process.argv[2];
    const password = process.argv[3];
    
    if (!username || !password) {
      console.error('Please provide username and password as arguments');
      console.log('Usage: node scripts/create-admin.js <username> <password>');
      process.exit(1);
    }
    
    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ username });
    if (existingAdmin) {
      console.log(`Admin user '${username}' already exists`);
      process.exit(0);
    }
    
    // Create new admin
    const admin = new Admin({
      username,
      password,
      role: 'admin'
    });
    
    // Save admin to database
    await admin.save();
    
    console.log(`Admin user '${username}' created successfully`);
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdmin();
