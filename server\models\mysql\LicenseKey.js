const { query } = require('../../config/db');

class LicenseKey {
  // Find license key by ID
  static async findById(id) {
    try {
      const [rows] = await query(
        'SELECT * FROM license_keys WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      return this.formatLicenseKey(rows[0]);
    } catch (error) {
      console.error('Error finding license key by ID:', error);
      throw error;
    }
  }

  // Find license keys by user ID
  static async findByUserId(userId) {
    try {
      const [rows] = await query(
        'SELECT * FROM license_keys WHERE user_id = ? ORDER BY created_at DESC',
        [userId]
      );

      // Convert snake_case to camelCase for all results
      return rows.map(row => this.formatLicenseKey(row));
    } catch (error) {
      console.error('Error finding license keys by user ID:', error);
      throw error;
    }
  }

  // Find license key by payment ID
  static async findByPaymentId(paymentId) {
    try {
      const [rows] = await query(
        'SELECT * FROM license_keys WHERE payment_id = ?',
        [paymentId]
      );

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      return this.formatLicenseKey(rows[0]);
    } catch (error) {
      console.error('Error finding license key by payment ID:', error);
      throw error;
    }
  }

  // Find license key by criteria
  static async findOne(criteria) {
    try {
      let sql = 'SELECT * FROM license_keys WHERE ';
      const params = [];

      if (criteria.paymentId) {
        sql += 'payment_id = ?';
        params.push(criteria.paymentId);
      } else if (criteria.userId) {
        sql += 'user_id = ? ORDER BY created_at DESC LIMIT 1';
        params.push(criteria.userId);
      } else if (criteria.key) {
        sql += 'license_key = ?';
        params.push(criteria.key);
      } else {
        throw new Error('Invalid search criteria');
      }

      const [rows] = await query(sql, params);

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      return this.formatLicenseKey(rows[0]);
    } catch (error) {
      console.error('Error finding license key:', error);
      throw error;
    }
  }

  // Create a new license key
  static async create(licenseKeyData) {
    try {
      const {
        userId,
        paymentId,
        licenseKey = '',
        key = '',
        plan,
        expiryDate = null,
        status = 'pending',
        paymentStatus = 'pending'
      } = licenseKeyData;

      // Use either licenseKey or key parameter (for compatibility)
      const finalKey = licenseKey || key;

      // Calculate expiry date based on plan if not provided
      let finalExpiryDate = expiryDate;
      if (!finalExpiryDate) {
        const now = new Date();
        if (plan === 'Monthly') {
          finalExpiryDate = new Date(now);
          finalExpiryDate.setMonth(finalExpiryDate.getMonth() + 1);
        } else if (plan === 'Yearly') {
          finalExpiryDate = new Date(now);
          finalExpiryDate.setFullYear(finalExpiryDate.getFullYear() + 1);
        }
        // Lifetime plan has no expiry date (null)
      }

      const [result] = await query(
        'INSERT INTO license_keys (user_id, payment_id, license_key, plan, expiry_date, status, payment_status) ' +
        'VALUES (?, ?, ?, ?, ?, ?, ?)',
        [userId, paymentId, finalKey, plan, finalExpiryDate, status, paymentStatus]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating license key:', error);
      throw error;
    }
  }

  // Update license key
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      await query(
        `UPDATE license_keys SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating license key:', error);
      throw error;
    }
  }

  // Helper method to format license key data
  static formatLicenseKey(licenseKey) {
    return {
      id: licenseKey.id,
      userId: licenseKey.user_id,
      paymentId: licenseKey.payment_id,
      key: licenseKey.license_key,
      licenseKey: licenseKey.license_key, // Add both key and licenseKey for compatibility
      plan: licenseKey.plan,
      purchaseDate: licenseKey.purchase_date,
      expiryDate: licenseKey.expiry_date,
      status: licenseKey.status,
      paymentStatus: licenseKey.payment_status,
      createdAt: licenseKey.created_at,
      updatedAt: licenseKey.updated_at
    };
  }
}

module.exports = LicenseKey;
