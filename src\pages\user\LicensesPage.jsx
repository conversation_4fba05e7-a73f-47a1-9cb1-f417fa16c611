import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  KeyIcon,
  ClipboardDocumentIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function LicensesPage() {
  const [user, setUser] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [copiedLicenseKey, setCopiedLicenseKey] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }

        // Fetch user profile
        const userResponse = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!userResponse.ok) {
          throw new Error('Failed to fetch user data')
        }

        const userData = await userResponse.json()
        setUser(userData.user)
      } catch (error) {
        console.error('Error fetching user data:', error)
        setError('Failed to load user data. Please try again.')

        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [navigate])

  const copyToClipboard = (text, id) => {
    navigator.clipboard.writeText(text)
    setCopiedLicenseKey(id)
    setTimeout(() => setCopiedLicenseKey(null), 2000)
  }

  const getLicenseStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-900/20 text-green-400'
      case 'expired':
        return 'bg-yellow-900/20 text-yellow-400'
      case 'revoked':
        return 'bg-red-900/20 text-red-400'
      default:
        return 'bg-gray-900/20 text-gray-400'
    }
  }

  const getLicenseTypeIcon = (plan) => {
    switch (plan) {
      case 'Monthly':
        return <ArrowPathIcon className="w-5 h-5 text-cyan-400" />
      case 'Yearly':
        return <ShieldCheckIcon className="w-5 h-5 text-purple-400" />
      case 'Lifetime':
        return <DevicePhoneMobileIcon className="w-5 h-5 text-amber-400" />
      default:
        return <KeyIcon className="w-5 h-5 text-primary" />
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-light">Your License Keys</h1>
            <p className="text-text">
              Manage and view your Meta Master license keys
            </p>
          </div>

          <button
            onClick={() => navigate('/pricing')}
            className="px-4 py-2 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
          >
            Purchase New License
          </button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4">
            {error}
          </div>
        ) : (
          <div className="space-y-6">
            {/* License Keys Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                <KeyIcon className="w-5 h-5 mr-2 text-primary" />
                Active License Keys
              </h2>

              {user?.licenseKeys && user.licenseKeys.filter(license => license.paymentStatus === 'approved' && license.status === 'active').length > 0 ? (
                <div className="space-y-4">
                  {user.licenseKeys
                    .filter(license => license.paymentStatus === 'approved' && license.status === 'active')
                    .map((license, index) => (
                      <div key={index} className="bg-dark border border-border rounded-lg p-5">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center">
                              {getLicenseTypeIcon(license.plan)}
                              <span className="text-light font-medium ml-2">{license.plan} Plan</span>
                              <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${getLicenseStatusColor(license.status)}`}>
                                {license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                              </span>
                            </div>
                            <div className="mt-3">
                              <div className="text-xs text-primary mb-1 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                                </svg>
                                Your License Key:
                              </div>
                              <div className="flex items-center">
                                <code className="bg-gradient-to-r from-dark-light to-dark border border-primary/20 px-4 py-3 rounded-md text-sm font-mono text-light font-semibold tracking-wider shadow-lg shadow-primary/10">
                                  {license.key}
                                </code>
                                <button
                                  onClick={() => copyToClipboard(license.key, license._id)}
                                  className="ml-3 bg-primary/10 hover:bg-primary/20 p-2 rounded-md text-primary transition-colors"
                                  title="Copy to clipboard"
                                >
                                  {copiedLicenseKey === license._id ? (
                                    <CheckCircleIcon className="w-5 h-5 text-green-500" />
                                  ) : (
                                    <ClipboardDocumentIcon className="w-5 h-5" />
                                  )}
                                </button>
                              </div>
                            </div>
                          </div>

                          <div className="text-right text-sm text-text">
                            <div>Purchased: {new Date(license.purchaseDate).toLocaleDateString()}</div>
                            {license.expiryDate && (
                              <div>Expires: {new Date(license.expiryDate).toLocaleDateString()}</div>
                            )}
                          </div>
                        </div>

                        <div className="mt-4 pt-4 border-t border-border">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-text">
                              <span className="text-light">Devices:</span> {license.plan === 'Lifetime' ? '2' : '1'} allowed
                            </div>
                            <button className="text-primary text-sm hover:underline">
                              View Details
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                    <KeyIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-light mb-2">No Active Licenses</h3>
                    <p className="text-text mb-6">Purchase a license to unlock all features of Meta Master</p>
                    <button
                      onClick={() => navigate('/pricing')}
                      className="px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
                    >
                      Get Started
                    </button>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Pending License Keys Section */}
            {user?.licenseKeys && user.licenseKeys.filter(license => license.paymentStatus === 'pending').length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                  <KeyIcon className="w-5 h-5 mr-2 text-yellow-400" />
                  Pending License Keys
                </h2>

                <div className="space-y-4">
                  {user.licenseKeys
                    .filter(license => license.paymentStatus === 'pending')
                    .map((license, index) => (
                      <div key={index} className="bg-dark border border-yellow-900/20 rounded-lg p-5">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center">
                              {getLicenseTypeIcon(license.plan)}
                              <span className="text-light font-medium ml-2">{license.plan} Plan</span>
                              <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-yellow-900/20 text-yellow-400">
                                Pending Approval
                              </span>
                            </div>
                            <div className="mt-3">
                              <div className="text-xs text-yellow-400 mb-1 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                                License Key Pending:
                              </div>
                              <div className="flex items-center">
                                <div className="bg-dark-light px-4 py-3 rounded-md text-sm font-mono text-yellow-400/70 tracking-wider border border-yellow-400/20">
                                  Waiting for admin approval
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="text-right text-sm text-text">
                            <div>Purchased: {new Date(license.purchaseDate).toLocaleDateString()}</div>
                          </div>
                        </div>

                        <div className="mt-4 pt-4 border-t border-border">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-yellow-400/80">
                              Your payment is being verified by our team
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </motion.div>
            )}

            {/* Expired/Inactive License Keys Section */}
            {user?.licenseKeys && user.licenseKeys.filter(license => license.paymentStatus === 'approved' && license.status !== 'active').length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                  <KeyIcon className="w-5 h-5 mr-2 text-text" />
                  Expired or Inactive License Keys
                </h2>

                <div className="space-y-4">
                  {user.licenseKeys
                    .filter(license => license.paymentStatus === 'approved' && license.status !== 'active')
                    .map((license, index) => (
                      <div key={index} className="bg-dark border border-border rounded-lg p-5 opacity-70">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center">
                              {getLicenseTypeIcon(license.plan)}
                              <span className="text-light font-medium ml-2">{license.plan} Plan</span>
                              <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${getLicenseStatusColor(license.status)}`}>
                                {license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                              </span>
                            </div>
                            <div className="mt-3">
                              <div className="text-xs text-text mb-1 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-text/70" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                                </svg>
                                Expired License Key:
                              </div>
                              <div className="flex items-center">
                                <code className="bg-dark-light px-4 py-3 rounded-md text-sm font-mono text-text/70 tracking-wider border border-text/10">
                                  {license.key}
                                </code>
                              </div>
                            </div>
                          </div>

                          <div className="text-right text-sm text-text">
                            <div>Purchased: {new Date(license.purchaseDate).toLocaleDateString()}</div>
                            {license.expiryDate && (
                              <div>Expired: {new Date(license.expiryDate).toLocaleDateString()}</div>
                            )}
                          </div>
                        </div>

                        <div className="mt-4 pt-4 border-t border-border">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-text">
                              This license is no longer active
                            </div>
                            <button
                              onClick={() => navigate('/pricing')}
                              className="text-primary text-sm hover:underline"
                            >
                              Renew License
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </motion.div>
            )}

            {/* License Information Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-4 flex items-center">
                <ShieldCheckIcon className="w-5 h-5 mr-2 text-primary" />
                License Information
              </h2>

              <div className="space-y-4 text-text">
                <p>
                  Your Meta Master license key grants you access to all features of the software according to your plan.
                </p>
                <ul className="list-disc pl-5 space-y-2">
                  <li><span className="text-light">Monthly Plan:</span> Allows installation on 1 device, valid for 30 days</li>
                  <li><span className="text-light">Yearly Plan:</span> Allows installation on 1 device, valid for 365 days</li>
                  <li><span className="text-light">Lifetime Plan:</span> Allows installation on 2 devices, never expires</li>
                </ul>
                <p>
                  For any license-related issues or questions, please <a href="/contact" className="text-primary hover:underline">contact our support team</a>.
                </p>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </UserLayout>
  )
}
