import { useState, useEffect } from 'react'
import { 
  getVersionConfig, 
  getCurrentVersion, 
  getFormattedVersion,
  shouldShowVersionIn,
  getVersionBadgeText
} from '../utils/versionManager'

interface UseVersionReturn {
  version: string
  formattedVersion: string
  versionBadgeText: string
  shouldShowInHero: boolean
  shouldShowInFloating: boolean
  shouldShowInFeatures: boolean
  isLatest: boolean
  releaseDate: string
}

/**
 * React hook for accessing version information
 */
export function useVersion(): UseVersionReturn {
  const [versionData, setVersionData] = useState(() => {
    const config = getVersionConfig()
    return {
      version: config.version,
      formattedVersion: getFormattedVersion(),
      versionBadgeText: getVersionBadgeText(),
      shouldShowInHero: config.showInHero,
      shouldShowInFloating: config.showInFloatingElement,
      shouldShowInFeatures: config.showInFeatures,
      isLatest: config.isLatest,
      releaseDate: config.releaseDate
    }
  })

  useEffect(() => {
    const handleVersionChange = (event: CustomEvent) => {
      const config = event.detail
      setVersionData({
        version: config.version,
        formattedVersion: `v${config.version}`,
        versionBadgeText: config.isLatest ? `Version ${config.version} Now Available` : `Version ${config.version}`,
        shouldShowInHero: config.showInHero,
        shouldShowInFloating: config.showInFloatingElement,
        shouldShowInFeatures: config.showInFeatures,
        isLatest: config.isLatest,
        releaseDate: config.releaseDate
      })
    }

    window.addEventListener('versionConfigChanged', handleVersionChange as EventListener)
    
    return () => {
      window.removeEventListener('versionConfigChanged', handleVersionChange as EventListener)
    }
  }, [])

  return versionData
}
