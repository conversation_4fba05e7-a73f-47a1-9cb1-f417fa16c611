import { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  KeyIcon,
  ClipboardDocumentIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CreditCardIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function DashboardPage() {
  const [user, setUser] = useState(null)
  const [payments, setPayments] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [copiedLicenseKey, setCopiedLicenseKey] = useState(null)
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  // Check if user was redirected from checkout page
  useEffect(() => {
    // Check if the user came from the checkout page
    if (location.state && location.state.fromCheckout) {
      setShowPaymentSuccess(true)
      // Hide the success message after 5 seconds
      const timer = setTimeout(() => {
        setShowPaymentSuccess(false)
      }, 5000)

      return () => clearTimeout(timer)
    }
  }, [location])

  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }

        // Fetch user profile
        const userResponse = await fetch('http://localhost:5001/api/users/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!userResponse.ok) {
          throw new Error('Failed to fetch user data')
        }

        const userData = await userResponse.json()
        setUser(userData.user)

        // Fetch payment history
        const paymentsResponse = await fetch('http://localhost:5001/api/users/payments', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!paymentsResponse.ok) {
          throw new Error('Failed to fetch payment data')
        }

        const paymentsData = await paymentsResponse.json()
        console.log('Payment data:', paymentsData) // Debug log
        setPayments(paymentsData.payments || [])
      } catch (error) {
        console.error('Error fetching user data:', error)
        setError('Failed to load user data. Please try again.')

        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [navigate])

  const copyToClipboard = (text, id) => {
    navigator.clipboard.writeText(text)
    setCopiedLicenseKey(id)
    setTimeout(() => setCopiedLicenseKey(null), 2000)
  }

  const handleLogout = () => {
    localStorage.removeItem('userToken')
    navigate('/user/login')
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'verified':
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Approved
          </span>
        )
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="w-3 h-3 mr-1" />
            Pending
          </span>
        )
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="w-3 h-3 mr-1" />
            Rejected
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        )
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-light">Dashboard</h1>
            <p className="text-text">
              Welcome back, {user?.name || 'User'}
            </p>
          </div>

          <button
            onClick={handleLogout}
            className="flex items-center text-text hover:text-light transition-colors"
          >
            <ArrowRightOnRectangleIcon className="w-5 h-5 mr-1" />
            Logout
          </button>
        </div>

        {/* Payment success message */}
        {showPaymentSuccess && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-green-900/20 border border-green-500/30 text-green-400 rounded-lg p-4 mb-6 flex items-start"
          >
            <CheckCircleIcon className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Payment Successful!</p>
              <p className="text-sm">Your payment has been processed successfully. Your license key is now available in the License Keys section below.</p>
            </div>
          </motion.div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4">
            {error}
          </div>
        ) : (
          <div className="space-y-8">
            {/* User Profile Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="bg-gradient-to-r from-primary to-secondary rounded-full p-3">
                    <UserCircleIcon className="w-8 h-8 text-white" />
                  </div>
                  <div className="ml-4">
                    <h2 className="text-xl font-semibold text-light">{user?.name}</h2>
                    <p className="text-text">{user?.email}</p>
                  </div>
                </div>

                <button
                  onClick={() => navigate('/user/profile')}
                  className="text-sm text-primary hover:underline"
                >
                  Edit Profile
                </button>
              </div>
            </motion.div>

            {/* License Keys Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-4 flex items-center">
                <KeyIcon className="w-5 h-5 mr-2 text-primary" />
                Your License Keys
              </h2>

              {user?.licenseKeys && user.licenseKeys.length > 0 ? (
                <div className="space-y-4">
                  {user.licenseKeys.map((license, index) => (
                    <div key={index} className={`bg-dark border ${license.paymentStatus === 'pending' ? 'border-yellow-900/20' : 'border-border'} rounded-lg p-4`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            <span className="text-light font-medium">{license.plan} Plan</span>
                            {license.paymentStatus === 'pending' ? (
                              <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-yellow-900/20 text-yellow-400">
                                Pending Approval
                              </span>
                            ) : (
                              <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                                license.status === 'active' ? 'bg-green-900/20 text-green-400' :
                                license.status === 'expired' ? 'bg-yellow-900/20 text-yellow-400' :
                                'bg-red-900/20 text-red-400'
                              }`}>
                                {license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                              </span>
                            )}
                          </div>
                          <div className="mt-2 flex items-center">
                            {license.paymentStatus === 'pending' ? (
                              <div className="bg-dark-light px-2 py-1 rounded text-sm font-mono text-yellow-400/70">
                                Waiting for admin approval
                              </div>
                            ) : (
                              <>
                                <code className="bg-dark-light px-2 py-1 rounded text-sm font-mono text-light">
                                  {license.key}
                                </code>
                                <button
                                  onClick={() => copyToClipboard(license.key, license._id)}
                                  className="ml-2 text-text hover:text-light transition-colors"
                                  title="Copy to clipboard"
                                >
                                  {copiedLicenseKey === license._id ? (
                                    <CheckCircleIcon className="w-5 h-5 text-green-500" />
                                  ) : (
                                    <ClipboardDocumentIcon className="w-5 h-5" />
                                  )}
                                </button>
                              </>
                            )}
                          </div>
                        </div>

                        <div className="text-right text-sm text-text">
                          <div>Purchased: {new Date(license.purchaseDate).toLocaleDateString()}</div>
                          {license.expiryDate && license.paymentStatus === 'approved' && (
                            <div>Expires: {new Date(license.expiryDate).toLocaleDateString()}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                    <KeyIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-light mb-2">No License Keys Yet</h3>
                    <p className="text-text mb-6">Purchase a license to unlock all features of Meta Master</p>
                    <button
                      onClick={() => navigate('/pricing')}
                      className="px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
                    >
                      Get Started
                    </button>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Payment History Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-4 flex items-center">
                <CreditCardIcon className="w-5 h-5 mr-2 text-primary" />
                Payment History
              </h2>

              {payments && payments.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-border">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Order ID
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {payments.map((payment) => (
                        <tr key={payment._id || payment.orderId} className="hover:bg-dark-light/30">
                          <td className="px-4 py-3 text-sm text-light">
                            {payment.orderId}
                          </td>
                          <td className="px-4 py-3 text-sm text-text">
                            {payment.createdAt ? new Date(payment.createdAt).toLocaleDateString() : 'N/A'}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            {payment.plan}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            ৳{payment.amount}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {getStatusBadge(payment.status)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                    <CreditCardIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-light mb-2">No Payment History</h3>
                    <p className="text-text mb-6">Your payment history will appear here after you make a purchase</p>
                    <button
                      onClick={() => navigate('/pricing')}
                      className="px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
                    >
                      View Pricing Plans
                    </button>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        )}
      </div>
    </UserLayout>
  )
}
