/**
 * Scrolls smoothly to a section on the page by ID
 * @param id The ID of the section to scroll to (without the # prefix)
 * @param offset Optional offset from the top of the section (default: 100px)
 */
export const scrollToSection = (id: string, offset: number = 100): void => {
  // If we're not on the homepage, navigate there first
  if (window.location.pathname !== '/') {
    window.location.href = `/#${id}`;
    return;
  }

  // Find the element
  const element = document.getElementById(id);
  
  if (element) {
    // Calculate position to scroll to (with offset)
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;
    
    // Scroll smoothly to the element
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
};
