import { useState, useEffect } from 'react'
import { 
  getVersionConfig, 
  saveVersionConfig, 
  updateVersion,
  toggleVersionDisplay,
  resetVersionConfig,
  getVersionHistory
} from '../../utils/versionManager'

interface VersionConfig {
  version: string;
  releaseDate: string;
  isLatest: boolean;
  showInHero: boolean;
  showInFloatingElement: boolean;
  showInFeatures: boolean;
}

export default function VersionManager() {
  const [config, setConfig] = useState<VersionConfig>(getVersionConfig())
  const [newVersion, setNewVersion] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [message, setMessage] = useState('')

  useEffect(() => {
    const handleVersionChange = (event: CustomEvent) => {
      setConfig(event.detail)
    }

    window.addEventListener('versionConfigChanged', handleVersionChange as EventListener)
    
    return () => {
      window.removeEventListener('versionConfigChanged', handleVersionChange as EventListener)
    }
  }, [])

  const handleUpdateVersion = () => {
    if (!newVersion.trim()) {
      setMessage('Please enter a version number')
      return
    }

    updateVersion(newVersion.trim())
    setNewVersion('')
    setIsEditing(false)
    setMessage('Version updated successfully!')
    setTimeout(() => setMessage(''), 3000)
  }

  const handleToggleDisplay = (location: 'hero' | 'floating' | 'features', show: boolean) => {
    toggleVersionDisplay(location, show)
    setMessage(`Version display ${show ? 'enabled' : 'disabled'} for ${location}`)
    setTimeout(() => setMessage(''), 3000)
  }

  const handleToggleLatest = () => {
    saveVersionConfig({ isLatest: !config.isLatest })
    setMessage(`Version marked as ${!config.isLatest ? 'latest' : 'not latest'}`)
    setTimeout(() => setMessage(''), 3000)
  }

  const handleReset = () => {
    if (confirm('Are you sure you want to reset to default version configuration?')) {
      resetVersionConfig()
      setMessage('Version configuration reset to defaults')
      setTimeout(() => setMessage(''), 3000)
    }
  }

  const versionHistory = getVersionHistory()

  return (
    <div className="card">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-white">Version Management</h3>
        <button
          onClick={handleReset}
          className="px-3 py-1 text-sm bg-dark-700 text-dark-300 rounded-lg hover:bg-dark-600 hover:text-white transition-all duration-200"
        >
          Reset to Defaults
        </button>
      </div>

      {message && (
        <div className="mb-4 p-3 bg-primary/20 border border-primary/30 text-primary rounded-lg backdrop-blur-sm">
          {message}
        </div>
      )}

      {/* Current Version */}
      <div className="mb-6 p-4 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg border border-primary/20">
        <div className="flex justify-between items-center mb-2">
          <h4 className="font-medium text-white">Current Version</h4>
          <span className={`px-2 py-1 text-xs rounded-lg ${config.isLatest ? 'bg-primary/20 text-primary border border-primary/30' : 'bg-dark-700 text-dark-300 border border-dark-600'}`}>
            {config.isLatest ? 'Latest' : 'Not Latest'}
          </span>
        </div>
        <div className="text-2xl font-bold gradient-text mb-1">v{config.version}</div>
        <div className="text-sm text-dark-300">Released: {config.releaseDate}</div>
      </div>

      {/* Update Version */}
      <div className="mb-6 p-4 bg-dark-700/30 border border-dark-600 rounded-lg">
        <h4 className="font-medium text-white mb-3">Update Version</h4>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-all duration-200"
          >
            Update Version
          </button>
        ) : (
          <div className="flex gap-2">
            <input
              type="text"
              value={newVersion}
              onChange={(e) => setNewVersion(e.target.value)}
              placeholder="e.g., 5.4.0"
              className="flex-1 px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            />
            <button
              onClick={handleUpdateVersion}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-all duration-200"
            >
              Save
            </button>
            <button
              onClick={() => {
                setIsEditing(false)
                setNewVersion('')
              }}
              className="px-4 py-2 bg-dark-700 text-dark-300 rounded-lg hover:bg-dark-600 hover:text-white transition-all duration-200"
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Version Display Settings */}
      <div className="mb-6 p-4 border rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">Version Display Settings</h4>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.isLatest}
              onChange={handleToggleLatest}
              className="mr-2"
            />
            <span>Mark as latest version</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.showInHero}
              onChange={(e) => handleToggleDisplay('hero', e.target.checked)}
              className="mr-2"
            />
            <span>Show version badge in hero section</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.showInFloatingElement}
              onChange={(e) => handleToggleDisplay('floating', e.target.checked)}
              className="mr-2"
            />
            <span>Show version in floating element</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.showInFeatures}
              onChange={(e) => handleToggleDisplay('features', e.target.checked)}
              className="mr-2"
            />
            <span>Show version in features section</span>
          </label>
        </div>
      </div>

      {/* Version History */}
      <div className="p-4 border rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">Version History</h4>
        <div className="space-y-3">
          {versionHistory.map((version, index) => (
            <div key={version.version} className="border-l-4 border-blue-500 pl-4">
              <div className="flex justify-between items-center mb-1">
                <span className="font-medium">v{version.version}</span>
                <span className="text-sm text-gray-500">{version.date}</span>
              </div>
              <ul className="text-sm text-gray-600 list-disc list-inside">
                {version.changes.map((change, changeIndex) => (
                  <li key={changeIndex}>{change}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
