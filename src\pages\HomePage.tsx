import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import Hero from '../components/home/<USER>'
import Features from '../components/home/<USER>'
import Benefits from '../components/home/<USER>'
import Demo from '../components/home/<USER>'
import PricingSection from '../components/pricing/PricingSection'
import SEOContent from '../components/home/<USER>'
import Testimonials from '../components/home/<USER>'
import FAQ from '../components/home/<USER>'
import CTA from '../components/home/<USER>'
import { scrollToSection } from '../utils/scrollUtils'

export default function HomePage() {
  const location = useLocation()

  useEffect(() => {
    // Set page title
    document.title = 'Meta Master - #1 AI Metadata Generator for Microstock | 1200+ Users | CSV Export'

    // Check for affiliate referral in URL
    const searchParams = new URLSearchParams(location.search)
    const affiliateId = searchParams.get('ref')

    // If there's an affiliate ID in the URL, store it in localStorage for 30 days
    if (affiliateId) {
      localStorage.setItem('affiliateId', affiliateId)
      localStorage.setItem('affiliateExpiry', (Date.now() + 30 * 24 * 60 * 60 * 1000).toString())
      console.log('Affiliate ID stored:', affiliateId)
    }
  }, [location.search])

  // Handle hash navigation when the component mounts
  useEffect(() => {
    // Get the hash from the URL (without the # symbol)
    const hash = location.hash.replace('#', '')

    if (hash) {
      // Wait a bit for the page to fully render before scrolling
      setTimeout(() => {
        scrollToSection(hash)
      }, 300)
    } else {
      // If no hash, scroll to top
      window.scrollTo(0, 0)
    }
  }, [location.hash])

  return (
    <div>
      <Hero />
      <Features />
      <Benefits />
      <Demo />
      <SEOContent />
      <PricingSection />
      <Testimonials />
      <FAQ />
      <CTA />
    </div>
  )
}
