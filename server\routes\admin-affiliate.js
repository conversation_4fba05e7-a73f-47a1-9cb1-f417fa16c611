const express = require('express');
const router = express.Router();
const User = require('../models/mysql/User');
const AffiliateTransaction = require('../models/mysql/Affiliate');
const Withdrawal = require('../models/mysql/Withdrawal');
const { authenticate, isAdmin } = require('../middleware/auth');
const { query } = require('../config/db');

/**
 * Helper function to calculate available balance
 * @param {string} userId - User ID
 * @returns {number} Available balance
 */
async function calculateAvailableBalance(userId) {
  try {
    console.log(`Calculating available balance for user ID: ${userId}`);

    // Get user
    let user;
    try {
      user = await User.findById(userId);
      if (!user) {
        console.log(`User not found with ID: ${userId}`);
        return 0;
      }
    } catch (userError) {
      console.error(`Error finding user with ID ${userId}:`, userError);
      return 0;
    }

    // Get total earnings and withdrawn amount
    const totalEarnings = parseFloat(user.affiliateCommission) || 0;
    const withdrawnCommission = parseFloat(user.withdrawnCommission) || 0;
    console.log(`User ${user.username} - Total earnings: ${totalEarnings}, Withdrawn: ${withdrawnCommission}`);

    // Get pending withdrawal requests
    let pendingWithdrawals = [];
    try {
      pendingWithdrawals = await Withdrawal.find({
        userId: userId,
        status: 'pending'
      });
      console.log(`Found ${pendingWithdrawals.length} pending withdrawals for user ${user.username}`);
    } catch (withdrawalError) {
      console.error(`Error finding pending withdrawals for user ${user.username}:`, withdrawalError);
      pendingWithdrawals = [];
    }

    // Calculate total pending withdrawal amount
    const pendingWithdrawalAmount = pendingWithdrawals.reduce(
      (sum, withdrawal) => sum + (typeof withdrawal.amount === 'number' ? withdrawal.amount : 0),
      0
    );
    console.log(`User ${user.username} - Pending withdrawal amount: ${pendingWithdrawalAmount}`);

    // Available balance = total earnings - withdrawn - pending withdrawals
    const availableBalance = totalEarnings - withdrawnCommission - pendingWithdrawalAmount;
    console.log(`User ${user.username} - Available balance: ${availableBalance}`);

    return availableBalance;
  } catch (error) {
    console.error('Error calculating available balance:', error);
    return 0;
  }
}

/**
 * @route   GET /api/admin/affiliate/leaderboard
 * @desc    Get all affiliates with their stats for admin (direct SQL version)
 * @access  Private (Admin only)
 */
router.get('/leaderboard', authenticate, isAdmin, async (req, res) => {
  try {
    console.log('Fetching affiliate leaderboard data (direct SQL version)...');

    // Direct SQL query to get all users with affiliate data
    const [users] = await query(`
      SELECT 
        u.id, 
        u.username, 
        u.name, 
        u.email, 
        u.affiliate_id, 
        COALESCE(u.affiliate_commission, 0) as affiliate_commission, 
        COALESCE(u.withdrawn_commission, 0) as withdrawn_commission,
        u.created_at
      FROM 
        users u
      WHERE 
        u.affiliate_commission > 0 OR u.withdrawn_commission > 0
      ORDER BY 
        u.affiliate_commission DESC
    `);

    console.log(`Found ${users.length} users with affiliate data`);

    // If no users found, return empty array
    if (users.length === 0) {
      console.log('No users with affiliate data found, returning empty array');
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // For each user, get their referral count using direct SQL
    const usersWithReferrals = await Promise.all(users.map(async (user) => {
      console.log(`Processing user ${user.username} (ID: ${user.id})`);

      // Get transaction counts by status using direct SQL
      const [transactionCounts] = await query(`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
          SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
          SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
          SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid
        FROM 
          affiliate_transactions
        WHERE 
          referrer_id = ?
      `, [user.id]);

      const counts = transactionCounts[0] || { total: 0, pending: 0, approved: 0, rejected: 0, paid: 0 };
      
      // Calculate available balance
      let availableBalance = 0;
      try {
        availableBalance = await calculateAvailableBalance(user.id);
      } catch (error) {
        console.error(`Error calculating available balance for user ${user.username}:`, error);
      }

      return {
        _id: user.id,
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        affiliateId: user.affiliate_id,
        totalReferrals: parseInt(counts.total) || 0,
        pendingReferrals: parseInt(counts.pending) || 0,
        approvedReferrals: parseInt(counts.approved) || 0,
        rejectedReferrals: parseInt(counts.rejected) || 0,
        paidReferrals: parseInt(counts.paid) || 0,
        totalEarnings: parseFloat(user.affiliate_commission) || 0,
        withdrawnAmount: parseFloat(user.withdrawn_commission) || 0,
        availableBalance: availableBalance,
        joinedAt: user.created_at
      };
    }));

    // Sort by total earnings (highest first)
    usersWithReferrals.sort((a, b) => {
      // Ensure we're comparing numbers
      const aEarnings = parseFloat(a.totalEarnings) || 0;
      const bEarnings = parseFloat(b.totalEarnings) || 0;
      return bEarnings - aEarnings;
    });

    console.log(`Returning ${usersWithReferrals.length} users with referral data`);

    // Add timestamp to help prevent caching issues
    return res.status(200).json({
      success: true,
      count: usersWithReferrals.length,
      data: usersWithReferrals,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Get affiliate leaderboard error:', error);
    // Return empty data instead of error to prevent breaking the frontend
    return res.status(200).json({
      success: true,
      count: 0,
      data: []
    });
  }
});

module.exports = router;
