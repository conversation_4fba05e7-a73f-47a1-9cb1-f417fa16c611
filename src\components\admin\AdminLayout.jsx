import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  CreditCardIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowRightOnRectangleIcon,
  BanknotesIcon,
  UserGroupIcon,
  GlobeAltIcon,
  CogIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const AdminLayout = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('adminToken');
    if (!token) {
      navigate('/power/login');
      return;
    }

    // Verify token validity by making a request to the backend
    const verifyToken = async () => {
      try {
        // Log the API URL to debug
        console.log('API URL in AdminLayout:', import.meta.env.VITE_API_URL);

        // Use the environment variable or fallback to port 5001
        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
        const response = await fetch(`${apiUrl}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          setIsAuthenticated(true);
        } else {
          // Token is invalid, redirect to login
          localStorage.removeItem('adminToken');
          navigate('/power/login');
        }
      } catch (error) {
        console.error('Authentication error:', error);
        localStorage.removeItem('adminToken');
        navigate('/power/login');
      } finally {
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    navigate('/power/login');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: HomeIcon },
    { name: 'All Payments', href: '/admin/payments', icon: CreditCardIcon },
    { name: 'Pending Verification', href: '/admin/payments/pending', icon: ClockIcon },
    { name: 'Approved Payments', href: '/admin/payments/approved', icon: CheckCircleIcon },
    { name: 'Rejected Payments', href: '/admin/payments/rejected', icon: XCircleIcon },
    { name: 'Pending Withdrawals', href: '/admin/withdrawals', icon: ClockIcon },
    { name: 'All Withdrawals', href: '/admin/all-withdrawals', icon: BanknotesIcon },
    { name: 'Affiliate Applications', href: '/admin/affiliate-applications', icon: UserGroupIcon },
    { name: 'Affiliate Leaderboard', href: '/admin/affiliates', icon: UserGroupIcon },
    { name: 'Website Content', href: '/admin/website-content', icon: GlobeAltIcon },
    { name: 'Website Management', href: '/admin/website-management', icon: CogIcon },
  ];

  return (
    <div className="flex h-screen bg-gradient-to-b from-dark-950 to-dark-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div className="fixed inset-0 bg-dark-900/80 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex flex-col w-64 bg-dark-800/90 backdrop-blur-lg border-r border-dark-700 h-full">
            <div className="flex items-center justify-between flex-shrink-0 px-4 py-4">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-primary to-secondary flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-sm">MM</span>
                </div>
                <h1 className="text-xl font-display font-bold gradient-text">Meta Master Admin</h1>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className="text-dark-300 hover:text-white transition-colors duration-200"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="mt-5 flex-1 flex flex-col overflow-y-auto">
              <nav className="flex-1 px-3 space-y-2">
                {navigation.map((item) => {
                  const isActive = location.pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      onClick={() => setSidebarOpen(false)}
                      className={`${
                        isActive
                          ? 'bg-gradient-to-r from-primary/20 to-secondary/20 text-white border-r-2 border-primary shadow-lg'
                          : 'text-dark-300 hover:bg-dark-700/50 hover:text-white'
                      } group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200`}
                    >
                      <item.icon
                        className={`${
                          isActive ? 'text-primary' : 'text-dark-400 group-hover:text-primary'
                        } mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200`}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </div>
            <div className="p-4 border-t border-dark-700">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-3 py-3 text-sm font-medium text-dark-300 rounded-lg hover:bg-red-500/20 hover:text-red-400 transition-all duration-200 group"
              >
                <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-dark-400 group-hover:text-red-400 transition-colors duration-200" />
                Logout
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-dark-800/50 backdrop-blur-lg border-r border-dark-700">
            <div className="flex items-center flex-shrink-0 px-4 mb-2">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-primary to-secondary flex items-center justify-center mr-3">
                <span className="text-white font-bold text-sm">MM</span>
              </div>
              <h1 className="text-xl font-display font-bold gradient-text">Meta Master Admin</h1>
            </div>
            <div className="mt-5 flex-1 flex flex-col">
              <nav className="flex-1 px-3 space-y-2">
                {navigation.map((item) => {
                  const isActive = location.pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={`${
                        isActive
                          ? 'bg-gradient-to-r from-primary/20 to-secondary/20 text-white border-r-2 border-primary shadow-lg'
                          : 'text-dark-300 hover:bg-dark-700/50 hover:text-white'
                      } group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200`}
                    >
                      <item.icon
                        className={`${
                          isActive ? 'text-primary' : 'text-dark-400 group-hover:text-primary'
                        } mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200`}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </div>
            <div className="p-4 border-t border-dark-700">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-3 py-3 text-sm font-medium text-dark-300 rounded-lg hover:bg-red-500/20 hover:text-red-400 transition-all duration-200 group"
              >
                <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-dark-400 group-hover:text-red-400 transition-colors duration-200" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Mobile header */}
        <div className="md:hidden bg-dark-800/50 backdrop-blur-lg shadow-lg border-b border-dark-700">
          <div className="flex items-center justify-between px-4 py-3">
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-dark-300 hover:text-primary transition-colors duration-200"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            <div className="flex items-center">
              <div className="w-6 h-6 rounded bg-gradient-to-r from-primary to-secondary flex items-center justify-center mr-2">
                <span className="text-white font-bold text-xs">MM</span>
              </div>
              <h1 className="text-lg font-display font-bold gradient-text">Meta Master Admin</h1>
            </div>
            <button
              onClick={handleLogout}
              className="text-dark-300 hover:text-red-400 transition-colors duration-200"
            >
              <ArrowRightOnRectangleIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
