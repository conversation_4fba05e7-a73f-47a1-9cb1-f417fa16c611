import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import SectionHeader from '../ui/SectionHeader'

const testimonials = [
  {
    content: "Meta Master has saved me countless hours of manual metadata entry. The AI is surprisingly accurate and the batch processing feature is a game-changer for my workflow.",
    author: "<PERSON>",
    role: "Professional Photographer",
    image: "/src/assets/images/testimonial-1.jpg"
  },
  {
    content: "My stock photo sales increased by 30% after I started using Meta Master for all my image metadata. The AI generates keywords I wouldn't have thought of myself.",
    author: "<PERSON>",
    role: "Stock Photographer",
    image: "/src/assets/images/testimonial-2.jpg"
  },
  {
    content: "As a vector artist, I was skeptical about AI understanding my work, but Meta Master has been incredibly accurate. The silhouette detection feature is especially impressive.",
    author: "<PERSON>",
    role: "Vector Artist",
    image: "/src/assets/images/testimonial-3.jpg"
  },
  {
    content: "Our design agency processes thousands of assets monthly. Meta Master's batch processing and team features have streamlined our workflow tremendously.",
    author: "<PERSON>",
    role: "Creative Director",
    image: "/src/assets/images/testimonial-4.jpg"
  }
]

export default function Testimonials() {
  const [activeIndex, setActiveIndex] = useState(0)
  
  const nextTestimonial = () => {
    setActiveIndex((prev) => (prev + 1) % testimonials.length)
  }
  
  const prevTestimonial = () => {
    setActiveIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section className="section bg-dark-900/50">
      <div className="container">
        <SectionHeader
          title="What Our Users Say"
          subtitle="Join thousands of satisfied users who have transformed their workflow"
        />
        
        <div className="max-w-4xl mx-auto">
          {/* Testimonial carousel */}
          <div className="relative">
            <AnimatePresence mode="wait">
              {testimonials.map((testimonial, index) => (
                activeIndex === index && (
                  <motion.div
                    key={index}
                    className="bg-dark-800/50 backdrop-blur-sm rounded-xl p-8 border border-dark-700"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="flex flex-col md:flex-row gap-6 items-center">
                      <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-primary/30 flex-shrink-0">
                        <img 
                          src={testimonial.image} 
                          alt={testimonial.author} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      
                      <div className="flex-1">
                        <svg className="h-8 w-8 text-primary/40 mb-4" fill="currentColor" viewBox="0 0 32 32">
                          <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                        </svg>
                        
                        <p className="text-lg mb-4">{testimonial.content}</p>
                        
                        <div>
                          <p className="font-bold">{testimonial.author}</p>
                          <p className="text-dark-400 text-sm">{testimonial.role}</p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )
              ))}
            </AnimatePresence>
            
            {/* Navigation buttons */}
            <div className="flex justify-center gap-4 mt-8">
              <motion.button
                className="p-2 rounded-full bg-dark-800 text-dark-400 hover:text-white hover:bg-primary/20 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={prevTestimonial}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </motion.button>
              
              {testimonials.map((_, index) => (
                <motion.button
                  key={index}
                  className={`w-3 h-3 rounded-full ${
                    activeIndex === index ? 'bg-primary' : 'bg-dark-700'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.8 }}
                  onClick={() => setActiveIndex(index)}
                />
              ))}
              
              <motion.button
                className="p-2 rounded-full bg-dark-800 text-dark-400 hover:text-white hover:bg-primary/20 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={nextTestimonial}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
