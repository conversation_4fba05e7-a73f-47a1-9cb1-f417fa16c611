import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import { 
  EyeIcon, 
  CheckIcon, 
  XMarkIcon, 
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

const AffiliateApplicationsPage = () => {
  const [applications, setApplications] = useState([]);
  const [stats, setStats] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [actionType, setActionType] = useState(null); // 'approve' or 'reject'
  const [commissionRate, setCommissionRate] = useState(0.2);
  const [adminNotes, setAdminNotes] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'pending', 'approved', 'rejected'

  useEffect(() => {
    fetchApplications();
  }, [filter]);

  const fetchApplications = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.append('status', filter);
      }

      const response = await fetch(`${apiUrl}/api/affiliate-application/admin/applications?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch applications');
      }

      const data = await response.json();
      setApplications(data.data.applications);
      setStats(data.data.stats);
    } catch (error) {
      console.error('Error fetching applications:', error);
      setError('Failed to load affiliate applications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAction = async () => {
    if (!selectedApplication || !actionType) return;

    setActionLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const endpoint = actionType === 'approve' ? 'approve' : 'reject';

      const requestBody = {
        adminNotes
      };

      if (actionType === 'approve') {
        requestBody.commissionRate = parseFloat(commissionRate);
      }

      const response = await fetch(`${apiUrl}/api/affiliate-application/admin/${endpoint}/${selectedApplication.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Failed to ${actionType} application`);
      }

      // Refresh applications
      await fetchApplications();
      
      // Close modal and reset state
      setIsModalOpen(false);
      setSelectedApplication(null);
      setActionType(null);
      setAdminNotes('');
      setCommissionRate(0.2);
      
    } catch (error) {
      console.error(`Error ${actionType}ing application:`, error);
      setError(`Failed to ${actionType} application`);
    } finally {
      setActionLoading(false);
    }
  };

  const openActionModal = (application, action) => {
    setSelectedApplication(application);
    setActionType(action);
    setIsModalOpen(true);
    setAdminNotes('');
    setCommissionRate(0.2);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="w-3 h-3 mr-1" />
            Pending
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Approved
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="w-3 h-3 mr-1" />
            Rejected
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-light">Affiliate Applications</h1>
            <p className="text-text">Manage affiliate program applications</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-500/10">
                <PaperAirplaneIcon className="h-6 w-6 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-text text-sm">Total Applications</p>
                <h3 className="text-2xl font-semibold text-light">{stats.total_applications || 0}</h3>
              </div>
            </div>
          </div>

          <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-500/10">
                <ClockIcon className="h-6 w-6 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-text text-sm">Pending Review</p>
                <h3 className="text-2xl font-semibold text-light">{stats.pending_applications || 0}</h3>
              </div>
            </div>
          </div>

          <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-500/10">
                <CheckCircleIcon className="h-6 w-6 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-text text-sm">Approved</p>
                <h3 className="text-2xl font-semibold text-light">{stats.approved_applications || 0}</h3>
              </div>
            </div>
          </div>

          <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-red-500/10">
                <XCircleIcon className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-text text-sm">Rejected</p>
                <h3 className="text-2xl font-semibold text-light">{stats.rejected_applications || 0}</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="mb-6">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'all', label: 'All Applications' },
                { key: 'pending', label: 'Pending' },
                { key: 'approved', label: 'Approved' },
                { key: 'rejected', label: 'Rejected' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    filter === tab.key
                      ? 'border-primary text-primary'
                      : 'border-transparent text-text hover:text-light hover:border-border'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-900/20 border border-red-900/30 text-red-400 rounded-lg p-4">
            {error}
          </div>
        )}

        {/* Applications Table */}
        <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl overflow-hidden">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : applications.length === 0 ? (
            <div className="text-center py-12">
              <UserGroupIcon className="mx-auto h-12 w-12 text-text/50" />
              <h3 className="mt-2 text-sm font-medium text-light">No applications found</h3>
              <p className="mt-1 text-sm text-text">
                {filter === 'all' ? 'No affiliate applications have been submitted yet.' : `No ${filter} applications found.`}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-dark/50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                      Applied Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                      Commission Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {applications.map((application) => (
                    <tr key={application.id} className="hover:bg-dark/30">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-light">{application.user_name}</div>
                          <div className="text-sm text-text">{application.email}</div>
                          <div className="text-xs text-text/70">@{application.username}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(application.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-text">
                        {new Date(application.applied_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-text">
                        {application.commission_rate ? `${(application.commission_rate * 100).toFixed(0)}%` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        {application.status === 'pending' && (
                          <>
                            <button
                              onClick={() => openActionModal(application, 'approve')}
                              className="text-green-400 hover:text-green-300 transition-colors"
                            >
                              <CheckIcon className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => openActionModal(application, 'reject')}
                              className="text-red-400 hover:text-red-300 transition-colors"
                            >
                              <XMarkIcon className="w-5 h-5" />
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => {
                            setSelectedApplication(application);
                            setIsModalOpen(true);
                            setActionType('view');
                          }}
                          className="text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          <EyeIcon className="w-5 h-5" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Action Modal */}
        {isModalOpen && selectedApplication && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-dark border border-border rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-light mb-4">
                {actionType === 'view' ? 'Application Details' :
                 actionType === 'approve' ? 'Approve Application' : 'Reject Application'}
              </h3>

              {/* Application Details */}
              <div className="mb-4 p-4 bg-dark-light/50 rounded-lg">
                <div className="space-y-2">
                  <div>
                    <span className="text-text text-sm">User:</span>
                    <span className="text-light ml-2">{selectedApplication.user_name}</span>
                  </div>
                  <div>
                    <span className="text-text text-sm">Email:</span>
                    <span className="text-light ml-2">{selectedApplication.email}</span>
                  </div>
                  <div>
                    <span className="text-text text-sm">Applied:</span>
                    <span className="text-light ml-2">{new Date(selectedApplication.applied_at).toLocaleDateString()}</span>
                  </div>
                  {selectedApplication.application_reason && (
                    <div>
                      <span className="text-text text-sm">Reason:</span>
                      <p className="text-light mt-1 text-sm">{selectedApplication.application_reason}</p>
                    </div>
                  )}
                  {selectedApplication.admin_notes && (
                    <div>
                      <span className="text-text text-sm">Admin Notes:</span>
                      <p className="text-light mt-1 text-sm">{selectedApplication.admin_notes}</p>
                    </div>
                  )}
                </div>
              </div>

              {actionType !== 'view' && (
                <div className="space-y-4">
                  {actionType === 'approve' && (
                    <div>
                      <label className="block text-sm font-medium text-light mb-2">
                        Commission Rate (%)
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={commissionRate * 100}
                        onChange={(e) => setCommissionRate(parseFloat(e.target.value) / 100)}
                        className="w-full bg-dark border border-border rounded-lg px-3 py-2 text-light focus:outline-none focus:border-primary"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-light mb-2">
                      Admin Notes (Optional)
                    </label>
                    <textarea
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      rows={3}
                      className="w-full bg-dark border border-border rounded-lg px-3 py-2 text-light focus:outline-none focus:border-primary resize-none"
                      placeholder="Add any notes about this decision..."
                    />
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setSelectedApplication(null);
                    setActionType(null);
                    setAdminNotes('');
                    setCommissionRate(0.2);
                  }}
                  className="px-4 py-2 text-text hover:text-light transition-colors"
                >
                  {actionType === 'view' ? 'Close' : 'Cancel'}
                </button>

                {actionType !== 'view' && (
                  <button
                    onClick={handleAction}
                    disabled={actionLoading}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      actionType === 'approve'
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-red-600 hover:bg-red-700 text-white'
                    } disabled:opacity-50`}
                  >
                    {actionLoading ? 'Processing...' :
                     actionType === 'approve' ? 'Approve' : 'Reject'}
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AffiliateApplicationsPage;
