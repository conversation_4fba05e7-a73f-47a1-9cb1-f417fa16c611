# Meta Master Website Deployment Guide

This guide provides instructions for deploying the Meta Master website to Hostinger.

## Overview

The Meta Master website consists of two main parts:
1. **Frontend**: A React application built with Vite
2. **Backend**: A Node.js/Express API server with MongoDB

## Deployment Steps

### Frontend Deployment

1. **Upload the frontend files**:
   - Upload all files from the `dist` directory to the root directory of your Hostinger hosting account.
   - Make sure to include the `.htaccess` file for proper routing.

2. **Configure domain settings**:
   - In your Hostinger control panel, make sure your domain (getmetamaster.com) points to the directory where you uploaded the frontend files.

### Backend Deployment

There are two options for deploying the backend:

#### Option 1: Deploy on the same Hostinger account (if Node.js is supported)

1. **Create a subdomain for the API**:
   - Create a subdomain like `api.getmetamaster.com` in your Hostinger control panel.
   - Point it to a separate directory where you'll upload the backend files.

2. **Upload the backend files**:
   - Upload all files from the `server` directory to the subdomain directory.
   - Rename `package.json.production` to `package.json`.
   - Rename `.env.production` to `.env` and update the MongoDB connection string and other settings.

3. **Install dependencies and start the server**:
   ```
   npm install
   npm start
   ```

4. **Configure the frontend to use the API subdomain**:
   - Update API URLs in the frontend code to point to `https://api.getmetamaster.com` instead of `http://localhost:5001`.

#### Option 2: Deploy the backend on a separate hosting service

If Hostinger doesn't support Node.js or you prefer a dedicated hosting for your API:

1. **Deploy the backend on a service like Render, Railway, or DigitalOcean**:
   - Follow the service-specific instructions for deploying a Node.js application.
   - Use the provided `package.json.production` and `.env.production` files.

2. **Update the frontend to use the external API URL**:
   - Update API URLs in the frontend code to point to your backend service URL.

### MongoDB Setup

1. **Create a MongoDB Atlas account** if you don't have one already.
2. **Create a new cluster** and database for Meta Master.
3. **Update the MongoDB connection string** in the backend `.env` file.
4. **Set up database user credentials** with appropriate permissions.

## Post-Deployment Steps

1. **Test the website** thoroughly to ensure all features work correctly.
2. **Set up SSL certificates** for both the main domain and API subdomain.
3. **Configure backups** for your database and files.
4. **Set up monitoring** to track the health of your application.

## Troubleshooting

### Common Issues

1. **API Connection Issues**:
   - Check CORS settings in the server.js file.
   - Verify that the API URL in the frontend code is correct.
   - Ensure the API server is running and accessible.

2. **MongoDB Connection Issues**:
   - Verify the connection string in the .env file.
   - Check if the IP address is whitelisted in MongoDB Atlas.
   - Ensure the database user has the correct permissions.

3. **Routing Issues**:
   - Make sure the .htaccess file is properly uploaded and configured.
   - Check if the hosting provider supports the rewrite rules.

## Maintenance

1. **Regular Updates**:
   - Keep dependencies updated to ensure security and performance.
   - Apply security patches as needed.

2. **Monitoring**:
   - Regularly check server logs for errors.
   - Monitor database performance and storage usage.

## Contact

For assistance with deployment, contact:
- Email: <EMAIL>
- Facebook: https://www.facebook.com/m.mastersoft/
