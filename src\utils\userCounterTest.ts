/**
 * Test file for the user counter utility
 * Run this to verify the counter is working correctly
 */

import { 
  getCurrentUserCount, 
  getFormattedUserCount, 
  getUserCountForDate, 
  getGrowthStats 
} from './userCounter';

// Test the user counter functionality
export function testUserCounter() {
  console.log('=== User Counter Test ===');
  
  // Test current count
  const currentCount = getCurrentUserCount();
  console.log(`Current user count: ${currentCount}`);
  
  // Test formatted count
  const formattedCount = getFormattedUserCount();
  console.log(`Formatted user count: ${formattedCount}`);
  
  // Test specific dates
  const baseDate = '2024-12-20';
  const baseDateCount = getUserCountForDate(baseDate);
  console.log(`Count on base date (${baseDate}): ${baseDateCount}`);
  
  // Test future dates
  const futureDate = '2024-12-25';
  const futureDateCount = getUserCountForDate(futureDate);
  console.log(`Count on future date (${futureDate}): ${futureDateCount}`);
  
  // Test growth stats
  const growthStats = getGrowthStats();
  console.log('Growth Statistics:', growthStats);
  
  // Test consistency (same date should always return same count)
  const testDate = '2024-12-22';
  const count1 = getUserCountForDate(testDate);
  const count2 = getUserCountForDate(testDate);
  console.log(`Consistency test for ${testDate}: ${count1} === ${count2} ? ${count1 === count2}`);
  
  // Show daily progression for next 7 days
  console.log('\n=== Daily Progression (Next 7 Days) ===');
  const today = new Date();
  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];
    const count = getUserCountForDate(dateStr);
    console.log(`${dateStr}: ${count} users`);
  }
  
  console.log('=== Test Complete ===');
}

// Run the test if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment - you can call testUserCounter() from console
  (window as any).testUserCounter = testUserCounter;
  console.log('User counter test available. Run testUserCounter() in console to test.');
}
