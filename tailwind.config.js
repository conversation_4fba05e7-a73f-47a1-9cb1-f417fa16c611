/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#00A7D9', // Meta Master Blue
          50: '#e6f7fd',
          100: '#cceffa',
          200: '#99dff5',
          300: '#66cfef',
          400: '#33bfea',
          500: '#00AFE5',
          600: '#00A7D9',
          700: '#0086ad',
          800: '#006582',
          900: '#004356',
          950: '#00212b',
        },
        secondary: {
          DEFAULT: '#0096C7', // Slightly darker blue
          50: '#e6f5fa',
          100: '#ccebf5',
          200: '#99d7eb',
          300: '#66c3e0',
          400: '#33afd6',
          500: '#0096C7',
          600: '#0088b3',
          700: '#006a8f',
          800: '#00506b',
          900: '#003548',
          950: '#001a24',
        },
        accent: {
          DEFAULT: '#00D5FC', // Bright cyan
          50: '#e6fbff',
          100: '#ccf7ff',
          200: '#99efff',
          300: '#66e7ff',
          400: '#33dfff',
          500: '#00D5FC',
          600: '#00c0e3',
          700: '#0096b3',
          800: '#007185',
          900: '#004b57',
          950: '#00252b',
        },
        dark: {
          DEFAULT: '#0f172a', // Slate 900
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        display: ['Lexend', 'sans-serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(0, 167, 217, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(0, 167, 217, 0.8)' },
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
