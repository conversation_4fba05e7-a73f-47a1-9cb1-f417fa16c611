# Meta Master Website Instructions

This document provides instructions on how to use, customize, and deploy the Meta Master marketing website.

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Installation

1. Navigate to the project directory:
```bash
cd website-react-new
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Project Structure

The project is organized as follows:

- `src/` - Source code
  - `assets/` - Images, icons, and other static assets
  - `components/` - Reusable UI components
    - `home/` - Components specific to the home page
    - `layout/` - Layout components (<PERSON>er, Footer)
    - `pricing/` - Pricing-related components
    - `ui/` - Generic UI components
  - `contexts/` - React context providers
  - `pages/` - Page components
  - `utils/` - Utility functions
  - `App.tsx` - Main application component
  - `main.tsx` - Application entry point
  - `index.css` - Global styles
- `public/` - Static files that will be served directly

## Customization

### Images

Replace the placeholder images in the `src/assets/images/` directory with actual screenshots and images of the Meta Master software:

1. `logo.svg` - The Meta Master logo
2. `app-preview.png` - Main application preview image
3. `demo-video.png` - Demo video thumbnail
4. `screenshot-1.png` through `screenshot-6.png` - Application screenshots
5. `testimonial-1.jpg` through `testimonial-4.jpg` - User testimonial photos
6. `hero-bg.png` - Hero section background

### Content

Update the content in the component files to reflect the latest features and pricing of Meta Master:

1. `src/components/home/<USER>
2. `src/components/home/<USER>
3. `src/components/home/<USER>
4. `src/components/pricing/PricingSection.tsx` - Pricing plans
5. `src/components/home/<USER>
6. `src/components/home/<USER>

### Colors

The color scheme can be customized in the `tailwind.config.js` file. The current theme uses:

- Primary: Indigo (#6366f1)
- Secondary: Violet (#8b5cf6)
- Accent: Pink (#ec4899)
- Dark: Slate (#0f172a)

## Building for Production

To build the website for production, run:

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory.

## Deployment

### Netlify

1. Install the Netlify CLI:
```bash
npm install -g netlify-cli
```

2. Build the project:
```bash
npm run build
```

3. Deploy to Netlify:
```bash
netlify deploy --prod
```

### Vercel

1. Install the Vercel CLI:
```bash
npm install -g vercel
```

2. Deploy to Vercel:
```bash
vercel --prod
```

### GitHub Pages

1. Install the gh-pages package:
```bash
npm install -g gh-pages
```

2. Build the project:
```bash
npm run build
```

3. Deploy to GitHub Pages:
```bash
gh-pages -d dist
```

## Additional Resources

- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [React Router Documentation](https://reactrouter.com/docs/en/v6)

## Support

If you need assistance with the website, please contact the <NAME_EMAIL>.
