const mysql = require('mysql2/promise');
require('dotenv').config();

// Create a connection pool with error handling
let pool = null;

try {
  pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'Jayed123#@!',
    database: process.env.DB_NAME || 'meta_master',
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    authPlugins: {
      mysql_native_password: () => ({ type: 'mysql_native_password' })
    }
  });
} catch (error) {
  console.error('Failed to create database pool:', error.message);
}

// Test the connection
const testConnection = async () => {
  if (!pool) {
    console.log('Database pool not created - skipping connection test');
    return false;
  }

  try {
    console.log('Attempting to connect to MySQL server...');
    const connection = await pool.getConnection();
    console.log('MySQL connected successfully with database');
    connection.release();
    return true;
  } catch (error) {
    console.error('MySQL connection error:', error.message);

    // Provide more specific error messages based on error code
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('Access denied. Please check your MySQL username and password.');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused. Please check if MySQL server is running.');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error(`Database '${process.env.DB_NAME}' does not exist.`);
    }

    return false;
  }
};

// Helper function to execute queries
const query = async (sql, params) => {
  if (!pool) {
    console.error('Database pool not available');
    if (process.env.NODE_ENV === 'development') {
      console.log('⚠️  Returning empty result - no database connection');
      return [[], {}];
    }
    throw new Error('Database not available');
  }

  try {
    return await pool.execute(sql, params);
  } catch (error) {
    console.error('MySQL query error:', error);
    // For development, return empty result instead of throwing
    if (process.env.NODE_ENV === 'development') {
      console.log('⚠️  Returning empty result due to database error');
      return [[], {}];
    }
    throw error;
  }
};

module.exports = {
  pool,
  testConnection,
  query
};
