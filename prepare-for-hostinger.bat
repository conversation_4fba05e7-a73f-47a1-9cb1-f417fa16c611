@echo off
echo ===================================================
echo Meta Master Website - Prepare for Hostinger Upload
echo ===================================================
echo.

echo Building frontend for production...
call npm run build
echo.

echo Creating deployment directories...
if not exist "hostinger-upload" mkdir hostinger-upload
if not exist "hostinger-upload\frontend" mkdir hostinger-upload\frontend
if not exist "hostinger-upload\backend" mkdir hostinger-upload\backend
echo.

echo Copying frontend files...
xcopy /E /Y dist\* hostinger-upload\frontend\
echo.

echo Copying backend files...
xcopy /E /Y server\* hostinger-upload\backend\
del hostinger-upload\backend\package.json
rename hostinger-upload\backend\package.json.production package.json
del hostinger-upload\backend\.env
rename hostinger-upload\backend\.env.production .env
echo.

echo Copying deployment instructions...
copy DEPLOYMENT.md hostinger-upload\
echo.

echo Creating README file...
echo # Meta Master Website Files for Hostinger > hostinger-upload\README.md
echo. >> hostinger-upload\README.md
echo This package contains all the files needed to deploy the Meta Master website to Hostinger. >> hostinger-upload\README.md
echo. >> hostinger-upload\README.md
echo - frontend/: Contains the React frontend files to be uploaded to the main domain >> hostinger-upload\README.md
echo - backend/: Contains the Node.js backend files to be uploaded to the API subdomain >> hostinger-upload\README.md
echo - DEPLOYMENT.md: Detailed deployment instructions >> hostinger-upload\README.md
echo.

echo Creating ZIP archive...
powershell Compress-Archive -Path hostinger-upload\* -DestinationPath meta-master-hostinger.zip -Force
echo.

echo Done! Files are ready in the 'hostinger-upload' directory and 'meta-master-hostinger.zip' archive.
echo.

pause
