const { query } = require('../../config/db');

class Tutorial {
  // Find tutorial by ID
  static async findById(id) {
    try {
      const [rows] = await query(
        'SELECT t.*, tc.name as category_name, tc.slug as category_slug ' +
        'FROM tutorials t ' +
        'LEFT JOIN tutorial_categories tc ON t.category_id = tc.id ' +
        'WHERE t.id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return this.formatTutorial(rows[0]);
    } catch (error) {
      console.error('Error finding tutorial by ID:', error);
      throw error;
    }
  }

  // Find tutorials by criteria
  static async find(criteria = {}, sort = {}) {
    try {
      let sql = 'SELECT t.*, tc.name as category_name, tc.slug as category_slug ' +
                'FROM tutorials t ' +
                'LEFT JOIN tutorial_categories tc ON t.category_id = tc.id';
      const params = [];

      // Add WHERE clause if criteria provided
      if (Object.keys(criteria).length > 0) {
        const conditions = [];

        Object.entries(criteria).forEach(([key, value]) => {
          // Handle special case for 'category' which maps to category_id
          if (key === 'category') {
            if (value === 'all') {
              return; // Skip this condition for 'all' category
            }
            conditions.push(`tc.slug = ?`);
            params.push(value);
            return;
          }

          // Handle special case for 'isActive' which maps to is_active
          if (key === 'isActive') {
            conditions.push(`t.is_active = ?`);
            params.push(value ? 1 : 0);
            return;
          }

          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          conditions.push(`t.${snakeKey} = ?`);
          params.push(value);
        });

        if (conditions.length > 0) {
          sql += ` WHERE ${conditions.join(' AND ')}`;
        }
      }

      // Add ORDER BY clause if sort provided
      if (Object.keys(sort).length > 0) {
        const sortFields = [];

        Object.entries(sort).forEach(([key, value]) => {
          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          sortFields.push(`t.${snakeKey} ${value === 1 ? 'ASC' : 'DESC'}`);
        });

        sql += ` ORDER BY ${sortFields.join(', ')}`;
      } else {
        // Default sort by order_num ASC, created_at DESC
        sql += ' ORDER BY t.order_num ASC, t.created_at DESC';
      }

      const [rows] = await query(sql, params);

      // Convert snake_case to camelCase for all results
      return rows.map(row => this.formatTutorial(row));
    } catch (error) {
      console.error('Error finding tutorials:', error);
      throw error;
    }
  }

  // Create a new tutorial
  static async create(tutorialData) {
    try {
      const {
        title,
        description,
        duration,
        level,
        videoUrl,
        thumbnailUrl,
        categoryId,
        orderNum = 0,
        isActive = true,
        createdBy
      } = tutorialData;

      const [result] = await query(
        'INSERT INTO tutorials (title, description, duration, level, video_url, thumbnail_url, ' +
        'category_id, order_num, is_active, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          title,
          description,
          duration,
          level,
          videoUrl,
          thumbnailUrl,
          categoryId,
          orderNum,
          isActive ? 1 : 0,
          createdBy
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating tutorial:', error);
      throw error;
    }
  }

  // Update tutorial
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        
        // Handle boolean values
        if (key === 'isActive') {
          fields.push(`${snakeKey} = ?`);
          values.push(value ? 1 : 0);
          return;
        }
        
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      // If there are no fields to update, return the existing tutorial
      if (fields.length === 0) {
        return await this.findById(id);
      }

      await query(
        `UPDATE tutorials SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating tutorial:', error);
      throw error;
    }
  }

  // Delete tutorial
  static async delete(id) {
    try {
      await query('DELETE FROM tutorials WHERE id = ?', [id]);
      return { success: true };
    } catch (error) {
      console.error('Error deleting tutorial:', error);
      throw error;
    }
  }

  // Helper method to format tutorial data
  static formatTutorial(tutorial) {
    return {
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      duration: tutorial.duration,
      level: tutorial.level,
      videoUrl: tutorial.video_url,
      thumbnailUrl: tutorial.thumbnail_url,
      categoryId: tutorial.category_id,
      category: tutorial.category_slug || 'uncategorized',
      categoryName: tutorial.category_name || 'Uncategorized',
      orderNum: tutorial.order_num,
      isActive: tutorial.is_active === 1,
      createdBy: tutorial.created_by,
      createdAt: tutorial.created_at,
      updatedAt: tutorial.updated_at
    };
  }
}

module.exports = Tutorial;
