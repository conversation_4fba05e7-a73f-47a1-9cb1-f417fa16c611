const { query } = require('../config/db');
require('dotenv').config();

async function addAffiliateApplicationSystem() {
  try {
    console.log('Starting affiliate application system migration...');

    // Add new columns to users table
    console.log('Adding affiliate status columns to users table...');
    
    try {
      await query(`
        ALTER TABLE users
        ADD COLUMN affiliate_status ENUM('not_applied', 'pending', 'approved', 'rejected') DEFAULT 'not_applied'
      `);
      console.log('✓ Added affiliate_status column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('✓ affiliate_status column already exists');
      } else {
        throw error;
      }
    }

    try {
      await query(`
        ALTER TABLE users
        ADD COLUMN affiliate_commission_rate DECIMAL(4, 2) DEFAULT 0
      `);
      console.log('✓ Added affiliate_commission_rate column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('✓ affiliate_commission_rate column already exists');
      } else {
        throw error;
      }
    }

    try {
      await query(`
        ALTER TABLE users
        ADD COLUMN affiliate_approved_at DATETIME DEFAULT NULL
      `);
      console.log('✓ Added affiliate_approved_at column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('✓ affiliate_approved_at column already exists');
      } else {
        throw error;
      }
    }

    // Create affiliate_applications table
    console.log('Creating affiliate_applications table...');

    await query(`
      CREATE TABLE IF NOT EXISTS affiliate_applications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        commission_rate DECIMAL(4, 2) DEFAULT 0.2,
        application_reason TEXT,
        admin_notes TEXT,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        reviewed_at DATETIME DEFAULT NULL,
        reviewed_by INT DEFAULT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewed_by) REFERENCES admins(id) ON DELETE SET NULL,
        UNIQUE KEY unique_user_application (user_id)
      )
    `);
    console.log('✓ Created affiliate_applications table');

    // Update existing users who have affiliate commission to approved status
    console.log('Updating existing affiliates to approved status...');

    const [result] = await query(`
      UPDATE users
      SET affiliate_status = 'approved',
          affiliate_commission_rate = 0.2,
          affiliate_approved_at = created_at
      WHERE affiliate_commission > 0 OR withdrawn_commission > 0
    `);

    console.log(`✓ Updated ${result.affectedRows} existing affiliates to approved status`);

    console.log('\n🎉 Affiliate application system migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  addAffiliateApplicationSystem();
}

module.exports = addAffiliateApplicationSystem;
