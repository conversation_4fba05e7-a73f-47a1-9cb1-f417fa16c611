const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    // Try with the provided password
    try {
      console.log('Trying to connect with the provided password...');
      const conn = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'Jayed123#@!'
      });
      console.log('Connected successfully with the provided password!');
      await conn.end();
      return;
    } catch (err) {
      console.log('Failed to connect with the provided password:', err.message);
    }

    console.log('Could not connect with any of the common passwords. Please check your MySQL installation and credentials.');
  } catch (error) {
    console.error('Error testing connection:', error);
  }
}

testConnection();
