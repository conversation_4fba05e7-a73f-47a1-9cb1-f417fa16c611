import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'

interface ButtonProps {
  children: React.ReactNode
  to?: string
  href?: string
  variant?: 'primary' | 'secondary' | 'accent' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  onClick?: () => void
  disabled?: boolean
  type?: 'button' | 'submit' | 'reset'
  fullWidth?: boolean
  icon?: React.ReactNode
  animate?: boolean
}

export default function Button({
  children,
  to,
  href,
  variant = 'primary',
  size = 'md',
  className = '',
  onClick,
  disabled = false,
  type = 'button',
  fullWidth = false,
  icon,
  animate = false,
}: ButtonProps) {
  // Base classes
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  }
  
  // Variant classes
  const variantClasses = {
    primary: 'bg-primary text-white hover:bg-primary-600 focus:ring-primary shadow-md hover:shadow-lg',
    secondary: 'bg-secondary text-white hover:bg-secondary-600 focus:ring-secondary shadow-md hover:shadow-lg',
    accent: 'bg-accent text-white hover:bg-accent-600 focus:ring-accent shadow-md hover:shadow-lg',
    outline: 'border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white focus:ring-primary',
  }
  
  // Disabled classes
  const disabledClasses = 'opacity-50 cursor-not-allowed'
  
  // Full width class
  const fullWidthClass = fullWidth ? 'w-full' : ''
  
  // Combine all classes
  const classes = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${disabled ? disabledClasses : ''} ${fullWidthClass} ${className}`
  
  // Button content
  const content = (
    <>
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </>
  )
  
  // Wrap with motion if animate is true
  const buttonContent = animate ? (
    <motion.span
      className="inline-block"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
    >
      {content}
    </motion.span>
  ) : content
  
  // Render the appropriate element based on props
  if (to) {
    return (
      <Link to={to} className={classes}>
        {buttonContent}
      </Link>
    )
  }
  
  if (href) {
    return (
      <a href={href} className={classes} target="_blank" rel="noopener noreferrer">
        {buttonContent}
      </a>
    )
  }
  
  return (
    <button
      type={type}
      className={classes}
      onClick={onClick}
      disabled={disabled}
    >
      {buttonContent}
    </button>
  )
}
