import { useEffect, useState } from 'react'
import { 
  getCurrentUserCount, 
  getFormattedUserCount, 
  getGrowthStats,
  getUserCountForDate 
} from '../../utils/userCounter'

interface GrowthStats {
  currentCount: number;
  dailyGrowth: number;
  weeklyGrowth: number;
  monthlyGrowth: number;
}

export default function UserCountStats() {
  const [stats, setStats] = useState<GrowthStats | null>(null)
  const [formattedCount, setFormattedCount] = useState('')
  const [exactCount, setExactCount] = useState(0)

  useEffect(() => {
    const growthStats = getGrowthStats()
    const formatted = getFormattedUserCount()
    const exact = getCurrentUserCount()

    setStats(growthStats)
    setFormattedCount(formatted)
    setExactCount(exact)
  }, [])

  if (!stats) {
    return <div>Loading user statistics...</div>
  }

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-white mb-4">Dynamic User Count Statistics</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-gradient-to-r from-primary/20 to-primary/10 p-4 rounded-lg border border-primary/30">
          <div className="text-2xl font-bold text-primary">{formattedCount}</div>
          <div className="text-sm text-dark-300">Displayed Count</div>
        </div>

        <div className="bg-gradient-to-r from-secondary/20 to-secondary/10 p-4 rounded-lg border border-secondary/30">
          <div className="text-2xl font-bold text-secondary">{exactCount.toLocaleString()}</div>
          <div className="text-sm text-dark-300">Exact Count</div>
        </div>

        <div className="bg-gradient-to-r from-accent/20 to-accent/10 p-4 rounded-lg border border-accent/30">
          <div className="text-2xl font-bold text-accent">+{stats.dailyGrowth}</div>
          <div className="text-sm text-dark-300">Daily Growth</div>
        </div>

        <div className="bg-gradient-to-r from-purple-500/20 to-purple-500/10 p-4 rounded-lg border border-purple-500/30">
          <div className="text-2xl font-bold text-purple-400">+{stats.weeklyGrowth}</div>
          <div className="text-sm text-dark-300">Weekly Growth</div>
        </div>
      </div>

      <div className="border-t border-dark-700 pt-4">
        <h4 className="font-medium text-white mb-3">Growth Breakdown</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-dark-300">Monthly Growth:</span>
            <span className="font-medium text-white">+{stats.monthlyGrowth} users</span>
          </div>
          <div className="flex justify-between">
            <span className="text-dark-300">Average Daily Growth:</span>
            <span className="font-medium text-white">~{Math.round(stats.monthlyGrowth / 30)} users/day</span>
          </div>
          <div className="flex justify-between">
            <span className="text-dark-300">Growth Rate:</span>
            <span className="font-medium text-primary">{((stats.monthlyGrowth / (stats.currentCount - stats.monthlyGrowth)) * 100).toFixed(1)}% monthly</span>
          </div>
        </div>
      </div>

      <div className="border-t border-dark-700 pt-4 mt-4">
        <h4 className="font-medium text-white mb-3">How It Works</h4>
        <div className="text-sm text-dark-300 space-y-1">
          <p>• Base count: 1,200 users (as of July 29, 2025)</p>
          <p>• Daily growth: 8-12 users (averages ~10 daily)</p>
          <p>• Uses seeded random numbers for consistency</p>
          <p>• Same date always returns same count</p>
          <p>• Displayed count rounded to nearest 50 for realism</p>
        </div>
      </div>
    </div>
  )
}
