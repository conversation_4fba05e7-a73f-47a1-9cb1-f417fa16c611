import { useState, useEffect } from 'react'
import { 
  getCurrentUserCount, 
  getFormattedUserCount, 
  getGrowthStats 
} from '../utils/userCounter'

interface UseUserCountReturn {
  exactCount: number
  formattedCount: string
  isLoading: boolean
  growthStats: {
    currentCount: number
    dailyGrowth: number
    weeklyGrowth: number
    monthlyGrowth: number
  } | null
}

/**
 * React hook for accessing dynamic user count data
 */
export function useUserCount(): UseUserCountReturn {
  const [exactCount, setExactCount] = useState(0)
  const [formattedCount, setFormattedCount] = useState('1200+')
  const [isLoading, setIsLoading] = useState(true)
  const [growthStats, setGrowthStats] = useState(null)

  useEffect(() => {
    try {
      const exact = getCurrentUserCount()
      const formatted = getFormattedUserCount()
      const stats = getGrowthStats()

      setExactCount(exact)
      setFormattedCount(formatted)
      setGrowthStats(stats)
    } catch (error) {
      console.error('Error calculating user count:', error)
      // Fallback to default values
      setExactCount(1200)
      setFormattedCount('1200+')
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    exactCount,
    formattedCount,
    isLoading,
    growthStats
  }
}

/**
 * Simple hook that just returns the formatted count
 */
export function useFormattedUserCount(): string {
  const [formattedCount, setFormattedCount] = useState('1200+')

  useEffect(() => {
    try {
      const formatted = getFormattedUserCount()
      setFormattedCount(formatted)
    } catch (error) {
      console.error('Error getting formatted user count:', error)
      // Keep default value
    }
  }, [])

  return formattedCount
}
