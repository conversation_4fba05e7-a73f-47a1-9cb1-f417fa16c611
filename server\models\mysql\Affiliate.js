const { query } = require('../../config/db');

class AffiliateTransaction {
  // Find transaction by ID
  static async findById(id) {
    try {
      const [rows] = await query(
        'SELECT * FROM affiliate_transactions WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      return this.formatTransaction(rows[0]);
    } catch (error) {
      console.error('Error finding affiliate transaction by ID:', error);
      throw error;
    }
  }

  // Find transactions by criteria
  static async find(criteria = {}, sort = {}) {
    try {
      console.log('Finding affiliate transactions with criteria:', JSON.stringify(criteria));

      // Handle MongoDB-style queries
      if (criteria.referrer && typeof criteria.referrer === 'string') {
        // If referrer is a string (ID), convert it to referrer_id for MySQL
        criteria.referrerId = criteria.referrer;
        delete criteria.referrer;
      }

      // Handle referrerId specifically
      if (criteria.referrerId) {
        console.log('Using referrerId:', criteria.referrerId);
      }

      // Handle MongoDB-style populate in the criteria
      let includeReferredUser = false;
      let includePayment = false;

      // Check if we need to populate related data based on MongoDB-style populate
      if (criteria.populate) {
        if (criteria.populate.includes('referredUser')) {
          includeReferredUser = true;
        }
        if (criteria.populate.includes('payment')) {
          includePayment = true;
        }
        delete criteria.populate;
      }

      // First check if the affiliate_transactions table exists
      try {
        const [tables] = await query('SHOW TABLES LIKE "affiliate_transactions"');
        if (tables.length === 0) {
          console.error('affiliate_transactions table does not exist');
          return [];
        }
      } catch (tableError) {
        console.error('Error checking if affiliate_transactions table exists:', tableError);
        return [];
      }

      // Build the SQL query
      let sqlQuery = 'SELECT at.* FROM affiliate_transactions at';
      const params = [];

      // Only join with other tables if they exist and we need their data
      try {
        // Check if users table exists
        const [userTables] = await query('SHOW TABLES LIKE "users"');
        if (userTables.length > 0) {
          sqlQuery = 'SELECT at.*, ' +
                    'u1.id as referrer_id, u1.username as referrer_username, u1.name as referrer_name, u1.email as referrer_email, ' +
                    'u2.id as referred_user_id, u2.username as referred_username, u2.name as referred_name, u2.email as referred_email';

          // Check if payments table exists
          const [paymentTables] = await query('SHOW TABLES LIKE "payments"');
          if (paymentTables.length > 0) {
            sqlQuery += ', p.id as payment_id, p.order_id, p.amount, p.plan as payment_plan, p.created_at as payment_created_at';
          }

          sqlQuery += ' FROM affiliate_transactions at ' +
                    'LEFT JOIN users u1 ON at.referrer_id = u1.id ' +
                    'LEFT JOIN users u2 ON at.referred_user_id = u2.id';

          if (paymentTables.length > 0) {
            sqlQuery += ' LEFT JOIN payments p ON at.payment_id = p.id';
          }
        }
      } catch (joinError) {
        console.error('Error checking tables for joins:', joinError);
        // Fallback to simple query without joins
        sqlQuery = 'SELECT * FROM affiliate_transactions';
      }

      // Add WHERE clause if criteria provided
      if (Object.keys(criteria).length > 0) {
        const conditions = [];

        Object.entries(criteria).forEach(([key, value]) => {
          // Handle special case for 'referrerId' which maps to referrer_id
          if (key === 'referrerId') {
            conditions.push(`at.referrer_id = ?`);
            params.push(value);
            return;
          }

          // Handle special case for 'referredUserId' which maps to referred_user_id
          if (key === 'referredUserId') {
            conditions.push(`at.referred_user_id = ?`);
            params.push(value);
            return;
          }

          // Handle special case for 'paymentId' which maps to payment_id
          if (key === 'paymentId') {
            conditions.push(`at.payment_id = ?`);
            params.push(value);
            return;
          }

          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          conditions.push(`at.${snakeKey} = ?`);
          params.push(value);
        });

        sqlQuery += ` WHERE ${conditions.join(' AND ')}`;
      }

      // Add ORDER BY clause if sort provided
      if (Object.keys(sort).length > 0) {
        const sortFields = [];

        Object.entries(sort).forEach(([key, value]) => {
          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          sortFields.push(`at.${snakeKey} ${value === 1 ? 'ASC' : 'DESC'}`);
        });

        sqlQuery += ` ORDER BY ${sortFields.join(', ')}`;
      } else {
        // Default sort by created_at DESC
        sqlQuery += ' ORDER BY at.created_at DESC';
      }

      console.log('Executing SQL query:', sqlQuery, 'with params:', params);
      const [rows] = await query(sqlQuery, params);
      console.log('Query returned rows:', rows.length);

      // Convert snake_case to camelCase for all results
      const transactions = rows.map(row => {
        const transaction = this.formatTransaction(row);

        // Add referredUser data if available
        if (row.referred_user_id) {
          transaction.referredUser = {
            id: row.referred_user_id,
            username: row.referred_username,
            name: row.referred_name,
            email: row.referred_email
          };
        }

        // Add payment data if available
        if (row.payment_id) {
          transaction.payment = {
            id: row.payment_id,
            orderId: row.order_id,
            amount: row.amount,
            plan: row.payment_plan,
            createdAt: row.payment_created_at
          };
        }

        return transaction;
      });

      // Add MongoDB-compatible methods for backward compatibility
      // These methods need to return the transactions array to maintain chainability
      const enhancedTransactions = [...transactions];

      enhancedTransactions.populate = function() {
        console.log('Populate method called (MongoDB compatibility)');
        return this;
      };

      enhancedTransactions.sort = function() {
        console.log('Sort method called (MongoDB compatibility)');
        return this;
      };

      console.log(`Returning ${enhancedTransactions.length} transactions`);
      return enhancedTransactions;
    } catch (error) {
      console.error('Error finding affiliate transactions:', error);
      // Return empty array instead of throwing error to prevent breaking the application
      return [];
    }
  }

  // Create a new transaction
  static async create(transactionData) {
    try {
      const {
        referrerId,
        referredUserId,
        paymentId,
        originalAmount,
        commissionAmount,
        commissionRate = 0.2,
        status = 'pending',
        plan
      } = transactionData;

      const [result] = await query(
        'INSERT INTO affiliate_transactions (referrer_id, referred_user_id, payment_id, original_amount, ' +
        'commission_amount, commission_rate, status, plan) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [referrerId, referredUserId, paymentId, originalAmount, commissionAmount, commissionRate, status, plan]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating affiliate transaction:', error);
      throw error;
    }
  }

  // Update transaction
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      await query(
        `UPDATE affiliate_transactions SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating affiliate transaction:', error);
      throw error;
    }
  }

  // Get affiliate statistics
  static async getAffiliateStats(userId) {
    try {
      console.log(`Getting affiliate stats for user ID: ${userId}`);

      let totalReferrals = 0;
      let totalCommission = 0;
      let pendingCommission = 0;

      // First check if the users table exists
      try {
        const [userTables] = await query('SHOW TABLES LIKE "users"');
        if (userTables.length > 0) {
          // Get total referrals count
          try {
            const [referralsResult] = await query(
              'SELECT COUNT(*) as total FROM users WHERE referred_by = ?',
              [userId]
            );
            totalReferrals = referralsResult[0].total || 0;
            console.log(`User ${userId} has ${totalReferrals} referrals`);
          } catch (referralsError) {
            console.error('Error getting referrals count:', referralsError);
            // Check if the referred_by column exists
            try {
              const [columns] = await query('SHOW COLUMNS FROM users LIKE "referred_by"');
              if (columns.length === 0) {
                console.error('referred_by column does not exist in users table');
              }
            } catch (columnError) {
              console.error('Error checking referred_by column:', columnError);
            }
          }
        } else {
          console.error('users table does not exist');
        }
      } catch (tableError) {
        console.error('Error checking if users table exists:', tableError);
      }

      // Check if the affiliate_transactions table exists
      try {
        const [tables] = await query('SHOW TABLES LIKE "affiliate_transactions"');
        if (tables.length > 0) {
          // Get total commission earned
          try {
            const [commissionResult] = await query(
              'SELECT SUM(commission_amount) as total FROM affiliate_transactions ' +
              'WHERE referrer_id = ? AND status IN ("approved", "paid")',
              [userId]
            );
            totalCommission = commissionResult[0].total || 0;
            console.log(`User ${userId} has ${totalCommission} total commission`);
          } catch (commissionError) {
            console.error('Error getting total commission:', commissionError);
          }

          // Get pending commission
          try {
            const [pendingResult] = await query(
              'SELECT SUM(commission_amount) as total FROM affiliate_transactions ' +
              'WHERE referrer_id = ? AND status = "pending"',
              [userId]
            );
            pendingCommission = pendingResult[0].total || 0;
            console.log(`User ${userId} has ${pendingCommission} pending commission`);
          } catch (pendingError) {
            console.error('Error getting pending commission:', pendingError);
          }
        } else {
          console.error('affiliate_transactions table does not exist');
        }
      } catch (tableError) {
        console.error('Error checking if affiliate_transactions table exists:', tableError);
      }

      return {
        totalReferrals,
        totalCommission,
        pendingCommission
      };
    } catch (error) {
      console.error('Error getting affiliate stats:', error);
      // Return default values instead of throwing error
      return {
        totalReferrals: 0,
        totalCommission: 0,
        pendingCommission: 0
      };
    }
  }

  // Helper method to format transaction data
  static formatTransaction(transaction) {
    return {
      id: transaction.id,
      referrerId: transaction.referrer_id,
      referredUserId: transaction.referred_user_id,
      paymentId: transaction.payment_id,
      originalAmount: transaction.original_amount,
      commissionAmount: transaction.commission_amount,
      commissionRate: transaction.commission_rate,
      status: transaction.status,
      plan: transaction.plan,
      createdAt: transaction.created_at,
      updatedAt: transaction.updated_at
    };
  }
}

module.exports = AffiliateTransaction;
