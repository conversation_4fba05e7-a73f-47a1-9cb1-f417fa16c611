/**
 * Seed script for tutorials and tutorial categories
 * 
 * Run this script to populate the tutorial tables with initial data
 * Usage: node seedTutorials.js
 */

require('dotenv').config();
const { TutorialCategory, Tutorial } = require('../models/mysql');
const { query } = require('../config/db');

// Initial tutorial categories
const categories = [
  {
    name: 'Basics',
    slug: 'basics',
    description: 'Fundamental tutorials for getting started with Meta Master',
    orderNum: 1,
    isActive: true
  },
  {
    name: 'Advanced Features',
    slug: 'advanced',
    description: 'Advanced tutorials for power users',
    orderNum: 2,
    isActive: true
  },
  {
    name: 'Export Options',
    slug: 'export',
    description: 'Tutorials about exporting metadata in different formats',
    orderNum: 3,
    isActive: true
  }
];

// Initial tutorials
const tutorials = [
  {
    title: 'Getting Started with Meta Master',
    description: 'Learn the basics of Meta Master and how to set up your first project.',
    duration: '5 min',
    level: 'Beginner',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    thumbnailUrl: '/images/tutorials/default.jpg',
    category: 'basics',
    orderNum: 1,
    isActive: true
  },
  {
    title: 'Understanding Metadata',
    description: 'Learn about metadata and why it\'s important for your images.',
    duration: '8 min',
    level: 'Beginner',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    thumbnailUrl: '/images/tutorials/default.jpg',
    category: 'basics',
    orderNum: 2,
    isActive: true
  },
  {
    title: 'Batch Processing Images',
    description: 'Learn how to process multiple images at once to save time.',
    duration: '12 min',
    level: 'Intermediate',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    thumbnailUrl: '/images/tutorials/default.jpg',
    category: 'advanced',
    orderNum: 1,
    isActive: true
  },
  {
    title: 'Working with Transparent PNGs',
    description: 'Master the techniques for handling transparent PNG files.',
    duration: '10 min',
    level: 'Intermediate',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    thumbnailUrl: '/images/tutorials/default.jpg',
    category: 'advanced',
    orderNum: 2,
    isActive: true
  },
  {
    title: 'Exporting to Getty Images Format',
    description: 'Learn how to export your metadata in Getty Images compatible format.',
    duration: '7 min',
    level: 'Advanced',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    thumbnailUrl: '/images/tutorials/default.jpg',
    category: 'export',
    orderNum: 1,
    isActive: true
  },
  {
    title: 'Customizing Keywords',
    description: 'Learn how to create and manage custom keywords for your images.',
    duration: '9 min',
    level: 'Intermediate',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    thumbnailUrl: '/images/tutorials/default.jpg',
    category: 'advanced',
    orderNum: 3,
    isActive: true
  }
];

// Seed function
async function seedTutorials() {
  try {
    console.log('Starting tutorial seeding...');

    // Connect to database
    await new Promise((resolve, reject) => {
      query('SELECT 1')
        .then(() => {
          console.log('Database connected');
          resolve();
        })
        .catch(err => {
          console.error('Database connection error:', err);
          reject(err);
        });
    });

    // Clear existing data
    console.log('Clearing existing tutorial data...');
    await query('DELETE FROM tutorials');
    await query('DELETE FROM tutorial_categories');
    
    // Reset auto-increment
    await query('ALTER TABLE tutorials AUTO_INCREMENT = 1');
    await query('ALTER TABLE tutorial_categories AUTO_INCREMENT = 1');

    // Create categories
    console.log('Creating tutorial categories...');
    const categoryMap = {};
    
    for (const category of categories) {
      const createdCategory = await TutorialCategory.create(category);
      categoryMap[category.slug] = createdCategory.id;
      console.log(`Created category: ${category.name}`);
    }

    // Create tutorials
    console.log('Creating tutorials...');
    for (const tutorial of tutorials) {
      const categoryId = categoryMap[tutorial.category];
      
      if (!categoryId) {
        console.warn(`Category not found for tutorial: ${tutorial.title}`);
        continue;
      }
      
      const createdTutorial = await Tutorial.create({
        ...tutorial,
        categoryId
      });
      
      console.log(`Created tutorial: ${tutorial.title}`);
    }

    console.log('Tutorial seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding tutorials:', error);
    process.exit(1);
  }
}

// Run the seed function
seedTutorials();
