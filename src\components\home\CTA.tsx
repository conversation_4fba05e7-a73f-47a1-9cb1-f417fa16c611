import { motion } from 'framer-motion'
import { Link, useNavigate } from 'react-router-dom'

export default function CTA() {
  const navigate = useNavigate()

  // Function to check authentication before redirecting to download
  const handleDownloadClick = (e: React.MouseEvent) => {
    e.preventDefault()
    const token = localStorage.getItem('userToken')

    if (token) {
      // User is authenticated, redirect to download page
      navigate('/download')
    } else {
      // User is not authenticated, redirect to login page with return path
      navigate('/user/login', {
        state: {
          returnTo: '/download'
        }
      })
    }
  }
  return (
    <section className="section bg-[#051824] py-20">
      <div className="container">
        <div className="relative rounded-xl overflow-hidden border border-cyan-500/20">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-900/20 via-[#072a3a] to-cyan-900/20"></div>

          {/* Content */}
          <div className="relative z-10 py-16 px-8 md:py-20 md:px-12 text-center">
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-6 text-white"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Ready to <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500">Transform</span> Your Workflow?
            </motion.h2>

            <motion.p
              className="text-lg text-gray-400 max-w-2xl mx-auto mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Join thousands of microstock contributors who are saving time and increasing their earnings with Meta Master.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <a
                href="/download"
                onClick={handleDownloadClick}
                className="px-8 py-3 bg-gradient-to-r from-cyan-600 to-blue-700 hover:from-cyan-700 hover:to-blue-800 text-white font-medium rounded-md shadow-lg shadow-cyan-900/30 transition-all duration-300"
              >
                Download Now
              </a>
              <Link
                to="/demo"
                className="px-8 py-3 bg-[#072a3a] text-cyan-400 border border-cyan-500/30 hover:bg-cyan-900/10 font-medium rounded-md transition-all duration-300"
              >
                See Demo
              </Link>
            </motion.div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-24 h-24 bg-gradient-radial from-cyan-600/20 to-transparent blur-xl"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-radial from-cyan-600/20 to-transparent blur-xl"></div>
        </div>
      </div>
    </section>
  )
}
