/**
 * Dynamic User Counter Utility
 * Calculates the current user count based on a base number plus daily growth
 */

interface UserCountConfig {
  baseCount: number;
  baseDate: string; // ISO date string when the base count was accurate
  dailyGrowthMin: number;
  dailyGrowthMax: number;
  randomBonusMin: number;
  randomBonusMax: number;
}

const USER_COUNT_CONFIG: UserCountConfig = {
  baseCount: 1200, // Starting count as of the base date
  baseDate: '2025-07-29', // Date when we had 1200 users
  dailyGrowthMin: 8,
  dailyGrowthMax: 12,
  randomBonusMin: 0,
  randomBonusMax: 0
};

/**
 * Get a consistent random number for a given date
 * This ensures the same "random" number is generated for the same date
 */
function getSeededRandom(seed: string): number {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash) / 2147483647; // Normalize to 0-1
}

/**
 * Get a random number between min and max using a seed
 */
function getSeededRandomBetween(min: number, max: number, seed: string): number {
  const random = getSeededRandom(seed);
  return Math.floor(random * (max - min + 1)) + min;
}

/**
 * Calculate the current user count based on daily growth
 */
export function getCurrentUserCount(): number {
  const config = USER_COUNT_CONFIG;
  const baseDate = new Date(config.baseDate);
  const currentDate = new Date();
  
  // Calculate days since base date
  const timeDiff = currentDate.getTime() - baseDate.getTime();
  const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
  
  if (daysDiff < 0) {
    // If somehow we're before the base date, return base count
    return config.baseCount;
  }
  
  let totalGrowth = 0;
  
  // Calculate growth for each day
  for (let day = 0; day < daysDiff; day++) {
    const dayDate = new Date(baseDate);
    dayDate.setDate(baseDate.getDate() + day);
    const dayString = dayDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Daily growth (10-15 users)
    const dailyGrowth = getSeededRandomBetween(
      config.dailyGrowthMin,
      config.dailyGrowthMax,
      `daily-${dayString}`
    );
    
    // Random bonus (10-15 additional users)
    const randomBonus = getSeededRandomBetween(
      config.randomBonusMin,
      config.randomBonusMax,
      `bonus-${dayString}`
    );
    
    totalGrowth += dailyGrowth + randomBonus;
  }
  
  return config.baseCount + totalGrowth;
}

/**
 * Get the user count formatted for display
 */
export function getFormattedUserCount(): string {
  const count = getCurrentUserCount();
  
  // Round to nearest 50 for a more realistic look
  const roundedCount = Math.round(count / 50) * 50;
  
  return `${roundedCount}+`;
}

/**
 * Get the exact user count (for internal use)
 */
export function getExactUserCount(): number {
  return getCurrentUserCount();
}

/**
 * Get user count for a specific date (useful for testing)
 */
export function getUserCountForDate(targetDate: string): number {
  const config = USER_COUNT_CONFIG;
  const baseDate = new Date(config.baseDate);
  const checkDate = new Date(targetDate);
  
  const timeDiff = checkDate.getTime() - baseDate.getTime();
  const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
  
  if (daysDiff < 0) {
    return config.baseCount;
  }
  
  let totalGrowth = 0;
  
  for (let day = 0; day < daysDiff; day++) {
    const dayDate = new Date(baseDate);
    dayDate.setDate(baseDate.getDate() + day);
    const dayString = dayDate.toISOString().split('T')[0];
    
    const dailyGrowth = getSeededRandomBetween(
      config.dailyGrowthMin,
      config.dailyGrowthMax,
      `daily-${dayString}`
    );
    
    const randomBonus = getSeededRandomBetween(
      config.randomBonusMin,
      config.randomBonusMax,
      `bonus-${dayString}`
    );
    
    totalGrowth += dailyGrowth + randomBonus;
  }
  
  return config.baseCount + totalGrowth;
}

/**
 * Get growth statistics for display
 */
export function getGrowthStats(): {
  currentCount: number;
  dailyGrowth: number;
  weeklyGrowth: number;
  monthlyGrowth: number;
} {
  const today = new Date().toISOString().split('T')[0];
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStr = yesterday.toISOString().split('T')[0];
  
  const weekAgo = new Date();
  weekAgo.setDate(weekAgo.getDate() - 7);
  const weekAgoStr = weekAgo.toISOString().split('T')[0];
  
  const monthAgo = new Date();
  monthAgo.setDate(monthAgo.getDate() - 30);
  const monthAgoStr = monthAgo.toISOString().split('T')[0];
  
  const currentCount = getUserCountForDate(today);
  const yesterdayCount = getUserCountForDate(yesterdayStr);
  const weekAgoCount = getUserCountForDate(weekAgoStr);
  const monthAgoCount = getUserCountForDate(monthAgoStr);
  
  return {
    currentCount,
    dailyGrowth: currentCount - yesterdayCount,
    weeklyGrowth: currentCount - weekAgoCount,
    monthlyGrowth: currentCount - monthAgoCount
  };
}
