const express = require('express');
const router = express.Router();
const { Tutorial, TutorialCategory } = require('../models/mysql');
const { authenticate, isAdmin } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadDir = path.join(__dirname, '../../public/uploads/tutorials');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'tutorial-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit for videos
  fileFilter: function(req, file, cb) {
    const filetypes = /jpeg|jpg|png|gif|svg|mp4|webm|mov|avi|mkv/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only image and video files are allowed!'));
  }
});

/**
 * @route   GET /api/tutorials
 * @desc    Get all tutorials
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    const { category = 'all', active = true } = req.query;
    
    // Build criteria object
    const criteria = {};
    if (category !== 'all') {
      criteria.category = category;
    }
    if (active === 'true' || active === true) {
      criteria.isActive = true;
    }
    
    // Get tutorials
    const tutorials = await Tutorial.find(criteria);
    
    // Get categories
    const categories = await TutorialCategory.findAll();
    
    // Add 'all' category
    const allCategories = [
      { id: 'all', name: 'All Tutorials', slug: 'all' },
      ...categories
    ];
    
    return res.status(200).json({
      success: true,
      data: {
        tutorials,
        categories: allCategories
      }
    });
  } catch (error) {
    console.error('Get tutorials error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving tutorials',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/tutorials/:id
 * @desc    Get tutorial by ID
 * @access  Public
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get tutorial
    const tutorial = await Tutorial.findById(id);
    
    if (!tutorial) {
      return res.status(404).json({
        success: false,
        message: 'Tutorial not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: tutorial
    });
  } catch (error) {
    console.error('Get tutorial error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving tutorial',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/tutorials
 * @desc    Create a new tutorial
 * @access  Private (Admin only)
 */
router.post('/', authenticate, isAdmin, async (req, res) => {
  try {
    const {
      title,
      description,
      duration,
      level,
      videoUrl,
      thumbnailUrl,
      categoryId,
      orderNum,
      isActive
    } = req.body;
    
    // Validate required fields
    if (!title || !videoUrl) {
      return res.status(400).json({
        success: false,
        message: 'Title and video URL are required'
      });
    }
    
    // Create tutorial
    const tutorial = await Tutorial.create({
      title,
      description,
      duration,
      level,
      videoUrl,
      thumbnailUrl,
      categoryId,
      orderNum,
      isActive,
      createdBy: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Tutorial created successfully',
      data: tutorial
    });
  } catch (error) {
    console.error('Create tutorial error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while creating tutorial',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   PUT /api/tutorials/:id
 * @desc    Update a tutorial
 * @access  Private (Admin only)
 */
router.put('/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      duration,
      level,
      videoUrl,
      thumbnailUrl,
      categoryId,
      orderNum,
      isActive
    } = req.body;
    
    // Check if tutorial exists
    const tutorial = await Tutorial.findById(id);
    
    if (!tutorial) {
      return res.status(404).json({
        success: false,
        message: 'Tutorial not found'
      });
    }
    
    // Update tutorial
    const updatedTutorial = await Tutorial.update(id, {
      title,
      description,
      duration,
      level,
      videoUrl,
      thumbnailUrl,
      categoryId,
      orderNum,
      isActive
    });
    
    return res.status(200).json({
      success: true,
      message: 'Tutorial updated successfully',
      data: updatedTutorial
    });
  } catch (error) {
    console.error('Update tutorial error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while updating tutorial',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   DELETE /api/tutorials/:id
 * @desc    Delete a tutorial
 * @access  Private (Admin only)
 */
router.delete('/:id', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if tutorial exists
    const tutorial = await Tutorial.findById(id);
    
    if (!tutorial) {
      return res.status(404).json({
        success: false,
        message: 'Tutorial not found'
      });
    }
    
    // Delete tutorial
    await Tutorial.delete(id);
    
    return res.status(200).json({
      success: true,
      message: 'Tutorial deleted successfully'
    });
  } catch (error) {
    console.error('Delete tutorial error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while deleting tutorial',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/tutorials/upload-video
 * @desc    Upload a video for a tutorial
 * @access  Private (Admin only)
 */
router.post('/upload-video', authenticate, isAdmin, upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No video file provided'
      });
    }
    
    // Return the URL to the uploaded file
    const videoUrl = `/uploads/tutorials/${req.file.filename}`;
    
    return res.status(200).json({
      success: true,
      message: 'Video uploaded successfully',
      data: { videoUrl }
    });
  } catch (error) {
    console.error('Upload video error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while uploading video',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/tutorials/upload-thumbnail
 * @desc    Upload a thumbnail image for a tutorial
 * @access  Private (Admin only)
 */
router.post('/upload-thumbnail', authenticate, isAdmin, upload.single('thumbnail'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No thumbnail file provided'
      });
    }
    
    // Return the URL to the uploaded file
    const thumbnailUrl = `/uploads/tutorials/${req.file.filename}`;
    
    return res.status(200).json({
      success: true,
      message: 'Thumbnail uploaded successfully',
      data: { thumbnailUrl }
    });
  } catch (error) {
    console.error('Upload thumbnail error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while uploading thumbnail',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
