import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import {
  CreditCardIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CurrencyBangladeshiIcon
} from '@heroicons/react/24/outline';

const DashboardPage = () => {
  const [stats, setStats] = useState({
    totalPayments: 0,
    pendingPayments: 0,
    approvedPayments: 0,
    rejectedPayments: 0,
    totalRevenue: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const token = localStorage.getItem('adminToken');

        // Log the API URL to debug
        console.log('API URL in DashboardPage:', import.meta.env.VITE_API_URL);

        // Use the environment variable or fallback to port 5001
        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
        // Fetch all payments
        const response = await fetch(`${apiUrl}/api/payments`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch payment data');
        }

        const data = await response.json();
        const payments = data.data;

        // Calculate stats
        const pendingPayments = payments.filter(p => p.verificationStatus === 'pending');
        const approvedPayments = payments.filter(p => p.verificationStatus === 'approved');
        const rejectedPayments = payments.filter(p => p.verificationStatus === 'rejected');

        // Calculate total revenue from approved payments
        // Ensure proper numeric conversion for each payment amount
        const totalRevenue = approvedPayments.reduce((sum, payment) => {
          // Convert payment amount to number to ensure proper addition
          const paymentAmount = typeof payment.amount === 'string'
            ? parseFloat(payment.amount)
            : Number(payment.amount) || 0;

          console.log(`Payment ${payment.orderId}: Amount=${payment.amount}, Type=${typeof payment.amount}, Converted=${paymentAmount}`);
          return sum + paymentAmount;
        }, 0);

        setStats({
          totalPayments: payments.length,
          pendingPayments: pendingPayments.length,
          approvedPayments: approvedPayments.length,
          rejectedPayments: rejectedPayments.length,
          totalRevenue
        });
      } catch (err) {
        console.error('Error fetching dashboard stats:', err);
        setError('Failed to load dashboard data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      name: 'Total Payments',
      value: stats.totalPayments,
      icon: CreditCardIcon,
      color: 'from-primary to-secondary'
    },
    {
      name: 'Pending Verification',
      value: stats.pendingPayments,
      icon: ClockIcon,
      color: 'from-accent to-secondary'
    },
    {
      name: 'Approved Payments',
      value: stats.approvedPayments,
      icon: CheckCircleIcon,
      color: 'from-green-500 to-green-600'
    },
    {
      name: 'Rejected Payments',
      value: stats.rejectedPayments,
      icon: XCircleIcon,
      color: 'from-red-500 to-red-600'
    },
    {
      name: 'Total Revenue',
      value: `৳${stats.totalRevenue.toLocaleString()}`,
      icon: CurrencyBangladeshiIcon,
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <AdminLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold gradient-text">Dashboard</h1>
        <p className="mt-1 text-sm text-dark-300">
          Overview of Meta Master payment statistics
        </p>

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="mt-8 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            {statCards.map((card) => (
              <div key={card.name} className="card">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className={`flex-shrink-0 rounded-lg p-3 bg-gradient-to-r ${card.color}`}>
                      <card.icon className="h-6 w-6 text-white" aria-hidden="true" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-dark-300 truncate">
                          {card.name}
                        </dt>
                        <dd>
                          <div className="text-lg font-medium text-white">
                            {card.value}
                          </div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default DashboardPage;
