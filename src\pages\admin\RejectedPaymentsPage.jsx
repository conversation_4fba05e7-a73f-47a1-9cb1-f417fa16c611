import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import { EyeIcon } from '@heroicons/react/24/outline';

const RejectedPaymentsPage = () => {
  const [payments, setPayments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchRejectedPayments();
  }, []);

  const fetchRejectedPayments = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('adminToken');
      
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payments`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }
      
      const data = await response.json();
      // Filter only rejected payments
      const rejectedPayments = data.data.filter(payment => payment.verificationStatus === 'rejected');
      setPayments(rejectedPayments);
    } catch (err) {
      console.error('Error fetching rejected payments:', err);
      setError('Failed to load rejected payments. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (payment) => {
    setSelectedPayment(payment);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <AdminLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold gradient-text">Rejected Payments</h1>
        <p className="mt-1 text-sm text-dark-300">
          View all rejected payment transactions
        </p>

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="mt-8 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
            </div>
          </div>
        ) : payments.length === 0 ? (
          <div className="mt-8 card p-6 text-center">
            <p className="text-dark-300">No rejected payments found</p>
          </div>
        ) : (
          <div className="mt-8 card">
            <ul className="divide-y divide-dark-700">
              {payments.map((payment) => (
                <li key={payment.orderId}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary truncate">
                          {payment.orderId}
                        </p>
                        <p className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-lg bg-red-500/20 text-red-400 border border-red-500/30">
                          Rejected
                        </p>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <button
                          onClick={() => handleViewDetails(payment)}
                          className="px-3 py-1 border border-dark-600 rounded-lg text-sm font-medium text-dark-300 bg-dark-700 hover:bg-dark-600 hover:text-white transition-all duration-200"
                        >
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          Details
                        </button>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-dark-300">
                          {payment.customerInfo.firstName} {payment.customerInfo.lastName}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-dark-300 sm:mt-0 sm:ml-6">
                          {payment.paymentMethod.toUpperCase()} - ৳{payment.amount}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-dark-300 sm:mt-0 sm:ml-6">
                          {payment.plan}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-dark-300 sm:mt-0">
                        <p>
                          Rejected on {payment.verifiedAt ? formatDate(payment.verifiedAt) : formatDate(payment.updatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Modal for payment details */}
      {isModalOpen && selectedPayment && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Payment Details
                    </h3>
                    
                    <div className="mt-4 border-t border-gray-200 pt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Order ID</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.orderId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Transaction ID</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.transactionId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Payment Method</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.paymentMethod.toUpperCase()}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Amount</p>
                          <p className="mt-1 text-sm text-gray-900">৳{selectedPayment.amount}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Plan</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.plan}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Date</p>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(selectedPayment.createdAt)}</p>
                        </div>
                      </div>
                      
                      <div className="mt-4">
                        <p className="text-sm font-medium text-gray-500">Customer Information</p>
                        <div className="mt-1 grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Name</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.firstName} {selectedPayment.customerInfo.lastName}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.email}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Phone</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.phone}</p>
                          </div>
                          {selectedPayment.customerInfo.address && (
                            <div>
                              <p className="text-sm text-gray-500">Address</p>
                              <p className="text-sm text-gray-900">{selectedPayment.customerInfo.address}</p>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {selectedPayment.adminNotes && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-500">Rejection Reason</p>
                          <p className="mt-1 text-sm text-gray-900 bg-red-50 p-2 rounded border-l-4 border-red-500">{selectedPayment.adminNotes}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeModal}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default RejectedPaymentsPage;
