import React, { useState } from 'react';
import { CloudArrowUpIcon, XMarkIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';

const VideoUploader = ({ 
  onVideoUpload, 
  currentVideoUrl = null,
  label = "Upload Video",
  id = "video-upload",
  className = ""
}) => {
  const [videoFile, setVideoFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [videoRef, setVideoRef] = useState(null);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleVideoChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setVideoFile(file);
      setUploadError(null);
    }
  };

  const handleVideoUpload = async () => {
    if (!videoFile) return;

    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);

    try {
      const token = localStorage.getItem('adminToken');
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';

      const formData = new FormData();
      formData.append('video', videoFile);

      // Create a custom XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.open('POST', `${apiUrl}/api/website-content/upload-video`);
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      
      xhr.onload = function() {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          onVideoUpload(response.data);
          setVideoFile(null);
          setUploadProgress(0);
        } else {
          throw new Error(`Failed to upload video: ${xhr.status} ${xhr.statusText}`);
        }
        setIsUploading(false);
      };
      
      xhr.onerror = function() {
        setUploadError('Network error occurred during upload');
        setIsUploading(false);
      };
      
      xhr.send(formData);
    } catch (error) {
      console.error('Error uploading video:', error);
      setUploadError(`Failed to upload video: ${error.message}`);
      setIsUploading(false);
    }
  };

  const togglePlayPause = () => {
    if (videoRef) {
      if (isPlaying) {
        videoRef.pause();
      } else {
        videoRef.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVideoRef = (ref) => {
    setVideoRef(ref);
    if (ref) {
      ref.addEventListener('ended', () => setIsPlaying(false));
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      
      {currentVideoUrl && (
        <div className="relative rounded-lg overflow-hidden border border-gray-300">
          <video 
            ref={handleVideoRef}
            src={currentVideoUrl} 
            className="w-full h-auto" 
            controls={false}
          />
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
            <button
              type="button"
              onClick={togglePlayPause}
              className="p-3 bg-white rounded-full shadow-lg"
            >
              {isPlaying ? (
                <PauseIcon className="h-6 w-6 text-gray-800" />
              ) : (
                <PlayIcon className="h-6 w-6 text-gray-800" />
              )}
            </button>
          </div>
        </div>
      )}
      
      <div className="mt-1 flex items-center">
        <input
          type="file"
          onChange={handleVideoChange}
          accept="video/*"
          className="sr-only"
          id={id}
        />
        <label
          htmlFor={id}
          className="relative cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
        >
          <span>Select video</span>
        </label>
        
        {videoFile && (
          <div className="ml-3 flex flex-col">
            <div className="flex items-center">
              <span className="text-sm text-gray-500 truncate max-w-xs">{videoFile.name}</span>
              <span className="ml-2 text-xs text-gray-400">({formatFileSize(videoFile.size)})</span>
              <button
                type="button"
                onClick={() => setVideoFile(null)}
                className="ml-2 text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
            
            <div className="flex items-center mt-2">
              <button
                type="button"
                onClick={handleVideoUpload}
                disabled={isUploading}
                className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isUploading ? (
                  <>
                    <CloudArrowUpIcon className="animate-bounce -ml-1 mr-2 h-4 w-4" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <CloudArrowUpIcon className="-ml-1 mr-2 h-4 w-4" />
                    Upload
                  </>
                )}
              </button>
            </div>
            
            {isUploading && (
              <div className="w-full mt-2">
                <div className="bg-gray-200 rounded-full h-2.5">
                  <div 
                    className="bg-indigo-600 h-2.5 rounded-full" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{uploadProgress}% uploaded</p>
              </div>
            )}
          </div>
        )}
      </div>
      
      {uploadError && (
        <p className="mt-2 text-sm text-red-600">{uploadError}</p>
      )}
    </div>
  );
};

export default VideoUploader;
