// Type declarations for imageImports.js

// Declare the module
declare module '../../utils/imageImports' {
  export const logo: string;
  export const screenshot1: string;
  export const screenshot2: string;
  export const screenshot3: string;
  export const screenshot4: string;
  export const screenshot5: string;
  export const screenshot6: string;
  export const mainDemo: string;
  export const metaMasterDemo: string;
  export const demoVideo: string;
  export const heroBg: string;
  export const gridPattern: string;
  export const testimonial1: string;
  export const testimonial2: string;
  export const testimonial3: string;
  export const testimonial4: string;
  export const appPreview: string;
  
  export const images: {
    logo: string;
    screenshot1: string;
    screenshot2: string;
    screenshot3: string;
    screenshot4: string;
    screenshot5: string;
    screenshot6: string;
    mainDemo: string;
    metaMasterDemo: string;
    demoVideo: string;
    heroBg: string;
    gridPattern: string;
    testimonial1: string;
    testimonial2: string;
    testimonial3: string;
    testimonial4: string;
    appPreview: string;
  };
}

// Also declare the module with a relative path
declare module '../utils/imageImports' {
  export const logo: string;
  export const screenshot1: string;
  export const screenshot2: string;
  export const screenshot3: string;
  export const screenshot4: string;
  export const screenshot5: string;
  export const screenshot6: string;
  export const mainDemo: string;
  export const metaMasterDemo: string;
  export const demoVideo: string;
  export const heroBg: string;
  export const gridPattern: string;
  export const testimonial1: string;
  export const testimonial2: string;
  export const testimonial3: string;
  export const testimonial4: string;
  export const appPreview: string;
  
  export const images: {
    logo: string;
    screenshot1: string;
    screenshot2: string;
    screenshot3: string;
    screenshot4: string;
    screenshot5: string;
    screenshot6: string;
    mainDemo: string;
    metaMasterDemo: string;
    demoVideo: string;
    heroBg: string;
    gridPattern: string;
    testimonial1: string;
    testimonial2: string;
    testimonial3: string;
    testimonial4: string;
    appPreview: string;
  };
}
