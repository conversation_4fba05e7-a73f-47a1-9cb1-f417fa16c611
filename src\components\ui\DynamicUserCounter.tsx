import { motion, useInView } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'
import { getExactUserCount, getFormattedUserCount } from '../../utils/userCounter'

interface DynamicUserCounterProps {
  suffix?: string
  duration?: number
  className?: string
  showExact?: boolean // Whether to show exact count or formatted (rounded) count
}

const DynamicUserCounter = ({ 
  suffix = "+", 
  duration = 2, 
  className = "font-bold",
  showExact = false 
}: DynamicUserCounterProps) => {
  const [count, setCount] = useState(0)
  const [targetCount, setTargetCount] = useState(0)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  useEffect(() => {
    // Calculate the current user count
    const currentCount = showExact ? getExactUserCount() : Math.round(getExactUserCount() / 50) * 50
    setTargetCount(currentCount)
  }, [showExact])

  useEffect(() => {
    if (isInView && targetCount > 0) {
      let start = 0
      const end = targetCount

      // Get animation duration based on value size
      const incrementTime = (duration * 1000) / Math.min(end, 100) // Cap at 100 increments for smooth animation

      // Don't run if already at the end value
      if (start === end) {
        setCount(end)
        return
      }

      const timer = setInterval(() => {
        const increment = Math.ceil(end / 100)
        start = Math.min(start + increment, end)
        setCount(start)

        if (start === end) {
          clearInterval(timer)
        }
      }, incrementTime)

      return () => {
        clearInterval(timer)
      }
    }
  }, [isInView, targetCount, duration])

  return (
    <span ref={ref} className={className}>
      {count.toLocaleString()}{suffix}
    </span>
  )
}

export default DynamicUserCounter
