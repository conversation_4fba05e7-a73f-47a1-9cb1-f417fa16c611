import { motion, useInView } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import SectionHeader from '../ui/SectionHeader'
import DynamicUserCounter from '../ui/DynamicUserCounter'
import { getFormattedUserCount } from '../../utils/userCounter'

const benefits = [
  {
    title: 'Lightning-Fast Metadata Generation',
    description: 'Generate professional metadata for hundreds of files in minutes with Google Gemini 2.5 AI. Save 10+ hours per week compared to manual tagging.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    title: 'SEO-Optimized Keywords & Titles',
    description: 'AI-generated metadata is specifically optimized for microstock search algorithms, increasing your content visibility by up to 300%.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    ),
  },
  {
    title: 'Maximize Microstock Revenue',
    description: 'Better metadata equals more downloads and higher earnings. Users report 40-60% increase in sales after using Meta Master.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    title: 'Universal Platform Compatibility',
    description: 'Perfect CSV export for Shutterstock, Adobe Stock, Getty Images, Freepik, Dreamstime, and all major microstock platforms.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
      </svg>
    ),
  },
]

// Animated counter component
interface AnimatedCounterProps {
  value: string | number
  suffix?: string
  duration?: number
}

const AnimatedCounter = ({ value, suffix = "", duration = 2 }: AnimatedCounterProps) => {
  const [count, setCount] = useState(0)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  useEffect(() => {
    if (isInView) {
      let start = 0
      const end = parseInt(value.toString().replace(/[^0-9]/g, ""), 10)

      // Get animation duration based on value size
      const incrementTime = (duration * 1000) / end

      // Don't run if already at the end value
      if (start === end) return

      const timer = setInterval(() => {
        start = Math.min(start + Math.ceil(end / 100), end)
        setCount(start)

        if (start === end) {
          clearInterval(timer)
        }
      }, incrementTime)

      return () => {
        clearInterval(timer)
      }
    }
  }, [isInView, value, duration])

  return (
    <span ref={ref} className="font-bold">
      {count}{suffix}
    </span>
  )
}

// Stats component
const Stats = () => {
  const [userCountText, setUserCountText] = useState('1200+')

  useEffect(() => {
    // Update the user count text when component mounts
    setUserCountText(getFormattedUserCount())
  }, [])

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-16 mb-16 p-8 rounded-xl bg-gradient-to-br from-dark-800/50 to-dark-900/50 border border-dark-700 text-center"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex flex-col items-center justify-center p-6 relative">
        {/* Background glow effect */}
        <motion.div
          className="absolute inset-0 bg-primary/5 rounded-lg -z-10"
          animate={{
            boxShadow: ['0 0 0px rgba(0, 167, 217, 0.2)', '0 0 20px rgba(0, 167, 217, 0.4)', '0 0 0px rgba(0, 167, 217, 0.2)']
          }}
          transition={{ duration: 3, repeat: Infinity }}
        />

        {/* Icon */}
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center text-primary mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </div>

        {/* Counter */}
        <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">
          <DynamicUserCounter
            suffix="+"
            duration={2}
            className="font-bold"
            showExact={false}
          />
        </div>
        <p className="text-xl text-dark-400">Active Contributors</p>
      </div>

      <div className="flex flex-col items-center justify-center p-6 relative">
        {/* Background glow effect */}
        <motion.div
          className="absolute inset-0 bg-secondary/5 rounded-lg -z-10"
          animate={{
            boxShadow: ['0 0 0px rgba(99, 102, 241, 0.2)', '0 0 20px rgba(99, 102, 241, 0.4)', '0 0 0px rgba(99, 102, 241, 0.2)']
          }}
          transition={{ duration: 3, repeat: Infinity, delay: 1 }}
        />

        {/* Icon */}
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-secondary/20 to-accent/20 flex items-center justify-center text-secondary mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        </div>

        {/* Counter */}
        <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent mb-2">
          <AnimatedCounter value="500" suffix="K+" />
        </div>
        <p className="text-xl text-dark-400">Files Processed</p>
      </div>
    </motion.div>
  )
}

export default function Benefits() {
  const navigate = useNavigate()
  const [userCountText, setUserCountText] = useState('1200+')

  useEffect(() => {
    // Update the user count text when component mounts
    setUserCountText(getFormattedUserCount())
  }, [])

  // Function to check authentication before redirecting to download
  const handleGetStartedClick = (e: React.MouseEvent) => {
    e.preventDefault()
    const token = localStorage.getItem('userToken')

    if (token) {
      // User is authenticated, redirect to download page
      navigate('/download')
    } else {
      // User is not authenticated, redirect to login page with return path
      navigate('/user/login', {
        state: {
          returnTo: '/download'
        }
      })
    }
  }
  return (
    <section className="section bg-dark-900/50">
      <div className="container">
        <SectionHeader
          title="Why Meta Master is the Top Choice for Microstock Success"
          subtitle={`Join ${userCountText} contributors who trust Meta Master for professional metadata generation and increased earnings`}
        />

        {/* Stats section */}
        <Stats />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              className="flex gap-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex-shrink-0">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center text-primary">
                  {benefit.icon}
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                <p className="text-dark-400">{benefit.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-16 p-8 rounded-xl bg-gradient-to-br from-dark-800/50 to-dark-900/50 border border-dark-700 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h3 className="text-2xl font-bold mb-4">Join {userCountText} Contributors Earning More with Meta Master</h3>
          <p className="text-dark-400 mb-6 max-w-2xl mx-auto">
            Don't waste hours on manual metadata creation. Start generating professional, SEO-optimized metadata in minutes and watch your microstock earnings grow.
          </p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <a href="/download" onClick={handleGetStartedClick} className="btn btn-primary">
              Get Started Today
            </a>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
