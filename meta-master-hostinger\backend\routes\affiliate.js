const express = require('express');
const router = express.Router();
const User = require('../models/User');
const AffiliateTransaction = require('../models/Affiliate');
const Withdrawal = require('../models/Withdrawal');
const { authenticate, isAdmin } = require('../middleware/auth');

/**
 * Helper function to calculate available balance
 * @param {string} userId - User ID
 * @returns {number} Available balance
 */
async function calculateAvailableBalance(userId) {
  try {
    // Get user
    const user = await User.findById(userId);
    if (!user) return 0;

    // Get total earnings and withdrawn amount
    const totalEarnings = user.affiliateCommission || 0;
    const withdrawnCommission = user.withdrawnCommission || 0;

    // Get pending withdrawal requests
    const pendingWithdrawals = await Withdrawal.find({
      user: userId,
      status: 'pending'
    });

    // Calculate total pending withdrawal amount
    const pendingWithdrawalAmount = pendingWithdrawals.reduce(
      (sum, withdrawal) => sum + withdrawal.amount,
      0
    );

    // Available balance = total earnings - withdrawn - pending withdrawals
    return totalEarnings - withdrawnCommission - pendingWithdrawalAmount;
  } catch (error) {
    console.error('Error calculating available balance:', error);
    return 0;
  }
}

/**
 * @route   GET /api/affiliate/stats
 * @desc    Get affiliate stats for the current user
 * @access  Private
 */
router.get('/stats', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with affiliate info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all affiliate transactions for this user
    const transactions = await AffiliateTransaction.find({ referrer: userId })
      .populate('referredUser', 'firstName lastName email')
      .populate('payment', 'orderId plan amount createdAt')
      .sort({ createdAt: -1 });

    // Calculate stats
    const totalEarnings = user.affiliateCommission || 0;
    const withdrawnCommission = user.withdrawnCommission || 0;
    const availableBalance = await calculateAvailableBalance(userId);

    const totalReferrals = transactions.length;
    const pendingCommissions = transactions
      .filter(t => t.status === 'pending')
      .reduce((sum, t) => sum + t.commissionAmount, 0);

    // Return affiliate stats
    return res.status(200).json({
      success: true,
      data: {
        affiliateId: user.affiliateId,
        totalEarnings,
        withdrawnCommission,
        availableBalance,
        totalReferrals,
        pendingCommissions,
        transactions
      }
    });
  } catch (error) {
    console.error('Get affiliate stats error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving affiliate stats',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate/link
 * @desc    Get affiliate link for the current user
 * @access  Private
 */
router.get('/link', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with affiliate info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Make sure user has an affiliate ID
    if (!user.affiliateId) {
      // Generate a new affiliate ID if not present
      // Remove any special characters and spaces, and convert to lowercase
      const cleanUsername = user.username.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      user.affiliateId = cleanUsername;
      await user.save();
    }

    // Construct the affiliate link
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const affiliateLink = `${baseUrl}/?ref=${user.username}`;

    // Return affiliate link
    return res.status(200).json({
      success: true,
      data: {
        affiliateId: user.affiliateId,
        affiliateLink
      }
    });
  } catch (error) {
    console.error('Get affiliate link error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving affiliate link',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate/admin/leaderboard
 * @desc    Get all affiliates with their stats for admin
 * @access  Private (Admin only)
 */
router.get('/admin/leaderboard', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all users who have affiliate data (either have referred someone or have commission)
    const users = await User.find({
      $or: [
        { affiliateCommission: { $gt: 0 } },
        { withdrawnCommission: { $gt: 0 } }
      ]
    }).select('username firstName lastName email affiliateId affiliateCommission withdrawnCommission createdAt');

    // For each user, get their referral count
    const usersWithReferrals = await Promise.all(users.map(async (user) => {
      // Get all transactions for this user
      const transactions = await AffiliateTransaction.find({ referrer: user._id });

      // Count transactions by status
      const pendingCount = transactions.filter(t => t.status === 'pending').length;
      const approvedCount = transactions.filter(t => t.status === 'approved').length;
      const rejectedCount = transactions.filter(t => t.status === 'rejected').length;
      const paidCount = transactions.filter(t => t.status === 'paid').length;

      // Calculate available balance using helper function
      const availableBalance = await calculateAvailableBalance(user._id);

      return {
        _id: user._id,
        username: user.username,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        affiliateId: user.affiliateId,
        totalReferrals: transactions.length,
        pendingReferrals: pendingCount,
        approvedReferrals: approvedCount,
        rejectedReferrals: rejectedCount,
        paidReferrals: paidCount,
        totalEarnings: user.affiliateCommission || 0,
        withdrawnAmount: user.withdrawnCommission || 0,
        availableBalance: availableBalance,
        joinedAt: user.createdAt
      };
    }));

    // Sort by total earnings (highest first)
    usersWithReferrals.sort((a, b) => b.totalEarnings - a.totalEarnings);

    return res.status(200).json({
      success: true,
      count: usersWithReferrals.length,
      data: usersWithReferrals
    });
  } catch (error) {
    console.error('Get affiliate leaderboard error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving affiliate leaderboard',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/affiliate/admin/transactions/:userId
 * @desc    Get all affiliate transactions for a specific user (admin view)
 * @access  Private (Admin only)
 */
router.get('/admin/transactions/:userId', authenticate, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    // Get user info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all transactions for this user
    const transactions = await AffiliateTransaction.find({ referrer: userId })
      .populate('referredUser', 'firstName lastName email username')
      .populate('payment', 'orderId plan amount createdAt')
      .sort({ createdAt: -1 });

    return res.status(200).json({
      success: true,
      count: transactions.length,
      user: {
        _id: user._id,
        username: user.username,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        affiliateId: user.affiliateId,
        totalEarnings: user.affiliateCommission || 0,
        withdrawnAmount: user.withdrawnCommission || 0,
        availableBalance: await calculateAvailableBalance(user._id)
      },
      data: transactions
    });
  } catch (error) {
    console.error('Get user affiliate transactions error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving user affiliate transactions',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate/admin/transaction/:transactionId/approve
 * @desc    Approve an affiliate transaction
 * @access  Private (Admin only)
 */
router.post('/admin/transaction/:transactionId/approve', authenticate, isAdmin, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { adminNotes } = req.body;

    // Find the transaction
    const transaction = await AffiliateTransaction.findById(transactionId);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Affiliate transaction not found'
      });
    }

    // Check if transaction is already approved or rejected
    if (transaction.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: `Transaction is already ${transaction.status}`
      });
    }

    // Update transaction status
    transaction.status = 'approved';
    transaction.updatedAt = Date.now();
    await transaction.save();

    // Update user's affiliate commission
    const user = await User.findById(transaction.referrer);
    if (user) {
      user.affiliateCommission = (user.affiliateCommission || 0) + transaction.commissionAmount;
      await user.save();
    }

    return res.status(200).json({
      success: true,
      message: 'Affiliate transaction approved successfully',
      data: transaction
    });
  } catch (error) {
    console.error('Approve affiliate transaction error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while approving affiliate transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate/admin/transaction/:transactionId/reject
 * @desc    Reject an affiliate transaction
 * @access  Private (Admin only)
 */
router.post('/admin/transaction/:transactionId/reject', authenticate, isAdmin, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { adminNotes } = req.body;

    // Find the transaction
    const transaction = await AffiliateTransaction.findById(transactionId);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Affiliate transaction not found'
      });
    }

    // Check if transaction is already approved or rejected
    if (transaction.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: `Transaction is already ${transaction.status}`
      });
    }

    // Update transaction status
    transaction.status = 'rejected';
    transaction.updatedAt = Date.now();
    await transaction.save();

    return res.status(200).json({
      success: true,
      message: 'Affiliate transaction rejected successfully',
      data: transaction
    });
  } catch (error) {
    console.error('Reject affiliate transaction error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while rejecting affiliate transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/affiliate/fix-balance
 * @desc    Fix user's affiliate balance
 * @access  Private
 */
router.post('/fix-balance', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all approved affiliate transactions
    const transactions = await AffiliateTransaction.find({
      referrer: userId,
      status: 'approved'
    });

    // Calculate total earnings from transactions
    const totalEarnings = transactions.reduce(
      (sum, transaction) => sum + transaction.commissionAmount,
      0
    );

    // Get all paid withdrawals
    const paidWithdrawals = await Withdrawal.find({
      user: userId,
      status: 'paid'
    });

    // Calculate total withdrawn amount
    const totalWithdrawn = paidWithdrawals.reduce(
      (sum, withdrawal) => sum + withdrawal.amount,
      0
    );

    // Update user's balance
    user.affiliateCommission = totalEarnings;
    user.withdrawnCommission = totalWithdrawn;
    await user.save();

    // Calculate available balance
    const availableBalance = await calculateAvailableBalance(userId);

    return res.status(200).json({
      success: true,
      message: 'Affiliate balance fixed successfully',
      data: {
        totalEarnings,
        totalWithdrawn,
        availableBalance
      }
    });
  } catch (error) {
    console.error('Fix affiliate balance error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while fixing affiliate balance',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
