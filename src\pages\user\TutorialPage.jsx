import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  AcademicCapIcon,
  BookOpenIcon,
  PlayCircleIcon,
  DocumentTextIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function TutorialPage() {
  const [activeCategory, setActiveCategory] = useState('all')
  const [selectedTutorial, setSelectedTutorial] = useState(null)
  const [completedTutorials, setCompletedTutorials] = useState([])
  const [tutorials, setTutorials] = useState([])
  const [categories, setCategories] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  // Fetch tutorials from the API
  useEffect(() => {
    const fetchTutorials = async () => {
      try {
        setIsLoading(true);
        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
        const response = await fetch(`${apiUrl}/api/tutorials?category=${activeCategory}`);

        if (!response.ok) {
          throw new Error('Failed to fetch tutorials');
        }

        const data = await response.json();

        if (data.success && data.data) {
          setTutorials(data.data.tutorials);
          setCategories(data.data.categories);
        }
      } catch (error) {
        console.error('Error fetching tutorials:', error);
        // If API fails, use default tutorials
        setTutorials([
          {
            id: 1,
            title: 'Getting Started with Meta Master',
            description: 'Learn the basics of Meta Master and how to set up your first project.',
            duration: '5 min',
            level: 'Beginner',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            thumbnailUrl: '/images/tutorials/default.jpg',
            category: 'basics'
          },
          {
            id: 2,
            title: 'Understanding Metadata',
            description: 'Learn about metadata and why it\'s important for your images.',
            duration: '8 min',
            level: 'Beginner',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            thumbnailUrl: '/images/tutorials/default.jpg',
            category: 'basics'
          }
        ]);

        // Set default categories if API fails
        setCategories([
          { id: 'all', name: 'All Tutorials', slug: 'all' },
          { id: 'basics', name: 'Basics', slug: 'basics' },
          { id: 'advanced', name: 'Advanced Features', slug: 'advanced' },
          { id: 'export', name: 'Export Options', slug: 'export' }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTutorials();
  }, [activeCategory]);

  // Filter tutorials based on active category
  const filteredTutorials = tutorials;

  const openTutorial = (tutorial) => {
    setSelectedTutorial(tutorial)
    window.scrollTo(0, 0)
  }

  const closeTutorial = () => {
    setSelectedTutorial(null)
  }

  const markAsCompleted = (tutorialId) => {
    if (!completedTutorials.includes(tutorialId)) {
      setCompletedTutorials([...completedTutorials, tutorialId])
    }
  }

  const getLevelBadgeColor = (level) => {
    switch (level) {
      case 'Beginner':
        return 'bg-green-900/20 text-green-400'
      case 'Intermediate':
        return 'bg-blue-900/20 text-blue-400'
      case 'Advanced':
        return 'bg-purple-900/20 text-purple-400'
      default:
        return 'bg-gray-900/20 text-gray-400'
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-light">Tutorials</h1>
            <p className="text-text">
              Learn how to use Meta Master with our step-by-step tutorials
            </p>
          </div>
        </div>

        {selectedTutorial ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-semibold text-light">{selectedTutorial.title}</h2>
                <button
                  onClick={closeTutorial}
                  className="text-text hover:text-light transition-colors"
                >
                  Back to Tutorials
                </button>
              </div>

              <div className="aspect-video w-full bg-dark rounded-lg overflow-hidden mb-6">
                {selectedTutorial.videoUrl ? (
                  <iframe
                    className="w-full h-full"
                    src={selectedTutorial.videoUrl}
                    title={selectedTutorial.title}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-dark">
                    <p className="text-text">Video not available</p>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className="px-3 py-1 rounded-full text-xs font-medium bg-dark-light text-text">
                  {selectedTutorial.duration}
                </span>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getLevelBadgeColor(selectedTutorial.level)}`}>
                  {selectedTutorial.level}
                </span>
              </div>

              <p className="text-text mb-6">{selectedTutorial.description}</p>

              <div className="border-t border-border pt-6">
                <h3 className="text-lg font-semibold text-light mb-4">Tutorial Steps</h3>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mt-0.5">
                      <span className="text-primary text-xs font-medium">1</span>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-light font-medium">Introduction to {selectedTutorial.title}</h4>
                      <p className="text-text text-sm mt-1">Overview of what you'll learn in this tutorial.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mt-0.5">
                      <span className="text-primary text-xs font-medium">2</span>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-light font-medium">Setting Up</h4>
                      <p className="text-text text-sm mt-1">Preparing your environment and files.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mt-0.5">
                      <span className="text-primary text-xs font-medium">3</span>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-light font-medium">Step-by-Step Guide</h4>
                      <p className="text-text text-sm mt-1">Detailed instructions for completing the task.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mt-0.5">
                      <span className="text-primary text-xs font-medium">4</span>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-light font-medium">Tips and Best Practices</h4>
                      <p className="text-text text-sm mt-1">Advanced tips to improve your workflow.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center mt-8">
                <div className="flex items-center">
                  <DocumentTextIcon className="w-5 h-5 text-primary mr-2" />
                  <a href="#" className="text-primary hover:underline">Download PDF Guide</a>
                </div>

                <button
                  onClick={() => markAsCompleted(selectedTutorial.id)}
                  className={`flex items-center px-4 py-2 rounded-lg ${
                    completedTutorials.includes(selectedTutorial.id)
                      ? 'bg-green-900/20 text-green-400'
                      : 'bg-primary text-white hover:bg-primary/90'
                  } transition-colors`}
                >
                  {completedTutorials.includes(selectedTutorial.id) ? (
                    <>
                      <CheckCircleIcon className="w-5 h-5 mr-2" />
                      Completed
                    </>
                  ) : (
                    'Mark as Completed'
                  )}
                </button>
              </div>
            </div>

            <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-light mb-4">Related Tutorials</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {tutorials
                  .filter(tutorial => tutorial.id !== selectedTutorial.id && tutorial.category === selectedTutorial.category)
                  .slice(0, 2)
                  .map(tutorial => (
                    <div
                      key={tutorial.id}
                      className="bg-dark border border-border rounded-lg overflow-hidden cursor-pointer hover:border-primary/50 transition-colors"
                      onClick={() => openTutorial(tutorial)}
                    >
                      <div className="aspect-video bg-dark-light/50 relative">
                        {tutorial.thumbnailUrl && (
                          <img
                            src={tutorial.thumbnailUrl}
                            alt={tutorial.title}
                            className="w-full h-full object-cover"
                          />
                        )}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <PlayCircleIcon className="w-12 h-12 text-primary/70" />
                        </div>
                      </div>
                      <div className="p-4">
                        <h4 className="text-light font-medium">{tutorial.title}</h4>
                        <div className="flex items-center text-text text-sm mt-2">
                          <span>{tutorial.duration || '10 min'}</span>
                          <span className="mx-2">•</span>
                          <span>{tutorial.level || 'All Levels'}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                {tutorials.filter(tutorial => tutorial.id !== selectedTutorial.id && tutorial.category === selectedTutorial.category).length === 0 && (
                  <div className="col-span-2 py-4 text-center">
                    <p className="text-text">No related tutorials found.</p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        ) : (
          <>
            {/* Category Tabs */}
            <div className="mb-6 border-b border-border">
              <div className="flex overflow-x-auto hide-scrollbar space-x-4">
                {isLoading ? (
                  // Loading skeleton for categories
                  Array.from({ length: 4 }).map((_, index) => (
                    <div
                      key={index}
                      className="px-4 py-2 whitespace-nowrap"
                    >
                      <div className="h-6 w-24 bg-dark-light/70 rounded animate-pulse"></div>
                    </div>
                  ))
                ) : (
                  categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.slug)}
                      className={`px-4 py-2 whitespace-nowrap ${
                        activeCategory === category.slug
                          ? 'text-primary border-b-2 border-primary'
                          : 'text-text hover:text-light'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))
                )}
              </div>
            </div>

            {/* Tutorials Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {isLoading ? (
                // Loading skeleton
                Array.from({ length: 6 }).map((_, index) => (
                  <div
                    key={index}
                    className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl overflow-hidden animate-pulse"
                  >
                    <div className="aspect-video bg-dark-light/50"></div>
                    <div className="p-5">
                      <div className="h-6 bg-dark-light/70 rounded mb-2"></div>
                      <div className="h-4 bg-dark-light/70 rounded mb-1 w-3/4"></div>
                      <div className="h-4 bg-dark-light/70 rounded mb-4 w-1/2"></div>
                      <div className="flex gap-2 mb-4">
                        <div className="h-6 w-16 bg-dark-light/70 rounded-full"></div>
                        <div className="h-6 w-24 bg-dark-light/70 rounded-full"></div>
                      </div>
                      <div className="h-4 w-32 bg-dark-light/70 rounded"></div>
                    </div>
                  </div>
                ))
              ) : filteredTutorials.length > 0 ? (
                filteredTutorials.map(tutorial => (
                  <motion.div
                    key={tutorial.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className={`bg-dark-light/30 backdrop-blur-sm border ${
                      completedTutorials.includes(tutorial.id) ? 'border-green-500/30' : 'border-white/5'
                    } rounded-xl overflow-hidden cursor-pointer hover:border-primary/50 transition-colors`}
                    onClick={() => openTutorial(tutorial)}
                  >
                    <div className="aspect-video bg-dark-light/50 relative">
                      {completedTutorials.includes(tutorial.id) && (
                        <div className="absolute top-2 right-2 z-10">
                          <span className="bg-green-900/70 text-green-400 text-xs px-2 py-1 rounded-full flex items-center">
                            <CheckCircleIcon className="w-3 h-3 mr-1" />
                            Completed
                          </span>
                        </div>
                      )}
                      {tutorial.thumbnailUrl && (
                        <img
                          src={tutorial.thumbnailUrl}
                          alt={tutorial.title}
                          className="w-full h-full object-cover"
                        />
                      )}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <PlayCircleIcon className="w-16 h-16 text-primary/70" />
                      </div>
                    </div>

                    <div className="p-5">
                      <h3 className="text-lg font-semibold text-light mb-2">{tutorial.title}</h3>
                      <p className="text-text text-sm mb-4">{tutorial.description}</p>

                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className="px-3 py-1 rounded-full text-xs font-medium bg-dark-light text-text">
                          {tutorial.duration || '10 min'}
                        </span>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getLevelBadgeColor(tutorial.level || 'All Levels')}`}>
                          {tutorial.level || 'All Levels'}
                        </span>
                      </div>

                      <button className="flex items-center text-primary hover:underline">
                        Watch Tutorial
                        <ArrowRightIcon className="w-4 h-4 ml-1" />
                      </button>
                    </div>
                  </motion.div>
                ))
              ) : (
                <div className="col-span-3 py-10 text-center">
                  <p className="text-text">No tutorials found in this category.</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </UserLayout>
  )
}
