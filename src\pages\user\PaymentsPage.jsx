import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  CreditCardIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function PaymentsPage() {
  const [payments, setPayments] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedPayment, setSelectedPayment] = useState(null)
  const [showDetails, setShowDetails] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    const fetchPayments = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }
        
        // Fetch payment history
        const paymentsResponse = await fetch('http://localhost:5001/api/users/payments', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        
        if (!paymentsResponse.ok) {
          throw new Error('Failed to fetch payment data')
        }
        
        const paymentsData = await paymentsResponse.json()
        console.log('Payment data:', paymentsData) // Debug log
        setPayments(paymentsData.payments || [])
      } catch (error) {
        console.error('Error fetching payment data:', error)
        setError('Failed to load payment data. Please try again.')
        
        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPayments()
  }, [navigate])
  
  const getStatusBadge = (status) => {
    switch (status) {
      case 'verified':
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Approved
          </span>
        )
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="w-3 h-3 mr-1" />
            Pending
          </span>
        )
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="w-3 h-3 mr-1" />
            Rejected
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        )
    }
  }
  
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
  
  const openPaymentDetails = (payment) => {
    setSelectedPayment(payment)
    setShowDetails(true)
  }
  
  const closePaymentDetails = () => {
    setShowDetails(false)
    setTimeout(() => setSelectedPayment(null), 300)
  }
  
  const getPaymentMethodIcon = (method) => {
    switch (method?.toLowerCase()) {
      case 'bkash':
        return '💳 bKash'
      case 'nagad':
        return '💳 Nagad'
      case 'rocket':
        return '💳 Rocket'
      case 'upay':
        return '💳 Upay'
      default:
        return '💳 Payment'
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-light">Payment History</h1>
            <p className="text-text">
              View and manage your payment transactions
            </p>
          </div>
          
          <button
            onClick={() => navigate('/pricing')}
            className="px-4 py-2 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
          >
            Make New Payment
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4">
            {error}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Payment History Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-6 flex items-center">
                <CreditCardIcon className="w-5 h-5 mr-2 text-primary" />
                Recent Transactions
              </h2>
              
              {payments && payments.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-border">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Order ID
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Plan
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {payments.map((payment) => (
                        <tr key={payment._id || payment.orderId} className="hover:bg-dark-light/30">
                          <td className="px-4 py-3 text-sm text-light">
                            {payment.orderId}
                          </td>
                          <td className="px-4 py-3 text-sm text-text">
                            {formatDate(payment.createdAt)}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            {payment.plan}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            ৳{payment.amount}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {getStatusBadge(payment.status)}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            <button
                              onClick={() => openPaymentDetails(payment)}
                              className="text-primary hover:underline"
                            >
                              View Details
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                    <CreditCardIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-light mb-2">No Payment History</h3>
                    <p className="text-text mb-6">Your payment history will appear here after you make a purchase</p>
                    <button
                      onClick={() => navigate('/pricing')}
                      className="px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
                    >
                      View Pricing Plans
                    </button>
                  </div>
                </div>
              )}
            </motion.div>
            
            {/* Payment Information Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-4 flex items-center">
                <QuestionMarkCircleIcon className="w-5 h-5 mr-2 text-primary" />
                Payment Information
              </h2>
              
              <div className="space-y-4 text-text">
                <p>
                  Meta Master accepts payments through the following methods:
                </p>
                <ul className="list-disc pl-5 space-y-2">
                  <li><span className="text-light">bKash:</span> Fast and secure mobile banking service</li>
                  <li><span className="text-light">Nagad:</span> Digital financial service</li>
                  <li><span className="text-light">Rocket:</span> Mobile banking service by Dutch-Bangla Bank</li>
                  <li><span className="text-light">Upay:</span> Mobile financial service</li>
                </ul>
                <p>
                  After making a payment, our team will verify your transaction and activate your license key within 24 hours.
                  For any payment-related issues, please <a href="/contact" className="text-primary hover:underline">contact our support team</a>.
                </p>
              </div>
            </motion.div>
          </div>
        )}
        
        {/* Payment Details Modal */}
        {showDetails && selectedPayment && (
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-dark/80">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              className="bg-dark-light border border-white/10 rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-semibold text-light">Payment Details</h2>
                <button
                  onClick={closePaymentDetails}
                  className="text-text hover:text-light"
                >
                  <XCircleIcon className="w-6 h-6" />
                </button>
              </div>
              
              <div className="space-y-6">
                <div className="flex justify-between items-center p-4 bg-dark rounded-lg">
                  <div>
                    <div className="text-sm text-text">Order ID</div>
                    <div className="text-light font-medium">{selectedPayment.orderId}</div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    selectedPayment.status === 'verified' || selectedPayment.status === 'approved' 
                      ? 'bg-green-900/20 text-green-400' 
                      : selectedPayment.status === 'pending'
                      ? 'bg-yellow-900/20 text-yellow-400'
                      : 'bg-red-900/20 text-red-400'
                  }`}>
                    {selectedPayment.status.charAt(0).toUpperCase() + selectedPayment.status.slice(1)}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-dark rounded-lg">
                    <h3 className="text-sm text-text mb-1">Transaction Details</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-text">Plan:</span>
                        <span className="text-light">{selectedPayment.plan}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text">Amount:</span>
                        <span className="text-light">৳{selectedPayment.amount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text">Payment Method:</span>
                        <span className="text-light">{getPaymentMethodIcon(selectedPayment.paymentMethod)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text">Transaction ID:</span>
                        <span className="text-light">{selectedPayment.transactionId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text">Date:</span>
                        <span className="text-light">{formatDate(selectedPayment.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-dark rounded-lg">
                    <h3 className="text-sm text-text mb-1">Customer Information</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-text">Name:</span>
                        <span className="text-light">
                          {selectedPayment.customerInfo?.firstName} {selectedPayment.customerInfo?.lastName}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text">Email:</span>
                        <span className="text-light">{selectedPayment.customerInfo?.email}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text">Phone:</span>
                        <span className="text-light">{selectedPayment.customerInfo?.phone}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {selectedPayment.licenseKey && (
                  <div className="p-4 bg-dark rounded-lg">
                    <h3 className="text-sm text-text mb-2">License Key</h3>
                    <code className="block bg-dark-light/50 p-3 rounded text-sm font-mono text-light overflow-x-auto">
                      {selectedPayment.licenseKey}
                    </code>
                  </div>
                )}
                
                <div className="flex justify-between pt-4 border-t border-border">
                  <button
                    onClick={closePaymentDetails}
                    className="px-4 py-2 border border-border text-text rounded-lg hover:bg-dark-light transition-colors"
                  >
                    Close
                  </button>
                  
                  <div className="flex space-x-2">
                    <button
                      className="px-4 py-2 flex items-center text-text border border-border rounded-lg hover:bg-dark-light transition-colors"
                    >
                      <DocumentTextIcon className="w-4 h-4 mr-1" />
                      Receipt
                    </button>
                    
                    <button
                      className="px-4 py-2 flex items-center text-white bg-primary rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
                      Download Invoice
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </UserLayout>
  )
}
