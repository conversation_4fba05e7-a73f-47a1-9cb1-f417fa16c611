const { query, testConnection } = require('../config/db');

async function addCustomerColumns() {
  try {
    // First test the connection
    const connected = await testConnection();
    if (!connected) {
      console.error('Failed to connect to the database');
      process.exit(1);
    }

    console.log('Connected to the database');

    // Check if columns already exist
    const [columns] = await query('SHOW COLUMNS FROM payments');
    const columnNames = columns.map(col => col.Field);
    
    console.log('Existing columns:', columnNames);

    // Add columns if they don't exist
    if (!columnNames.includes('customer_first_name')) {
      console.log('Adding customer_first_name column...');
      await query('ALTER TABLE payments ADD COLUMN customer_first_name VARCHAR(100) DEFAULT NULL');
      console.log('Added customer_first_name column');
    }

    if (!columnNames.includes('customer_last_name')) {
      console.log('Adding customer_last_name column...');
      await query('ALTER TABLE payments ADD COLUMN customer_last_name VARCHAR(100) DEFAULT NULL');
      console.log('Added customer_last_name column');
    }

    if (!columnNames.includes('customer_email')) {
      console.log('Adding customer_email column...');
      await query('ALTER TABLE payments ADD COLUMN customer_email VARCHAR(100) DEFAULT NULL');
      console.log('Added customer_email column');
    }

    if (!columnNames.includes('customer_phone')) {
      console.log('Adding customer_phone column...');
      await query('ALTER TABLE payments ADD COLUMN customer_phone VARCHAR(20) DEFAULT NULL');
      console.log('Added customer_phone column');
    }

    console.log('Successfully added all customer information columns to payments table');
    process.exit(0);
  } catch (error) {
    console.error('Error adding columns:', error);
    process.exit(1);
  }
}

// Run the function
addCustomerColumns();
