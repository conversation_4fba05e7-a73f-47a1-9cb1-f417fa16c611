const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const initDatabase = async () => {
  try {
    // Create connection without database selection
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      multipleStatements: true // Allow multiple SQL statements
    });

    console.log('Connected to MySQL server');

    // Create database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'meta_master'}`);
    console.log(`Database '${process.env.DB_NAME || 'meta_master'}' created or already exists`);

    // Use the database
    await connection.query(`USE ${process.env.DB_NAME || 'meta_master'}`);
    console.log(`Using database '${process.env.DB_NAME || 'meta_master'}'`);

    // Read schema file
    const schemaPath = path.join(__dirname, '../config/schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Execute schema SQL
    await connection.query(schema);
    console.log('Database schema created successfully');

    // Create default admin if none exists
    const [adminRows] = await connection.query('SELECT COUNT(*) as count FROM admins');
    if (adminRows[0].count === 0) {
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);

      await connection.query(
        'INSERT INTO admins (username, password, role) VALUES (?, ?, ?)',
        ['admin', hashedPassword, 'superadmin']
      );
      console.log('Default admin user created with username: admin, password: admin123');
      console.log('IMPORTANT: Change this password immediately after first login!');
    }

    // Close connection
    await connection.end();
    console.log('Database initialization completed successfully');

  } catch (error) {
    console.error('Database initialization error:', error);
    process.exit(1);
  }
};

// Run the initialization
initDatabase();
