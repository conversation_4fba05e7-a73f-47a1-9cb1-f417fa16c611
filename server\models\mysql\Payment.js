const { query } = require('../../config/db');

class Payment {
  // Find payment by ID
  static async findById(id) {
    try {
      const [rows] = await query(
        'SELECT * FROM payments WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      return this.formatPayment(rows[0]);
    } catch (error) {
      console.error('Error finding payment by ID:', error);
      throw error;
    }
  }

  // Find payment by order ID
  static async findOne(criteria) {
    try {
      let sql = 'SELECT * FROM payments WHERE ';
      const params = [];

      if (criteria.orderId) {
        sql += 'order_id = ?';
        params.push(criteria.orderId);
      } else if (criteria.transactionId) {
        sql += 'transaction_id = ?';
        params.push(criteria.transactionId);
      } else {
        throw new Error('Invalid search criteria');
      }

      const [rows] = await query(sql, params);

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      return this.formatPayment(rows[0]);
    } catch (error) {
      console.error('Error finding payment:', error);
      throw error;
    }
  }

  // Find payments by criteria
  static async find(criteria = {}, sort = {}) {
    try {
      let sql = 'SELECT * FROM payments';
      const params = [];

      // Add WHERE clause if criteria provided
      if (Object.keys(criteria).length > 0) {
        const conditions = [];

        Object.entries(criteria).forEach(([key, value]) => {
          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          conditions.push(`${snakeKey} = ?`);
          params.push(value);
        });

        sql += ` WHERE ${conditions.join(' AND ')}`;
      }

      // Add ORDER BY clause if sort provided
      if (Object.keys(sort).length > 0) {
        const sortFields = [];

        Object.entries(sort).forEach(([key, value]) => {
          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          sortFields.push(`${snakeKey} ${value === 1 ? 'ASC' : 'DESC'}`);
        });

        sql += ` ORDER BY ${sortFields.join(', ')}`;
      } else {
        // Default sort by created_at DESC
        sql += ' ORDER BY created_at DESC';
      }

      console.log('Executing SQL query:', sql, params);
      const [rows] = await query(sql, params);
      console.log('Query returned', rows.length, 'rows');

      // Convert snake_case to camelCase for all results
      return rows.map(row => this.formatPayment(row));
    } catch (error) {
      console.error('Error finding payments:', error);
      throw error;
    }
  }

  // Create a new payment
  static async create(paymentData) {
    try {
      const {
        orderId,
        transactionId,
        paymentMethod,
        amount,
        plan,
        userId,
        referredBy,
        status = 'pending',
        customerInfo = {}
      } = paymentData;

      // Extract customer information
      // Handle both name formats (single name field or firstName+lastName)
      let customerName;
      if (customerInfo.name) {
        customerName = customerInfo.name;
      } else if (customerInfo.firstName || customerInfo.lastName) {
        customerName = `${customerInfo.firstName || ''} ${customerInfo.lastName || ''}`.trim();
      } else {
        customerName = null;
      }

      const customerEmail = customerInfo.email || null;
      const customerPhone = customerInfo.phone || null;

      // Try to insert with customer information
      try {
        const [result] = await query(
          'INSERT INTO payments (order_id, transaction_id, payment_method, amount, plan, user_id, referred_by, status, ' +
          'customer_name, customer_email, customer_phone) ' +
          'VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [
            orderId,
            transactionId,
            paymentMethod,
            amount,
            plan,
            userId,
            referredBy,
            status,
            customerName,
            customerEmail,
            customerPhone
          ]
        );
        return await this.findById(result.insertId);
      } catch (dbError) {
        // If the customer columns don't exist, fall back to the basic insert
        console.log('Falling back to basic payment insert without customer info:', dbError.message);
        const [result] = await query(
          'INSERT INTO payments (order_id, transaction_id, payment_method, amount, plan, user_id, referred_by, status) ' +
          'VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          [
            orderId,
            transactionId,
            paymentMethod,
            amount,
            plan,
            userId,
            referredBy,
            status
          ]
        );
        return await this.findById(result.insertId);
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  }

  // Update payment
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      // If there are no fields to update, return the existing payment
      if (fields.length === 0) {
        return await this.findById(id);
      }

      await query(
        `UPDATE payments SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating payment:', error);
      throw error;
    }
  }

  // Helper method to format payment data
  static formatPayment(payment) {
    // Create a formatted payment object with basic fields
    const formattedPayment = {
      id: payment.id,
      _id: payment.id, // Add _id for MongoDB compatibility
      orderId: payment.order_id,
      transactionId: payment.transaction_id,
      paymentMethod: payment.payment_method,
      // Ensure amount is always a number
      amount: typeof payment.amount === 'string' ? parseFloat(payment.amount) : Number(payment.amount) || 0,
      plan: payment.plan,
      userId: payment.user_id,
      user: payment.user_id, // Add user for MongoDB compatibility
      referredBy: payment.referred_by,
      isAffiliateCommissionPaid: payment.is_affiliate_commission_paid === 1,
      status: payment.status,
      // Add verificationStatus for frontend compatibility (maps to status in MySQL)
      verificationStatus: payment.status,
      licenseKey: payment.license_key,
      licenseKeySent: payment.license_key_sent === 1,
      adminNotes: payment.admin_notes,
      verifiedBy: payment.approved_by, // Map approved_by to verifiedBy
      verifiedAt: payment.approved_at, // Map approved_at to verifiedAt
      approvedBy: payment.approved_by,
      approvedAt: payment.approved_at,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    };

    // Add customerInfo for frontend compatibility
    // Check if customer information fields exist in the database record
    if (payment.hasOwnProperty('customer_name') ||
        payment.hasOwnProperty('customer_email') ||
        payment.hasOwnProperty('customer_phone')) {
      formattedPayment.customerInfo = {
        name: payment.customer_name || 'Customer Name',
        // Split name into firstName and lastName for backward compatibility
        firstName: payment.customer_name ? payment.customer_name.split(' ')[0] : 'Customer',
        lastName: payment.customer_name ? payment.customer_name.split(' ').slice(1).join(' ') : 'Name',
        email: payment.customer_email || '<EMAIL>',
        phone: payment.customer_phone || '123456789'
      };
    } else {
      // Provide default customer info if fields don't exist
      formattedPayment.customerInfo = {
        firstName: 'Customer',
        lastName: 'Name',
        name: 'Customer Name',
        email: '<EMAIL>',
        phone: '123456789'
      };
    }

    return formattedPayment;
  }
}

module.exports = Payment;
