const { query } = require('../../config/db');

class AffiliateApplication {
  constructor(data) {
    this.id = data.id;
    this.userId = data.user_id;
    this.status = data.status;
    this.commissionRate = data.commission_rate;
    this.applicationReason = data.application_reason;
    this.adminNotes = data.admin_notes;
    this.appliedAt = data.applied_at;
    this.reviewedAt = data.reviewed_at;
    this.reviewedBy = data.reviewed_by;
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
  }

  // Get all affiliate applications with user details
  static async findAll(options = {}) {
    try {
      let sqlQuery = `
        SELECT 
          aa.*,
          u.username,
          u.name as user_name,
          u.email,
          admin.username as reviewed_by_username,
          admin.name as reviewed_by_name
        FROM affiliate_applications aa
        LEFT JOIN users u ON aa.user_id = u.id
        LEFT JOIN users admin ON aa.reviewed_by = admin.id
      `;

      const conditions = [];
      const params = [];

      if (options.status) {
        conditions.push('aa.status = ?');
        params.push(options.status);
      }

      if (options.userId) {
        conditions.push('aa.user_id = ?');
        params.push(options.userId);
      }

      if (conditions.length > 0) {
        sqlQuery += ' WHERE ' + conditions.join(' AND ');
      }

      sqlQuery += ' ORDER BY aa.applied_at DESC';

      if (options.limit) {
        sqlQuery += ' LIMIT ?';
        params.push(options.limit);
      }

      const [rows] = await query(sqlQuery, params);
      return rows.map(row => new AffiliateApplication(row));
    } catch (error) {
      console.error('Error fetching affiliate applications:', error);
      throw error;
    }
  }

  // Find application by ID
  static async findById(id) {
    try {
      const [rows] = await query(`
        SELECT 
          aa.*,
          u.username,
          u.name as user_name,
          u.email,
          admin.username as reviewed_by_username,
          admin.name as reviewed_by_name
        FROM affiliate_applications aa
        LEFT JOIN users u ON aa.user_id = u.id
        LEFT JOIN users admin ON aa.reviewed_by = admin.id
        WHERE aa.id = ?
      `, [id]);

      return rows.length > 0 ? new AffiliateApplication(rows[0]) : null;
    } catch (error) {
      console.error('Error finding affiliate application by ID:', error);
      throw error;
    }
  }

  // Find application by user ID
  static async findByUserId(userId) {
    try {
      const [rows] = await query(`
        SELECT 
          aa.*,
          u.username,
          u.name as user_name,
          u.email,
          admin.username as reviewed_by_username,
          admin.name as reviewed_by_name
        FROM affiliate_applications aa
        LEFT JOIN users u ON aa.user_id = u.id
        LEFT JOIN users admin ON aa.reviewed_by = admin.id
        WHERE aa.user_id = ?
        ORDER BY aa.applied_at DESC
        LIMIT 1
      `, [userId]);

      return rows.length > 0 ? new AffiliateApplication(rows[0]) : null;
    } catch (error) {
      console.error('Error finding affiliate application by user ID:', error);
      throw error;
    }
  }

  // Create a new affiliate application
  static async create(applicationData) {
    try {
      const {
        userId,
        applicationReason = ''
      } = applicationData;

      // Check if user already has an application
      const existingApplication = await this.findByUserId(userId);
      if (existingApplication) {
        throw new Error('User already has an affiliate application');
      }

      const [result] = await query(
        'INSERT INTO affiliate_applications (user_id, status, application_reason, applied_at) VALUES (?, ?, ?, NOW())',
        [userId, 'pending', applicationReason]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating affiliate application:', error);
      throw error;
    }
  }

  // Update application status (approve/reject)
  static async updateStatus(id, statusData) {
    try {
      const {
        status,
        commissionRate = 0.2,
        adminNotes = '',
        reviewedBy
      } = statusData;

      if (!['approved', 'rejected'].includes(status)) {
        throw new Error('Invalid status. Must be approved or rejected');
      }

      await query(
        'UPDATE affiliate_applications SET status = ?, commission_rate = ?, admin_notes = ?, reviewed_by = ?, reviewed_at = NOW(), updated_at = NOW() WHERE id = ?',
        [status, commissionRate, adminNotes, reviewedBy, id]
      );

      // If approved, update user's affiliate status
      if (status === 'approved') {
        const application = await this.findById(id);
        if (application) {
          await query(
            'UPDATE users SET affiliate_status = ?, affiliate_commission_rate = ?, affiliate_approved_at = NOW() WHERE id = ?',
            ['approved', commissionRate, application.userId]
          );
        }
      }

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating affiliate application status:', error);
      throw error;
    }
  }

  // Get application statistics
  static async getStats() {
    try {
      const [stats] = await query(`
        SELECT 
          COUNT(*) as total_applications,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_applications,
          SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_applications,
          SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_applications
        FROM affiliate_applications
      `);

      return stats[0] || {
        total_applications: 0,
        pending_applications: 0,
        approved_applications: 0,
        rejected_applications: 0
      };
    } catch (error) {
      console.error('Error getting affiliate application stats:', error);
      throw error;
    }
  }

  // Delete application (if needed)
  static async delete(id) {
    try {
      const [result] = await query('DELETE FROM affiliate_applications WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting affiliate application:', error);
      throw error;
    }
  }
}

module.exports = AffiliateApplication;
