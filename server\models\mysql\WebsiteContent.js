const { pool } = require('../../config/db');

class WebsiteContent {
  // Find content by ID
  static async findById(id) {
    try {
      // Get the main content record
      const [contentRows] = await pool.query(
        'SELECT * FROM website_content WHERE id = ?',
        [id]
      );
      
      if (contentRows.length === 0) {
        return null;
      }
      
      // Get related sections, features, FAQs, and testimonials
      const content = await this.getContentWithRelations(contentRows[0]);
      
      return content;
    } catch (error) {
      console.error('Error finding website content by ID:', error);
      throw error;
    }
  }

  // Find content by criteria
  static async findOne(criteria = {}) {
    try {
      let query = 'SELECT * FROM website_content';
      const params = [];
      
      // Add WHERE clause if criteria provided
      if (Object.keys(criteria).length > 0) {
        const conditions = [];
        
        Object.entries(criteria).forEach(([key, value]) => {
          // Convert camelCase to snake_case
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
          conditions.push(`${snakeKey} = ?`);
          params.push(value);
        });
        
        query += ` WHERE ${conditions.join(' AND ')}`;
      }
      
      // Add ORDER BY clause to get the latest version
      query += ' ORDER BY version DESC LIMIT 1';
      
      const [rows] = await pool.query(query, params);
      
      if (rows.length === 0) {
        return null;
      }
      
      // Get related sections, features, FAQs, and testimonials
      const content = await this.getContentWithRelations(rows[0]);
      
      return content;
    } catch (error) {
      console.error('Error finding website content:', error);
      throw error;
    }
  }

  // Create new content
  static async create(contentData) {
    try {
      const { updatedBy } = contentData;
      
      // Get the latest version number
      const [versionResult] = await pool.query(
        'SELECT MAX(version) as maxVersion FROM website_content'
      );
      
      const newVersion = (versionResult[0].maxVersion || 0) + 1;
      
      // Insert new content record
      const [result] = await pool.query(
        'INSERT INTO website_content (version, updated_by) VALUES (?, ?)',
        [newVersion, updatedBy]
      );
      
      const contentId = result.insertId;
      
      // Insert sections if provided
      if (contentData.sections && contentData.sections.length > 0) {
        await this.insertSections(contentId, contentData.sections);
      }
      
      // Insert features if provided
      if (contentData.features && contentData.features.length > 0) {
        await this.insertFeatures(contentId, contentData.features);
      }
      
      // Insert FAQs if provided
      if (contentData.faqs && contentData.faqs.length > 0) {
        await this.insertFaqs(contentId, contentData.faqs);
      }
      
      // Insert testimonials if provided
      if (contentData.testimonials && contentData.testimonials.length > 0) {
        await this.insertTestimonials(contentId, contentData.testimonials);
      }
      
      return await this.findById(contentId);
    } catch (error) {
      console.error('Error creating website content:', error);
      throw error;
    }
  }

  // Update content
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];
      
      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Skip sections, features, faqs, testimonials - they're handled separately
        if (['sections', 'features', 'faqs', 'testimonials'].includes(key)) {
          return;
        }
        
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });
      
      // Add updated_at field
      fields.push('updated_at = CURRENT_TIMESTAMP');
      
      values.push(id); // Add ID for the WHERE clause
      
      // Update the main content record
      if (fields.length > 0) {
        await pool.query(
          `UPDATE website_content SET ${fields.join(', ')} WHERE id = ?`,
          values
        );
      }
      
      // Update sections if provided
      if (updateData.sections) {
        // Delete existing sections
        await pool.query('DELETE FROM content_sections WHERE content_id = ?', [id]);
        
        // Insert new sections
        await this.insertSections(id, updateData.sections);
      }
      
      // Update features if provided
      if (updateData.features) {
        // Delete existing features
        await pool.query('DELETE FROM features WHERE content_id = ?', [id]);
        
        // Insert new features
        await this.insertFeatures(id, updateData.features);
      }
      
      // Update FAQs if provided
      if (updateData.faqs) {
        // Delete existing FAQs
        await pool.query('DELETE FROM faqs WHERE content_id = ?', [id]);
        
        // Insert new FAQs
        await this.insertFaqs(id, updateData.faqs);
      }
      
      // Update testimonials if provided
      if (updateData.testimonials) {
        // Delete existing testimonials
        await pool.query('DELETE FROM testimonials WHERE content_id = ?', [id]);
        
        // Insert new testimonials
        await this.insertTestimonials(id, updateData.testimonials);
      }
      
      return await this.findById(id);
    } catch (error) {
      console.error('Error updating website content:', error);
      throw error;
    }
  }

  // Publish content
  static async publish(id, adminId) {
    try {
      await pool.query(
        'UPDATE website_content SET is_published = 1, published_at = CURRENT_TIMESTAMP, updated_by = ? WHERE id = ?',
        [adminId, id]
      );
      
      // Unpublish all other versions
      await pool.query(
        'UPDATE website_content SET is_published = 0 WHERE id != ?',
        [id]
      );
      
      return await this.findById(id);
    } catch (error) {
      console.error('Error publishing website content:', error);
      throw error;
    }
  }

  // Helper method to get content with all relations
  static async getContentWithRelations(contentRow) {
    // Get sections
    const [sectionsRows] = await pool.query(
      'SELECT * FROM content_sections WHERE content_id = ? ORDER BY order_num ASC',
      [contentRow.id]
    );
    
    // Get features
    const [featuresRows] = await pool.query(
      'SELECT * FROM features WHERE content_id = ? ORDER BY order_num ASC',
      [contentRow.id]
    );
    
    // Get FAQs
    const [faqsRows] = await pool.query(
      'SELECT * FROM faqs WHERE content_id = ? ORDER BY order_num ASC',
      [contentRow.id]
    );
    
    // Get testimonials
    const [testimonialsRows] = await pool.query(
      'SELECT * FROM testimonials WHERE content_id = ? ORDER BY order_num ASC',
      [contentRow.id]
    );
    
    // Format the content object
    return {
      id: contentRow.id,
      version: contentRow.version,
      isPublished: contentRow.is_published === 1,
      publishedAt: contentRow.published_at,
      sections: sectionsRows.map(row => ({
        id: row.id,
        sectionId: row.section_id,
        sectionName: row.section_name,
        title: row.title,
        subtitle: row.subtitle,
        content: row.content,
        buttonText: row.button_text,
        buttonUrl: row.button_url,
        imageUrl: row.image_url,
        order: row.order_num,
        isActive: row.is_active === 1
      })),
      features: featuresRows.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        icon: row.icon,
        order: row.order_num
      })),
      faqs: faqsRows.map(row => ({
        id: row.id,
        question: row.question,
        answer: row.answer,
        order: row.order_num
      })),
      testimonials: testimonialsRows.map(row => ({
        id: row.id,
        name: row.name,
        role: row.role,
        content: row.content,
        avatarUrl: row.avatar_url,
        order: row.order_num
      })),
      updatedBy: contentRow.updated_by,
      createdAt: contentRow.created_at,
      updatedAt: contentRow.updated_at
    };
  }

  // Helper method to insert sections
  static async insertSections(contentId, sections) {
    for (const [index, section] of sections.entries()) {
      await pool.query(
        'INSERT INTO content_sections (content_id, section_id, section_name, title, subtitle, content, ' +
        'button_text, button_url, image_url, order_num, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          contentId,
          section.sectionId,
          section.sectionName,
          section.title,
          section.subtitle,
          section.content,
          section.buttonText,
          section.buttonUrl,
          section.imageUrl,
          section.order || index,
          section.isActive === undefined ? 1 : (section.isActive ? 1 : 0)
        ]
      );
    }
  }

  // Helper method to insert features
  static async insertFeatures(contentId, features) {
    for (const [index, feature] of features.entries()) {
      await pool.query(
        'INSERT INTO features (content_id, title, description, icon, order_num) VALUES (?, ?, ?, ?, ?)',
        [
          contentId,
          feature.title,
          feature.description,
          feature.icon,
          feature.order || index
        ]
      );
    }
  }

  // Helper method to insert FAQs
  static async insertFaqs(contentId, faqs) {
    for (const [index, faq] of faqs.entries()) {
      await pool.query(
        'INSERT INTO faqs (content_id, question, answer, order_num) VALUES (?, ?, ?, ?)',
        [
          contentId,
          faq.question,
          faq.answer,
          faq.order || index
        ]
      );
    }
  }

  // Helper method to insert testimonials
  static async insertTestimonials(contentId, testimonials) {
    for (const [index, testimonial] of testimonials.entries()) {
      await pool.query(
        'INSERT INTO testimonials (content_id, name, role, content, avatar_url, order_num) VALUES (?, ?, ?, ?, ?, ?)',
        [
          contentId,
          testimonial.name,
          testimonial.role,
          testimonial.content,
          testimonial.avatarUrl,
          testimonial.order || index
        ]
      );
    }
  }
}

module.exports = WebsiteContent;
