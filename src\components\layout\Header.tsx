import { useState, useEffect } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Disclosure } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { scrollToSection } from '../../utils/scrollUtils'

const navigation = [
  { name: 'Home', href: '/' },
  { name: 'Features', href: '/#features' },
  { name: 'Demo', href: '/demo' },
  { name: 'Pricing', href: '/pricing' },
  { name: 'Download', href: '/download' },
  { name: 'Contact', href: '/contact' },
]

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const { pathname, hash } = useLocation()
  const navigate = useNavigate()

  // Check if user is logged in
  useEffect(() => {
    const userToken = localStorage.getItem('userToken')
    setIsLoggedIn(!!userToken)
  }, [pathname]) // Re-check when pathname changes

  // Handle navigation for hash links
  const handleNavigation = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    // If it's a hash link
    if (href.includes('#')) {
      e.preventDefault()
      const id = href.split('#')[1]

      if (pathname === '/') {
        // If we're already on the homepage, just scroll to the section
        scrollToSection(id)
      } else {
        // If we're on another page, navigate to homepage first, then scroll
        navigate('/')
        // Wait for navigation to complete before scrolling
        setTimeout(() => {
          scrollToSection(id)
        }, 100)
      }
    }
  }

  // Handle scroll event to change header appearance
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Function to check if a nav item is active
  const isActive = (item: { name: string; href: string }) => {
    if (item.href === '/') {
      return pathname === '/' && !hash
    }
    if (item.href.includes('#') && pathname === '/') {
      return hash === item.href.split('#')[1]
    }
    return pathname === item.href
  }

  return (
    <Disclosure
      as="nav"
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-dark-900/80 backdrop-blur-lg shadow-lg'
          : 'bg-transparent'
      }`}
    >
      {({ open }) => (
        <>
          <div className="container">
            <div className="relative flex h-20 items-center justify-between">
              <div className="absolute inset-y-0 left-0 flex items-center sm:hidden">
                {/* Mobile menu button*/}
                <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-dark-400 hover:bg-dark-800 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary">
                  <span className="absolute -inset-0.5" />
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>

              {/* Logo */}
              <div className="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                <motion.div
                  className="flex flex-shrink-0 items-center"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Link to="/" className="flex items-center">
                    <img
                      className="h-10 w-auto"
                      src="/images/logo.svg"
                      alt="Meta Master"
                    />
                    <span className="ml-3 text-xl font-display font-bold">Meta Master</span>
                  </Link>
                </motion.div>

                {/* Desktop navigation */}
                <div className="hidden sm:ml-6 sm:flex sm:space-x-8 sm:items-center">
                  {navigation.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}
                    >
                      <Link
                        to={item.href}
                        onClick={(e) => handleNavigation(e, item.href)}
                        className={`inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors duration-200 ${
                          isActive(item)
                            ? 'text-primary border-b-2 border-primary'
                            : 'text-dark-300 hover:text-white hover:border-b-2 hover:border-primary/50'
                        }`}
                      >
                        {item.name}
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="absolute inset-y-0 right-0 flex items-center space-x-4 pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                {isLoggedIn ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <Link
                      to="/user/dashboard"
                      className="text-white hover:text-primary transition-colors"
                    >
                      User Dashboard
                    </Link>
                  </motion.div>
                ) : (
                  <>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      <Link
                        to="/user/login"
                        className="text-white hover:text-primary transition-colors"
                      >
                        Log In
                      </Link>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                    >
                      <Link
                        to="/user/register"
                        className="text-white hover:text-primary transition-colors"
                      >
                        Create Account
                      </Link>
                    </motion.div>
                  </>
                )}

                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <Link
                    to="/pricing"
                    className="btn btn-primary"
                  >
                    Get Started
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Mobile menu */}
          <Disclosure.Panel className="sm:hidden bg-dark-800">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {navigation.map((item) => (
                <Disclosure.Button
                  key={item.name}
                  as={Link}
                  to={item.href}
                  onClick={(e) => handleNavigation(e, item.href)}
                  className={`block rounded-md px-3 py-2 text-base font-medium ${
                    isActive(item)
                      ? 'bg-primary/10 text-primary'
                      : 'text-dark-300 hover:bg-dark-700 hover:text-white'
                  }`}
                >
                  {item.name}
                </Disclosure.Button>
              ))}

              <div className="border-t border-dark-700 my-2 pt-2">
                {isLoggedIn ? (
                  <Disclosure.Button
                    as={Link}
                    to="/user/dashboard"
                    className="block rounded-md px-3 py-2 text-base font-medium text-dark-300 hover:bg-dark-700 hover:text-white"
                  >
                    User Dashboard
                  </Disclosure.Button>
                ) : (
                  <>
                    <Disclosure.Button
                      as={Link}
                      to="/user/login"
                      className="block rounded-md px-3 py-2 text-base font-medium text-dark-300 hover:bg-dark-700 hover:text-white"
                    >
                      Log In
                    </Disclosure.Button>

                    <Disclosure.Button
                      as={Link}
                      to="/user/register"
                      className="block rounded-md px-3 py-2 text-base font-medium text-dark-300 hover:bg-dark-700 hover:text-white"
                    >
                      Create Account
                    </Disclosure.Button>
                  </>
                )}

                <Disclosure.Button
                  as={Link}
                  to="/pricing"
                  className="block rounded-md px-3 py-2 mt-2 text-base font-medium bg-primary text-white"
                >
                  Get Started
                </Disclosure.Button>
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  )
}
