const { query } = require('../../config/db');
const bcrypt = require('bcryptjs');

class Admin {
  // Helper method to format admin data
  static _formatAdmin(admin) {
    return {
      id: admin.id,
      username: admin.username,
      password: admin.password,
      role: admin.role,
      createdAt: admin.created_at
    };
  }
  // Find admin by ID
  static async findById(id) {
    try {
      const [rows] = await query(
        'SELECT id, username, role, created_at FROM admins WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      const admin = rows[0];
      return this._formatAdmin(admin);
    } catch (error) {
      console.error('Error finding admin by ID:', error);
      throw error;
    }
  }

  // Find admin by username
  static async findOne(criteria) {
    try {
      console.log('Finding admin with criteria:', criteria);

      let sql = 'SELECT * FROM admins WHERE ';
      const params = [];

      if (criteria.username) {
        sql += 'username = ?';
        params.push(criteria.username);
      } else if (criteria.id) {
        sql += 'id = ?';
        params.push(criteria.id);
      } else {
        throw new Error('Invalid search criteria');
      }

      console.log('Executing SQL query:', sql, 'with params:', params);
      const [rows] = await query(sql, params);
      console.log('Query returned rows:', rows.length);

      if (rows.length === 0) {
        console.log('No admin found with the given criteria');
        return null;
      }

      // Convert snake_case to camelCase
      const admin = rows[0];
      console.log('Admin found:', admin.username);
      return this._formatAdmin(admin);
    } catch (error) {
      console.error('Error finding admin:', error);
      throw error;
    }
  }

  // Create a new admin
  static async create(adminData) {
    try {
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(adminData.password, salt);

      const [result] = await query(
        'INSERT INTO admins (username, password, role) VALUES (?, ?, ?)',
        [adminData.username, hashedPassword, adminData.role || 'admin']
      );

      // Get the newly created admin
      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating admin:', error);
      throw error;
    }
  }

  // Compare password
  static async comparePassword(adminId, password) {
    try {
      const [rows] = await query('SELECT password FROM admins WHERE id = ?', [adminId]);

      if (rows.length === 0) {
        return false;
      }

      const hashedPassword = rows[0].password;
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Error comparing password:', error);
      throw error;
    }
  }
}

module.exports = Admin;
