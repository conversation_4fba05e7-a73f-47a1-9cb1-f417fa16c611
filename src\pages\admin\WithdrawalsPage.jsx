import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CurrencyBangladeshiIcon,
  PhoneIcon
} from '@heroicons/react/24/outline'
import AdminLayout from '../../components/admin/AdminLayout'

export default function WithdrawalsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [withdrawals, setWithdrawals] = useState([])
  const [selectedWithdrawal, setSelectedWithdrawal] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [transactionId, setTransactionId] = useState('')
  const [adminNotes, setAdminNotes] = useState('')
  const [showApproveModal, setShowApproveModal] = useState(false)
  const [showRejectModal, setShowRejectModal] = useState(false)
  const [successMessage, setSuccessMessage] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    fetchWithdrawals()
  }, [])

  const fetchWithdrawals = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('adminToken')
      if (!token) {
        navigate('/power/login')
        return
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/withdrawal/admin/pending`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch withdrawal requests')
      }

      const data = await response.json()
      setWithdrawals(data.data || [])
    } catch (error) {
      console.error('Error fetching withdrawals:', error)
      setError('Failed to load withdrawal requests. Please try again.')

      // If token is invalid, redirect to login
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        localStorage.removeItem('adminToken')
        navigate('/power/login')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleApprove = async () => {
    if (!selectedWithdrawal) return

    setIsProcessing(true)
    setError(null)

    try {
      const token = localStorage.getItem('adminToken')
      if (!token) {
        navigate('/power/login')
        return
      }

      // Validate transaction ID
      if (!transactionId.trim()) {
        setError('Transaction ID is required')
        setIsProcessing(false)
        return
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      console.log('Approving withdrawal:', selectedWithdrawal);
      const withdrawalId = selectedWithdrawal.id || selectedWithdrawal._id;
      if (!withdrawalId) {
        setError('Withdrawal ID is missing');
        setIsProcessing(false);
        return;
      }

      const response = await fetch(`${apiUrl}/api/withdrawal/${withdrawalId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          transactionId,
          adminNotes
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccessMessage('Withdrawal request approved successfully')
        setShowApproveModal(false)
        setSelectedWithdrawal(null)
        setTransactionId('')
        setAdminNotes('')

        // Refresh the list
        fetchWithdrawals()
      } else {
        setError(data.message || 'Failed to approve withdrawal request')
      }
    } catch (error) {
      console.error('Error approving withdrawal:', error)
      setError('An error occurred. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReject = async () => {
    if (!selectedWithdrawal) return

    setIsProcessing(true)
    setError(null)

    try {
      const token = localStorage.getItem('adminToken')
      if (!token) {
        navigate('/power/login')
        return
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      console.log('Rejecting withdrawal:', selectedWithdrawal);
      const withdrawalId = selectedWithdrawal.id || selectedWithdrawal._id;
      if (!withdrawalId) {
        setError('Withdrawal ID is missing');
        setIsProcessing(false);
        return;
      }

      const response = await fetch(`${apiUrl}/api/withdrawal/${withdrawalId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          adminNotes
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccessMessage('Withdrawal request rejected successfully')
        setShowRejectModal(false)
        setSelectedWithdrawal(null)
        setAdminNotes('')

        // Refresh the list
        fetchWithdrawals()
      } else {
        setError(data.message || 'Failed to reject withdrawal request')
      }
    } catch (error) {
      console.error('Error rejecting withdrawal:', error)
      setError('An error occurred. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <AdminLayout>
      <div className="py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold gradient-text">Pending Withdrawals</h1>
          <button
            onClick={fetchWithdrawals}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-primary hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200"
          >
            <ClockIcon className="h-5 w-5 mr-2" />
            Refresh
          </button>
        </div>

        {successMessage && (
          <div className="mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-400">{successMessage}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setSuccessMessage(null)}
                    className="inline-flex bg-green-500/20 rounded-lg p-1.5 text-green-400 hover:bg-green-500/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                  >
                    <XCircleIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-400">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setError(null)}
                    className="inline-flex bg-red-500/20 rounded-lg p-1.5 text-red-400 hover:bg-red-500/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                  >
                    <XCircleIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : withdrawals.length === 0 ? (
          <div className="card p-6 text-center">
            <CurrencyBangladeshiIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No pending withdrawals</h3>
            <p className="mt-1 text-sm text-gray-500">There are no pending withdrawal requests at this time.</p>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {withdrawals.map((withdrawal) => (
                <li key={withdrawal.id || withdrawal._id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <CurrencyBangladeshiIcon className="h-10 w-10 text-indigo-600 bg-indigo-100 rounded-full p-2" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-indigo-600">
                            ৳{withdrawal.amount}
                          </div>
                          <div className="text-sm text-gray-500">
                            {withdrawal.user ? (withdrawal.user.name || `${withdrawal.user.firstName || ''} ${withdrawal.user.lastName || ''}`.trim() || withdrawal.user.username || withdrawal.user.email || 'User #' + withdrawal.userId) : 'Unknown User'}
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedWithdrawal(withdrawal)
                            setShowApproveModal(true)
                          }}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          Approve
                        </button>
                        <button
                          onClick={() => {
                            setSelectedWithdrawal(withdrawal)
                            setShowRejectModal(true)
                          }}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <XCircleIcon className="h-4 w-4 mr-1" />
                          Reject
                        </button>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <div className="flex items-center text-sm text-gray-500">
                          <PhoneIcon className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                          <p>
                            bKash: {withdrawal.paymentDetails?.bkashNumber || withdrawal.bkashNumber} ({withdrawal.paymentDetails?.accountType || withdrawal.accountType || 'personal'})
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <ClockIcon className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                        <p>
                          Requested on {new Date(withdrawal.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Approve Modal */}
        {showApproveModal && (
          <div className="fixed z-10 inset-0 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>
              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CheckCircleIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Approve Withdrawal
                      </h3>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          You are about to approve a withdrawal request of ৳{selectedWithdrawal?.amount} to bKash number {selectedWithdrawal?.paymentDetails?.bkashNumber || selectedWithdrawal?.bkashNumber}.
                        </p>
                        <div className="mt-4">
                          <label htmlFor="transactionId" className="block text-sm font-medium text-gray-700">
                            Transaction ID
                          </label>
                          <input
                            type="text"
                            id="transactionId"
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Enter bKash Transaction ID"
                            value={transactionId}
                            onChange={(e) => setTransactionId(e.target.value)}
                            required
                          />
                        </div>
                        <div className="mt-4">
                          <label htmlFor="adminNotes" className="block text-sm font-medium text-gray-700">
                            Notes (Optional)
                          </label>
                          <textarea
                            id="adminNotes"
                            rows="3"
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Add any notes about this transaction"
                            value={adminNotes}
                            onChange={(e) => setAdminNotes(e.target.value)}
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={handleApprove}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Approve'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => {
                      setShowApproveModal(false)
                      setSelectedWithdrawal(null)
                      setTransactionId('')
                      setAdminNotes('')
                    }}
                    disabled={isProcessing}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed z-10 inset-0 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>
              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                      <XCircleIcon className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Reject Withdrawal
                      </h3>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          You are about to reject a withdrawal request of ৳{selectedWithdrawal?.amount}. The amount will be returned to the user's available balance.
                        </p>
                        <div className="mt-4">
                          <label htmlFor="rejectAdminNotes" className="block text-sm font-medium text-gray-700">
                            Reason for Rejection
                          </label>
                          <textarea
                            id="rejectAdminNotes"
                            rows="3"
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Explain why this withdrawal is being rejected"
                            value={adminNotes}
                            onChange={(e) => setAdminNotes(e.target.value)}
                            required
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={handleReject}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Reject'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => {
                      setShowRejectModal(false)
                      setSelectedWithdrawal(null)
                      setAdminNotes('')
                    }}
                    disabled={isProcessing}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
