const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email address']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  affiliateId: {
    type: String,
    unique: true,
    sparse: true
  },
  affiliateCommission: {
    type: Number,
    default: 0
  },
  withdrawnCommission: {
    type: Number,
    default: 0
  },
  referredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  paymentInfo: {
    bkashNumber: {
      type: String,
      trim: true
    },
    accountType: {
      type: String,
      enum: ['personal', 'agent', 'merchant'],
      default: 'personal'
    }
  },
  licenseKeys: [{
    key: {
      type: String
    },
    plan: {
      type: String,
      enum: ['Monthly', 'Yearly', 'Lifetime']
    },
    purchaseDate: {
      type: Date,
      default: Date.now
    },
    expiryDate: {
      type: Date
    },
    status: {
      type: String,
      enum: ['active', 'expired', 'revoked', 'pending'],
      default: 'pending'
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },
    paymentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Payment'
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
UserSchema.pre('save', async function(next) {
  this.updatedAt = Date.now();

  // Generate affiliate ID if it doesn't exist
  if (!this.affiliateId) {
    // Generate a unique affiliate ID using the full username
    // Remove any special characters and spaces, and convert to lowercase
    const cleanUsername = this.username.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    this.affiliateId = cleanUsername;
  }

  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) {
    return next();
  }

  try {
    // Generate a salt
    const salt = await bcrypt.genSalt(10);
    // Hash the password along with the new salt
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to add a license key to the user
UserSchema.methods.addLicenseKey = function(licenseKey, plan, paymentId, paymentStatus = 'pending') {
  // Calculate expiry date based on plan
  let expiryDate = null;
  const now = new Date();

  if (plan === 'Monthly') {
    expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() + 1);
  } else if (plan === 'Yearly') {
    expiryDate = new Date(now);
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);
  }
  // Lifetime plan has no expiry date (null)

  // Set the license status based on payment status
  const licenseStatus = paymentStatus === 'approved' ? 'active' : 'pending';

  this.licenseKeys.push({
    key: licenseKey,
    plan,
    purchaseDate: now,
    expiryDate,
    status: licenseStatus,
    paymentStatus,
    paymentId
  });

  return this.save();
};

// Method to get active license keys
UserSchema.methods.getActiveLicenseKeys = function() {
  return this.licenseKeys.filter(license => license.status === 'active');
};

// Method to add a pending payment record without a license key
UserSchema.methods.addPendingPayment = function(plan, paymentId) {
  // Calculate expiry date based on plan (will be used when license is approved)
  let expiryDate = null;
  const now = new Date();

  if (plan === 'Monthly') {
    expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() + 1);
  } else if (plan === 'Yearly') {
    expiryDate = new Date(now);
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);
  }
  // Lifetime plan has no expiry date (null)

  this.licenseKeys.push({
    key: '', // Empty license key - will be filled by admin
    plan,
    purchaseDate: now,
    expiryDate,
    status: 'pending',
    paymentStatus: 'pending',
    paymentId
  });

  return this.save();
};

module.exports = mongoose.model('User', UserSchema);
