const mongoose = require('mongoose');

const WithdrawalSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 1000 // Minimum withdrawal amount: 1000 Taka
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: ['bkash'],
    default: 'bkash'
  },
  paymentDetails: {
    bkashNumber: {
      type: String,
      required: true
    },
    accountType: {
      type: String,
      enum: ['personal', 'agent', 'merchant'],
      default: 'personal'
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'rejected', 'paid'],
    default: 'pending'
  },
  adminNotes: {
    type: String
  },
  transactionId: {
    type: String
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  processedAt: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
WithdrawalSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Withdrawal', WithdrawalSchema);
