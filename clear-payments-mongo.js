const mongoose = require('mongoose');
require('dotenv').config();

// Import Payment model
const Payment = require('./hostinger-upload/backend/models/Payment');

const clearPayments = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/meta-master', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('Connected to MongoDB');
    
    // Count existing payments
    const count = await Payment.countDocuments();
    console.log(`Found ${count} payments in database`);
    
    if (count > 0) {
      // Delete all payments
      const result = await Payment.deleteMany({});
      console.log(`✅ Successfully deleted ${result.deletedCount} payments`);
    } else {
      console.log('No payments found to delete');
    }
    
    // Verify deletion
    const remainingCount = await Payment.countDocuments();
    console.log(`Remaining payments: ${remainingCount}`);
    
  } catch (error) {
    console.error('❌ Error clearing payments:', error);
  } finally {
    // Close connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
};

// Run the script
clearPayments();
