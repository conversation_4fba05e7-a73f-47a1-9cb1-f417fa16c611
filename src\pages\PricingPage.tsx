import { useEffect } from 'react'
import { motion } from 'framer-motion'
import PricingSection from '../components/pricing/PricingSection'
import CTA from '../components/home/<USER>'

export default function PricingPage() {
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0)

    // Set page title
    document.title = 'Pricing - Meta Master'
  }, [])

  return (
    <div className="bg-[#051824]">
      <PricingSection />

      <div className="py-16">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <motion.h2
              className="text-2xl md:text-3xl font-bold mb-8 text-center text-white"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Frequently Asked <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500">Questions</span>
            </motion.h2>

            <div className="space-y-6">
              <motion.div
                className="bg-[#072a3a] border border-cyan-500/20 rounded-lg p-6 shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                whileHover={{
                  boxShadow: '0 0 20px rgba(0, 167, 217, 0.2)',
                  borderColor: 'rgba(0, 167, 217, 0.3)'
                }}
              >
                <h3 className="text-xl font-bold mb-2 text-white">Do you offer refunds?</h3>
                <p className="text-gray-400">Yes, we offer a 30-day money-back guarantee. If you're not satisfied with Meta Master for any reason, simply contact our support team within 30 days of purchase for a full refund.</p>
              </motion.div>

              <motion.div
                className="bg-[#072a3a] border border-cyan-500/20 rounded-lg p-6 shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                whileHover={{
                  boxShadow: '0 0 20px rgba(0, 167, 217, 0.2)',
                  borderColor: 'rgba(0, 167, 217, 0.3)'
                }}
              >
                <h3 className="text-xl font-bold mb-2 text-white">Can I upgrade my plan later?</h3>
                <p className="text-gray-400">Absolutely! You can upgrade your plan at any time. You'll only pay the difference between your current plan and the new plan, prorated for the remaining time on your subscription.</p>
              </motion.div>

              <motion.div
                className="bg-[#072a3a] border border-cyan-500/20 rounded-lg p-6 shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                whileHover={{
                  boxShadow: '0 0 20px rgba(0, 167, 217, 0.2)',
                  borderColor: 'rgba(0, 167, 217, 0.3)'
                }}
              >
                <h3 className="text-xl font-bold mb-2 text-white">What payment methods do you accept?</h3>
                <p className="text-gray-400">We accept all major credit cards (Visa, Mastercard, American Express), PayPal, and cryptocurrency payments. All transactions are secure and encrypted.</p>
              </motion.div>

              <motion.div
                className="bg-[#072a3a] border border-cyan-500/20 rounded-lg p-6 shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
                whileHover={{
                  boxShadow: '0 0 20px rgba(0, 167, 217, 0.2)',
                  borderColor: 'rgba(0, 167, 217, 0.3)'
                }}
              >
                <h3 className="text-xl font-bold mb-2 text-white">Are there any additional costs?</h3>
                <p className="text-gray-400">The only potential additional cost is for the Google Gemini API usage. Google offers a free tier that's sufficient for most individual users. Heavy usage may require upgrading to Google's paid tier, but this is typically only necessary for Agency users processing thousands of files per month.</p>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      <CTA />
    </div>
  )
}
