# Server Configuration
PORT=5001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=meta-master-jwt-secret-change-in-production
JWT_EXPIRE=30d

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=meta_master

# CORS Origins
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,https://getmetamaster.com,https://www.getmetamaster.com

# Email Configuration
EMAIL_HOST=smtp.hostinger.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>
