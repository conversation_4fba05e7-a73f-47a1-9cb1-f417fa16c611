import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import But<PERSON> from '../ui/Button'
import DynamicUserCounter from '../ui/DynamicUserCounter'
import { getFormattedUserCount } from '../../utils/userCounter'
import { useVersion } from '../../hooks/useVersion'

const platforms = [
  'Adobe Stock',
  'Shutterstock',
  'Freepik',
  'Vecteezy',
  'Getty Images',
  'Dreamstime'
]

export default function Hero() {
  const [userCountText, setUserCountText] = useState('1200+')
  const { versionBadgeText, shouldShowInHero, shouldShowInFloating, formattedVersion } = useVersion()

  useEffect(() => {
    // Update the user count text when component mounts
    setUserCountText(getFormattedUserCount())
  }, [])

  return (
    <div className="relative overflow-hidden pt-20">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-radial from-primary/20 to-transparent opacity-50 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-radial from-secondary/20 to-transparent opacity-50 blur-3xl"></div>
      </div>

      <div className="container relative z-10 pt-16 pb-20 md:pt-24 md:pb-28 lg:pt-32 lg:pb-36">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left column - Text content */}
          <div>
            {shouldShowInHero && (
              <motion.div
                className="mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 mb-4">
                  <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                  <span className="text-sm font-medium text-primary">{versionBadgeText}</span>
                </div>
              </motion.div>
            )}

            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <span className="gradient-text">Meta Master</span> - #1 AI Metadata Generator for Microstock Success
            </motion.h1>

            <motion.p
              className="text-xl text-dark-300 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Meta Master is the ultimate AI-powered metadata generator trusted by <span className="text-primary font-semibold">{userCountText} microstock contributors</span>. Generate SEO-optimized titles, keywords, and descriptions for images, vectors, and videos using Google's advanced Gemini AI.
            </motion.p>

            <motion.div
              className="flex flex-wrap gap-3 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="flex items-center gap-2 text-sm text-dark-300">
                <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>5x Faster than Manual Tagging</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-dark-300">
                <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Batch Processing</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-dark-300">
                <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>CSV Export Ready</span>
              </div>
            </motion.div>

            <motion.div
              className="flex flex-wrap gap-4 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Button to="/download" variant="primary" size="lg" animate>
                Download Now
              </Button>
              <Button to="/demo" variant="outline" size="lg" animate>
                See Demo
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <p className="text-sm text-dark-400 mb-3">Perfect for contributors on:</p>
              <div className="flex flex-wrap gap-3">
                {platforms.map((platform, index) => (
                  <motion.span
                    key={platform}
                    className="inline-block px-3 py-1 rounded-full bg-dark-800 text-dark-300 text-sm"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                  >
                    {platform}
                  </motion.span>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Right column - App preview */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
          >
            <div className="relative z-10">
              <div className="relative rounded-xl overflow-hidden shadow-2xl border border-dark-700">
                <img
                  src="/src/assets/images/app-preview.png"
                  alt="Meta Master Application"
                  className="w-full h-auto"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-950/80 to-transparent opacity-60"></div>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-6 -right-6 p-4 bg-dark-800/90 backdrop-blur-sm rounded-lg border border-dark-700 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-dark-300">Powered by</p>
                    <p className="text-sm font-semibold">Google Gemini 2.5 AI</p>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -left-6 p-4 bg-dark-800/90 backdrop-blur-sm rounded-lg border border-dark-700 shadow-lg">
                <div className="text-center">
                  <p className="text-xs text-dark-300">Trusted Users</p>
                  <p className="text-xl font-bold gradient-text">
                    <DynamicUserCounter
                      suffix="+"
                      duration={1.5}
                      className="font-bold"
                      showExact={false}
                    />
                  </p>
                  <p className="text-xs text-dark-400">contributors worldwide</p>
                </div>
              </div>

              {shouldShowInFloating && (
                <div className="absolute top-1/2 -left-8 p-3 bg-dark-800/90 backdrop-blur-sm rounded-lg border border-dark-700 shadow-lg">
                  <div className="text-center">
                    <p className="text-xs text-dark-300">Version</p>
                    <p className="text-lg font-bold text-primary">{formattedVersion}</p>
                    <p className="text-xs text-dark-400">Latest</p>
                  </div>
                </div>
              )}
            </div>

            {/* Background glow */}
            <div className="absolute inset-0 bg-gradient-radial from-primary/20 to-transparent blur-3xl opacity-30"></div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
