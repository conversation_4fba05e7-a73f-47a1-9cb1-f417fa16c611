{"name": "meta-master-website", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "clear-payments": "node clear-local-payments.js"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "framer-motion": "^10.16.4", "mysql2": "^3.14.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-router-dom": "^6.16.0"}, "devDependencies": {"@types/node": "^22.15.12", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^6.3.4"}}