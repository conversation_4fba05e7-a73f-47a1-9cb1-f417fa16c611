import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { useLocation, Link } from 'react-router-dom'
import Button from '../components/ui/Button'

export default function ThankYouPage() {
  const location = useLocation()
  const queryParams = new URLSearchParams(location.search)

  const orderId = queryParams.get('order_id') || 'MM-2023-12345'
  const plan = queryParams.get('plan') || 'Professional'
  const amount = queryParams.get('amount') || '79'
  const paymentMethod = queryParams.get('payment_method') || 'Credit Card'
  const licenseKey = queryParams.get('license_key') || 'XXXXX-XXXXX-XXXXX-XXXXX-XXXXX'

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0)

    // Set page title
    document.title = 'Thank You - Meta Master'
  }, [])

  return (
    <div className="pt-32 pb-20 md:pt-40 md:pb-32">
      <div className="container">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-12">
            <motion.div
              className="inline-block p-4 rounded-full bg-primary/20 text-primary mb-6"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-5xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Thank You for Your Purchase!
            </motion.h1>

            <motion.p
              className="text-xl text-dark-300 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              Your order has been successfully processed. Your license key is displayed below - please save it for activating Meta Master.
            </motion.p>
          </div>

          <motion.div
            className="card mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h2 className="text-2xl font-bold mb-6">Order Summary</h2>

            <div className="space-y-4">
              <div className="flex justify-between py-2 border-b border-dark-700">
                <span className="text-dark-400">Order ID:</span>
                <span className="font-medium">{orderId}</span>
              </div>

              <div className="flex justify-between py-2 border-b border-dark-700">
                <span className="text-dark-400">Plan:</span>
                <span className="font-medium">Meta Master {plan}</span>
              </div>

              <div className="flex justify-between py-2 border-b border-dark-700">
                <span className="text-dark-400">Amount:</span>
                <span className="font-medium">${amount}</span>
              </div>

              <div className="flex justify-between py-2 border-b border-dark-700">
                <span className="text-dark-400">Payment Method:</span>
                <span className="font-medium">{paymentMethod}</span>
              </div>

              <div className="flex justify-between py-2 border-b border-dark-700">
                <span className="text-dark-400">Date:</span>
                <span className="font-medium">{new Date().toLocaleDateString()}</span>
              </div>

              <div className="flex justify-between py-2">
                <span className="text-dark-400">License Key:</span>
                <div className="flex items-center">
                  <span className="font-medium text-primary">{licenseKey}</span>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(licenseKey)
                      alert('License key copied to clipboard!')
                    }}
                    className="ml-2 p-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors"
                    title="Copy to clipboard"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="card mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <h2 className="text-2xl font-bold mb-4">Next Steps</h2>

            <ol className="space-y-4 mb-6">
              <li className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">
                  1
                </div>
                <div>
                  <p className="font-medium">Save your license key</p>
                  <p className="text-dark-400 text-sm">Copy and save your license key displayed above. You'll need it to activate Meta Master.</p>
                </div>
              </li>

              <li className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">
                  2
                </div>
                <div>
                  <p className="font-medium">Download Meta Master</p>
                  <p className="text-dark-400 text-sm">Use the download link in your email or click the button below to download the software.</p>
                </div>
              </li>

              <li className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">
                  3
                </div>
                <div>
                  <p className="font-medium">Install and activate</p>
                  <p className="text-dark-400 text-sm">Follow the installation instructions and enter your license key when prompted.</p>
                </div>
              </li>

              <li className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/20 text-primary flex items-center justify-center">
                  4
                </div>
                <div>
                  <p className="font-medium">Get started</p>
                  <p className="text-dark-400 text-sm">Follow our quick start guide to begin generating metadata for your content.</p>
                </div>
              </li>
            </ol>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button href="#" variant="primary" animate>
                Download Meta Master
              </Button>
              <Button href="#" variant="outline" animate>
                View Quick Start Guide
              </Button>
            </div>
          </motion.div>

          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <p className="text-dark-400 mb-6">
              If you have any questions or need assistance, please don't hesitate to <Link to="/contact" className="text-primary hover:underline">contact our support team</Link>.
            </p>

            <Button to="/" variant="outline">
              Return to Home
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
