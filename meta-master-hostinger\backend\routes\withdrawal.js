const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Withdrawal = require('../models/Withdrawal');
const { authenticate, isAdmin } = require('../middleware/auth');

/**
 * @route   POST /api/withdrawal/request
 * @desc    Create a new withdrawal request
 * @access  Private
 */
router.post('/request', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount, bkashNumber, accountType } = req.body;

    // Validate required fields
    if (!amount || !bkashNumber) {
      return res.status(400).json({
        success: false,
        message: 'Amount and bKash number are required'
      });
    }

    // Validate minimum withdrawal amount
    if (amount < 1000) {
      return res.status(400).json({
        success: false,
        message: 'Minimum withdrawal amount is 1000 Taka'
      });
    }

    // Get user with affiliate info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate available balance
    // Get pending withdrawal requests
    const pendingWithdrawals = await Withdrawal.find({
      user: userId,
      status: 'pending'
    });

    // Calculate total pending withdrawal amount
    const pendingWithdrawalAmount = pendingWithdrawals.reduce(
      (sum, withdrawal) => sum + withdrawal.amount,
      0
    );

    // Available balance = total earnings - withdrawn - pending withdrawals
    const availableBalance = user.affiliateCommission - user.withdrawnCommission - pendingWithdrawalAmount;

    // Check if user has enough available balance
    if (availableBalance < amount) {
      return res.status(400).json({
        success: false,
        message: `Insufficient balance for withdrawal. Available balance: ৳${availableBalance}`
      });
    }

    // Save bKash number to user profile if not already saved
    if (!user.paymentInfo.bkashNumber || user.paymentInfo.bkashNumber !== bkashNumber) {
      user.paymentInfo.bkashNumber = bkashNumber;
      user.paymentInfo.accountType = accountType || 'personal';
      await user.save();
    }

    // Create new withdrawal request
    const withdrawal = new Withdrawal({
      user: userId,
      amount,
      paymentMethod: 'bkash',
      paymentDetails: {
        bkashNumber,
        accountType: accountType || 'personal'
      },
      status: 'pending'
    });

    // Save the withdrawal request
    await withdrawal.save();

    // We don't need to deduct from affiliateCommission here
    // The amount will be tracked in the withdrawal record
    // and will be added to withdrawnCommission when approved
    // This prevents negative balance issues

    // Return success response
    return res.status(201).json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        id: withdrawal._id,
        amount: withdrawal.amount,
        status: withdrawal.status,
        createdAt: withdrawal.createdAt
      }
    });
  } catch (error) {
    console.error('Withdrawal request error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during withdrawal request',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/history
 * @desc    Get withdrawal history for the current user
 * @access  Private
 */
router.get('/history', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get all withdrawal requests for this user, sorted by creation date (newest first)
    const withdrawals = await Withdrawal.find({ user: userId }).sort({ createdAt: -1 });

    // Return withdrawal history
    return res.status(200).json({
      success: true,
      count: withdrawals.length,
      data: withdrawals
    });
  } catch (error) {
    console.error('Get withdrawal history error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving withdrawal history',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   PUT /api/withdrawal/payment-info
 * @desc    Update user's payment information
 * @access  Private
 */
router.put('/payment-info', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { bkashNumber, accountType } = req.body;

    // Validate required fields
    if (!bkashNumber) {
      return res.status(400).json({
        success: false,
        message: 'bKash number is required'
      });
    }

    // Get user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update payment info
    user.paymentInfo.bkashNumber = bkashNumber;
    user.paymentInfo.accountType = accountType || 'personal';
    await user.save();

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Payment information updated successfully',
      data: {
        bkashNumber: user.paymentInfo.bkashNumber,
        accountType: user.paymentInfo.accountType
      }
    });
  } catch (error) {
    console.error('Update payment info error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while updating payment information',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/payment-info
 * @desc    Get user's payment information
 * @access  Private
 */
router.get('/payment-info', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user with payment info
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Return payment info
    return res.status(200).json({
      success: true,
      data: {
        bkashNumber: user.paymentInfo.bkashNumber || '',
        accountType: user.paymentInfo.accountType || 'personal'
      }
    });
  } catch (error) {
    console.error('Get payment info error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving payment information',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/withdrawal/admin/pending
 * @desc    Get all pending withdrawal requests
 * @access  Private (Admin only)
 */
router.get('/admin/pending', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all pending withdrawal requests, sorted by creation date (oldest first)
    const withdrawals = await Withdrawal.find({ status: 'pending' })
      .sort({ createdAt: 1 })
      .populate('user', 'firstName lastName email username');

    // Return pending withdrawals
    return res.status(200).json({
      success: true,
      count: withdrawals.length,
      data: withdrawals
    });
  } catch (error) {
    console.error('Get pending withdrawals error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving pending withdrawals',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/withdrawal/:id/approve
 * @desc    Approve a withdrawal request
 * @access  Private (Admin only)
 */
router.post('/:id/approve', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { transactionId, adminNotes } = req.body;

    // Validate transaction ID
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
    }

    // Find withdrawal request
    const withdrawal = await Withdrawal.findById(id);
    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal request not found'
      });
    }

    // Check if withdrawal is already processed
    if (withdrawal.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: `Withdrawal request already ${withdrawal.status}`
      });
    }

    // Update withdrawal status
    withdrawal.status = 'paid';
    withdrawal.transactionId = transactionId;
    withdrawal.adminNotes = adminNotes || '';
    withdrawal.processedBy = req.user.id;
    withdrawal.processedAt = new Date();
    await withdrawal.save();

    // Update user's withdrawn amount
    const user = await User.findById(withdrawal.user);
    if (user) {
      user.withdrawnCommission += withdrawal.amount;
      await user.save();
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Withdrawal request approved and marked as paid',
      data: {
        id: withdrawal._id,
        amount: withdrawal.amount,
        status: withdrawal.status,
        transactionId: withdrawal.transactionId
      }
    });
  } catch (error) {
    console.error('Approve withdrawal error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during withdrawal approval',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/withdrawal/:id/reject
 * @desc    Reject a withdrawal request
 * @access  Private (Admin only)
 */
router.post('/:id/reject', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { adminNotes } = req.body;

    // Find withdrawal request
    const withdrawal = await Withdrawal.findById(id);
    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal request not found'
      });
    }

    // Check if withdrawal is already processed
    if (withdrawal.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: `Withdrawal request already ${withdrawal.status}`
      });
    }

    // Update withdrawal status
    withdrawal.status = 'rejected';
    withdrawal.adminNotes = adminNotes || 'Rejected by admin';
    withdrawal.processedBy = req.user.id;
    withdrawal.processedAt = new Date();
    await withdrawal.save();

    // No need to return the amount to user's available balance
    // since we're not deducting it when the withdrawal is requested

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Withdrawal request rejected',
      data: {
        id: withdrawal._id,
        amount: withdrawal.amount,
        status: withdrawal.status
      }
    });
  } catch (error) {
    console.error('Reject withdrawal error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during withdrawal rejection',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
