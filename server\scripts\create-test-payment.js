/**
 * <PERSON><PERSON><PERSON> to create a test payment
 *
 * Usage:
 * node scripts/create-test-payment.js
 */

require('dotenv').config();
const { query } = require('../config/db');

async function createTestPayment() {
  try {
    // Create multiple test payments with different statuses
    const payments = [
      { status: 'pending', plan: 'Monthly', amount: 199 },
      { status: 'approved', plan: 'Yearly', amount: 999 },
      { status: 'rejected', plan: 'Lifetime', amount: 1599 }
    ];

    for (const payment of payments) {
      // Generate a unique order ID
      const orderId = `TEST-${payment.status}-${Date.now()}`;

      // Insert test payment
      const [result] = await query(
        'INSERT INTO payments (order_id, transaction_id, payment_method, amount, plan, status) ' +
        'VALUES (?, ?, ?, ?, ?, ?)',
        [orderId, `TEST-TX-${Date.now()}`, 'bkash', payment.amount, payment.plan, payment.status]
      );

      console.log(`Test payment created successfully with order ID: ${orderId}`);
      console.log(`Payment ID: ${result.insertId}`);
      console.log(`Status: ${payment.status}`);
      console.log('---');

      // Add a small delay to ensure unique timestamps
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    process.exit(0);
  } catch (error) {
    console.error('Error creating test payment:', error);
    process.exit(1);
  }
}

// Connect to database and create test payment
const { testConnection } = require('../config/db');

testConnection()
  .then(connected => {
    if (!connected) {
      console.error('Failed to connect to MySQL. Please check your configuration.');
      process.exit(1);
    }

    createTestPayment();
  })
  .catch(err => {
    console.error('MySQL connection error:', err);
    process.exit(1);
  });
