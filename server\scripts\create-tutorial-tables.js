const { query } = require('../config/db');

const createTutorialTables = async () => {
  try {

    console.log('Using existing database connection');

    // Create tutorial_categories table
    const createCategoriesTable = `
      CREATE TABLE IF NOT EXISTS tutorial_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        order_num INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `;

    // Create tutorials table
    const createTutorialsTable = `
      CREATE TABLE IF NOT EXISTS tutorials (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        duration VARCHAR(20),
        level VARCHAR(50),
        video_url VARCHAR(255),
        thumbnail_url VARCHAR(255),
        category_id INT,
        order_num INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES tutorial_categories(id) ON DELETE SET NULL
      )
    `;

    // Execute table creation
    await query(createCategoriesTable);
    console.log('Tutorial categories table created successfully');

    await query(createTutorialsTable);
    console.log('Tutorials table created successfully');

    // Insert some sample categories
    const insertCategories = `
      INSERT IGNORE INTO tutorial_categories (name, slug, description, order_num) VALUES
      ('Getting Started', 'getting-started', 'Basic tutorials for beginners', 1),
      ('Advanced Features', 'advanced-features', 'Advanced functionality tutorials', 2),
      ('Tips & Tricks', 'tips-tricks', 'Helpful tips and tricks', 3)
    `;

    await query(insertCategories);
    console.log('Sample categories inserted');

    // Insert some sample tutorials
    const insertTutorials = `
      INSERT IGNORE INTO tutorials (title, description, duration, level, video_url, category_id, order_num) VALUES
      ('Introduction to Meta Master', 'Learn the basics of Meta Master', '5:30', 'Beginner', 'https://example.com/video1', 1, 1),
      ('Creating Your First Metadata', 'Step by step guide to create metadata', '8:45', 'Beginner', 'https://example.com/video2', 1, 2),
      ('Advanced Keyword Optimization', 'Learn advanced keyword techniques', '12:20', 'Advanced', 'https://example.com/video3', 2, 1)
    `;

    await query(insertTutorials);
    console.log('Sample tutorials inserted');

    console.log('Tutorial tables setup completed successfully');

  } catch (error) {
    console.error('Error creating tutorial tables:', error);
    process.exit(1);
  }
};

// Run the script
createTutorialTables();
