import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  CurrencyBangladeshiIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PhoneIcon
} from '@heroicons/react/24/outline'
import UserLayout from '../../components/user/UserLayout'

export default function WithdrawalPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  const [withdrawalHistory, setWithdrawalHistory] = useState([])
  const [paymentInfo, setPaymentInfo] = useState({
    bkashNumber: '',
    accountType: 'personal'
  })
  const [withdrawalAmount, setWithdrawalAmount] = useState('')
  const [affiliateData, setAffiliateData] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const token = localStorage.getItem('userToken')
        if (!token) {
          navigate('/user/login')
          return
        }

        // Fetch affiliate stats
        const affiliateResponse = await fetch('http://localhost:5001/api/affiliate/stats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!affiliateResponse.ok) {
          throw new Error('Failed to fetch affiliate data')
        }

        const affiliateData = await affiliateResponse.json()
        setAffiliateData(affiliateData.data)

        // Fetch payment info
        const paymentInfoResponse = await fetch('http://localhost:5001/api/withdrawal/payment-info', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (paymentInfoResponse.ok) {
          const paymentInfoData = await paymentInfoResponse.json()
          console.log('Payment info data:', paymentInfoData)

          // Handle different possible data structures
          let bkashNumber = ''
          let accountType = 'personal'

          if (paymentInfoData.data) {
            if (typeof paymentInfoData.data === 'object') {
              bkashNumber = paymentInfoData.data.bkashNumber || ''
              accountType = paymentInfoData.data.accountType || 'personal'
            }
          }

          setPaymentInfo({
            bkashNumber,
            accountType
          })
        }

        // Fetch withdrawal history
        const historyResponse = await fetch('http://localhost:5001/api/withdrawal/history', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (historyResponse.ok) {
          const historyData = await historyResponse.json()
          setWithdrawalHistory(historyData.data || [])
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        setError('Failed to load data. Please try again.')

        // If token is invalid, redirect to login
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          localStorage.removeItem('userToken')
          navigate('/user/login')
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [navigate])

  const handlePaymentInfoSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)
    setSuccess(null)

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      const response = await fetch('http://localhost:5001/api/withdrawal/payment-info', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(paymentInfo)
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Payment information updated successfully')
      } else {
        setError(data.message || 'Failed to update payment information')
      }
    } catch (error) {
      console.error('Error updating payment info:', error)
      setError('An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleWithdrawalSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)
    setSuccess(null)

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      // Validate amount
      const amount = parseFloat(withdrawalAmount)
      if (isNaN(amount) || amount < 1000) {
        setError('Minimum withdrawal amount is 1000 Taka')
        setIsSubmitting(false)
        return
      }

      // Make sure we have the latest available balance
      const availableBalance = affiliateData?.availableBalance || 0

      if (amount > availableBalance) {
        setError(`Insufficient balance for withdrawal. Your available balance is ৳${availableBalance}`)
        setIsSubmitting(false)
        return
      }

      // Validate bKash number
      if (!paymentInfo.bkashNumber) {
        setError('Please enter your bKash number')
        setIsSubmitting(false)
        return
      }

      console.log('Submitting withdrawal request with data:', {
        amount,
        bkashNumber: paymentInfo.bkashNumber,
        accountType: paymentInfo.accountType
      })

      const response = await fetch('http://localhost:5001/api/withdrawal/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          amount,
          bkashNumber: paymentInfo.bkashNumber,
          accountType: paymentInfo.accountType
        })
      })

      console.log('Withdrawal request response status:', response.status)

      try {
        const data = await response.json()
        console.log('Withdrawal request response data:', data)

        if (response.ok) {
          setSuccess('Withdrawal request submitted successfully')
          setWithdrawalAmount('')

          // Refresh data
          try {
            const affiliateResponse = await fetch('http://localhost:5001/api/affiliate/stats', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            })

            if (affiliateResponse.ok) {
              const affiliateData = await affiliateResponse.json()
              setAffiliateData(affiliateData.data)
            }
          } catch (refreshError) {
            console.error('Error refreshing affiliate data:', refreshError)
          }

          try {
            const historyResponse = await fetch('http://localhost:5001/api/withdrawal/history', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            })

            if (historyResponse.ok) {
              const historyData = await historyResponse.json()
              setWithdrawalHistory(historyData.data || [])
            }
          } catch (historyError) {
            console.error('Error refreshing withdrawal history:', historyError)
          }
        } else {
          setError(data.message || 'Failed to submit withdrawal request')
        }
      } catch (parseError) {
        console.error('Error parsing response:', parseError)
        setError('Failed to submit withdrawal request: ' + (response.statusText || 'Server error'))
      }
    } catch (error) {
      console.error('Error submitting withdrawal:', error)
      setError('An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleFixBalance = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const token = localStorage.getItem('userToken')
      if (!token) {
        navigate('/user/login')
        return
      }

      const response = await fetch('http://localhost:5001/api/affiliate/fix-balance', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Balance fixed successfully')

        // Refresh data
        const affiliateResponse = await fetch('http://localhost:5001/api/affiliate/stats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (affiliateResponse.ok) {
          const affiliateData = await affiliateResponse.json()
          setAffiliateData(affiliateData.data)
        }
      } else {
        setError(data.message || 'Failed to fix balance')
      }
    } catch (error) {
      console.error('Error fixing balance:', error)
      setError('An error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400">
            <ClockIcon className="w-3 h-3 mr-1" />
            Pending
          </span>
        )
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/20 text-green-400">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Paid
          </span>
        )
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/20 text-red-400">
            <XCircleIcon className="w-3 h-3 mr-1" />
            Rejected
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        )
    }
  }

  return (
    <UserLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-light mb-6">Withdraw Earnings</h1>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4 mb-6">
            {error}
          </div>
        ) : (
          <div className="space-y-6">
            {success && (
              <div className="bg-green-900/10 border border-green-900/20 text-green-500 rounded-lg p-4 mb-6">
                {success}
              </div>
            )}

            {/* Balance Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
            >
              {/* Available Balance */}
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-lg bg-primary/10">
                    <CurrencyBangladeshiIcon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="ml-4">
                    <p className="text-text text-sm">Available Balance</p>
                    <h3 className="text-2xl font-semibold text-light">৳{affiliateData?.availableBalance || 0}</h3>
                    {affiliateData?.availableBalance < 0 && (
                      <button
                        onClick={handleFixBalance}
                        className="mt-2 text-xs text-primary hover:text-primary/80 underline"
                      >
                        Fix Balance
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Total Earnings */}
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-lg bg-secondary/10">
                    <CurrencyBangladeshiIcon className="h-6 w-6 text-secondary" />
                  </div>
                  <div className="ml-4">
                    <p className="text-text text-sm">Total Earnings</p>
                    <h3 className="text-2xl font-semibold text-light">৳{(affiliateData?.totalEarnings || 0)}</h3>
                  </div>
                </div>
              </div>

              {/* Withdrawn Amount */}
              <div className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-lg bg-green-500/10">
                    <CurrencyBangladeshiIcon className="h-6 w-6 text-green-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-text text-sm">Withdrawn Amount</p>
                    <h3 className="text-2xl font-semibold text-light">৳{affiliateData?.withdrawnCommission || 0}</h3>
                  </div>
                </div>
              </div>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Payment Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-light mb-4">Payment Information</h2>
                <p className="text-text mb-4">
                  Update your bKash information to receive payments
                </p>

                <form onSubmit={handlePaymentInfoSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="bkashNumber" className="block text-sm font-medium text-light mb-1">
                      bKash Number
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <PhoneIcon className="h-5 w-5 text-text" />
                      </div>
                      <input
                        type="text"
                        id="bkashNumber"
                        className="w-full bg-dark border border-border rounded-lg pl-10 px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                        placeholder="01XXXXXXXXX"
                        value={paymentInfo.bkashNumber}
                        onChange={(e) => setPaymentInfo({ ...paymentInfo, bkashNumber: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="accountType" className="block text-sm font-medium text-light mb-1">
                      Account Type
                    </label>
                    <select
                      id="accountType"
                      className="w-full bg-dark border border-border rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                      value={paymentInfo.accountType}
                      onChange={(e) => setPaymentInfo({ ...paymentInfo, accountType: e.target.value })}
                    >
                      <option value="personal">Personal</option>
                      <option value="agent">Agent</option>
                      <option value="merchant">Merchant</option>
                    </select>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? 'Saving...' : 'Save Payment Information'}
                  </button>
                </form>
              </motion.div>

              {/* Withdrawal Request */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
              >
                <h2 className="text-xl font-semibold text-light mb-4">Request Withdrawal</h2>
                <p className="text-text mb-4">
                  Minimum withdrawal amount is 1000 Taka
                </p>

                <form onSubmit={handleWithdrawalSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="withdrawalAmount" className="block text-sm font-medium text-light mb-1">
                      Amount (Taka)
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <CurrencyBangladeshiIcon className="h-5 w-5 text-text" />
                      </div>
                      <input
                        type="number"
                        id="withdrawalAmount"
                        min="1000"
                        step="1"
                        className="w-full bg-dark border border-border rounded-lg pl-10 px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors"
                        placeholder="1000"
                        value={withdrawalAmount}
                        onChange={(e) => setWithdrawalAmount(e.target.value)}
                        required
                      />
                    </div>
                    <p className="mt-1 text-sm text-text">
                      Available balance: ৳{affiliateData?.availableBalance || 0}
                    </p>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting || !paymentInfo.bkashNumber || parseFloat(withdrawalAmount || 0) < 1000 || parseFloat(withdrawalAmount || 0) > (affiliateData?.availableBalance || 0)}
                    className="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? 'Processing...' : 'Request Withdrawal'}
                  </button>
                </form>
              </motion.div>
            </div>

            {/* Withdrawal History */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-xl p-6"
            >
              <h2 className="text-xl font-semibold text-light mb-4">Withdrawal History</h2>

              {withdrawalHistory.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-border">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          bKash Number
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                          Transaction ID
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {withdrawalHistory.map((withdrawal) => (
                        <tr key={withdrawal.id} className="hover:bg-dark-light/30">
                          <td className="px-4 py-3 text-sm text-text">
                            {new Date(withdrawal.createdAt).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            ৳{withdrawal.amount}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            {withdrawal.paymentDetails?.bkashNumber || withdrawal.bkashNumber}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {getStatusBadge(withdrawal.status)}
                          </td>
                          <td className="px-4 py-3 text-sm text-light">
                            {withdrawal.transactionId || '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="bg-dark-light/50 rounded-lg p-8 max-w-md mx-auto">
                    <CurrencyBangladeshiIcon className="w-12 h-12 text-primary/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-light mb-2">No Withdrawal History</h3>
                    <p className="text-text mb-6">Your withdrawal history will appear here after you make a withdrawal request</p>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        )}
      </div>
    </UserLayout>
  )
}
