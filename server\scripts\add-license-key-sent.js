/**
 * <PERSON><PERSON><PERSON> to add license_key_sent column to payments table
 * Run with: node scripts/add-license-key-sent.js
 */

const { query } = require('../config/db');

async function addLicenseKeySentColumn() {
  try {
    console.log('Checking if license_key_sent column exists in payments table...');
    
    // Check if the column already exists
    const [columns] = await query(
      "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'payments' AND COLUMN_NAME = 'license_key_sent'",
      [process.env.DB_NAME || 'meta_master']
    );
    
    if (columns.length > 0) {
      console.log('license_key_sent column already exists in payments table');
      return;
    }
    
    console.log('Adding license_key_sent column to payments table...');
    
    // Add the column
    await query(
      'ALTER TABLE payments ADD COLUMN license_key_sent BOOLEAN DEFAULT FALSE'
    );
    
    console.log('license_key_sent column added successfully');
  } catch (error) {
    console.error('Error adding license_key_sent column:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
addLicenseKeySentColumn();
